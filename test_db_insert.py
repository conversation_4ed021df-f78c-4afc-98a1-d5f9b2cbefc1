#!/usr/bin/env python3
"""
测试数据库插入
"""

import os
import sys
import pymysql

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from medical_ocr.config.config import DB_CONFIG

def test_insert():
    try:
        conn = pymysql.connect(
            host=DB_CONFIG['host'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database=DB_CONFIG['database'],
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        
        with conn.cursor() as cursor:
            # 查看表结构
            print("查看medical_exam表结构:")
            cursor.execute("DESCRIBE medical_exam")
            for row in cursor:
                print(f"  {row['Field']}: {row['Type']} {row['Null']} {row['Key']} {row['Default']}")
            
            # 尝试插入数据
            print("\n尝试插入测试数据...")
            try:
                cursor.execute(
                    """INSERT INTO medical_exam 
                    (user_id, medical_date, exam_info, exam_diag, exam_type, hospital) 
                    VALUES (%s, %s, %s, %s, %s, %s)""",
                    (
                        1,  # 用户ID
                        '2025-04-22',  # 检查日期
                        'CT平扫+增强扫描\n胸部CT检查\n造影剂使用情况：使用碘对比剂',  # 检查方法
                        '肺部未见明显异常\n心脏大小正常\n纵隔结构清晰',  # 放射性诊断
                        1,  # 检查类型
                        '复旦肿瘤'  # 医院
                    )
                )
                
                exam_id = cursor.lastrowid
                conn.commit()
                print(f"✓ 插入成功，ID: {exam_id}")
                
                # 查询刚插入的数据
                cursor.execute("SELECT * FROM medical_exam WHERE exam_id = %s", (exam_id,))
                row = cursor.fetchone()
                if row:
                    print("插入的数据:")
                    for key, value in row.items():
                        print(f"  {key}: {value}")
                
            except Exception as e:
                print(f"✗ 插入失败: {e}")
                conn.rollback()
        
        conn.close()
        
    except Exception as e:
        print(f"数据库连接错误: {e}")

if __name__ == "__main__":
    test_insert()
