{"input_path": "D:\\dev\\medicalReport\\ocr\\source\\0529.jpg", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": true, "use_doc_unwarping": true}, "angle": 0}, "dt_polys": [[[78, 193], [475, 340], [443, 423], [46, 276]], [[76, 381], [877, 550], [852, 666], [51, 497]], [[75, 488], [821, 654], [795, 766], [49, 600]], [[80, 829], [347, 894], [329, 966], [62, 901]], [[73, 1023], [301, 1072], [287, 1138], [59, 1089]], [[1120, 1153], [1361, 1142], [1364, 1209], [1123, 1220]], [[80, 1188], [352, 1249], [337, 1316], [64, 1255]], [[76, 1295], [574, 1388], [563, 1445], [65, 1352]], [[1061, 1354], [1364, 1345], [1366, 1410], [1063, 1418]], [[998, 1364], [1039, 1364], [1039, 1408], [998, 1408]], [[75, 1451], [347, 1504], [334, 1569], [62, 1515]], [[77, 1557], [499, 1628], [491, 1678], [69, 1606]], [[1131, 1600], [1183, 1600], [1183, 1653], [1131, 1653]], [[1203, 1595], [1362, 1590], [1364, 1651], [1205, 1656]], [[78, 1707], [343, 1754], [332, 1813], [67, 1766]], [[77, 1809], [426, 1864], [418, 1913], [69, 1859]], [[1223, 1840], [1364, 1833], [1368, 1895], [1227, 1903]], [[79, 1966], [391, 2011], [382, 2068], [71, 2023]], [[75, 2069], [422, 2110], [416, 2159], [69, 2119]], [[1127, 2093], [1179, 2093], [1179, 2145], [1127, 2145]], [[1197, 2087], [1363, 2080], [1366, 2140], [1200, 2147]], [[76, 2224], [342, 2254], [336, 2312], [69, 2283]], [[76, 2327], [540, 2369], [535, 2419], [72, 2377]], [[1087, 2333], [1365, 2321], [1368, 2385], [1090, 2398]], [[76, 2482], [341, 2505], [336, 2562], [71, 2539]], [[1059, 2576], [1365, 2566], [1368, 2630], [1061, 2641]], [[79, 2589], [550, 2622], [547, 2669], [76, 2635]], [[1020, 2587], [1065, 2587], [1065, 2629], [1020, 2629]], [[80, 2742], [392, 2766], [387, 2824], [75, 2799]], [[1001, 2839], [1035, 2839], [1035, 2876], [1001, 2876]], [[1068, 2830], [1362, 2822], [1364, 2879], [1070, 2887]], [[79, 2849], [548, 2873], [546, 2922], [76, 2899]], [[73, 3006], [338, 3016], [336, 3075], [71, 3065]], [[1037, 3080], [1362, 3074], [1363, 3131], [1038, 3137]], [[975, 3095], [999, 3095], [999, 3130], [975, 3130]], [[74, 3109], [563, 3127], [561, 3176], [73, 3159]], [[74, 3265], [286, 3272], [284, 3331], [73, 3324]], [[1149, 3327], [1251, 3327], [1251, 3393], [1149, 3393]], [[1219, 3323], [1371, 3323], [1371, 3398], [1219, 3398]], [[76, 3372], [454, 3382], [453, 3428], [75, 3418]], [[74, 3529], [336, 3537], [334, 3589], [73, 3580]], [[1127, 3584], [1178, 3584], [1178, 3638], [1127, 3638]], [[1198, 3583], [1365, 3583], [1365, 3643], [1198, 3643]], [[79, 3636], [467, 3636], [467, 3675], [79, 3675]], [[75, 3781], [385, 3785], [384, 3837], [75, 3833]], [[1221, 3827], [1372, 3827], [1372, 3894], [1221, 3894]], [[75, 3883], [455, 3879], [456, 3923], [75, 3927]], [[73, 4032], [389, 4026], [390, 4078], [74, 4084]], [[1200, 4079], [1373, 4086], [1371, 4152], [1197, 4145]], [[77, 4129], [491, 4129], [491, 4174], [77, 4174]], [[75, 4282], [388, 4282], [388, 4332], [75, 4332]], [[1166, 4321], [1376, 4321], [1376, 4393], [1166, 4393]], [[75, 4374], [469, 4380], [468, 4424], [75, 4418]], [[72, 4521], [338, 4525], [337, 4583], [71, 4578]], [[1082, 4573], [1371, 4573], [1371, 4636], [1082, 4636]], [[74, 4622], [576, 4632], [575, 4676], [73, 4667]], [[73, 4773], [372, 4785], [370, 4841], [70, 4828]], [[1227, 4822], [1373, 4817], [1375, 4885], [1230, 4890]], [[72, 4873], [439, 4883], [438, 4931], [71, 4921]], [[71, 5022], [336, 5032], [334, 5089], [69, 5079]], [[1212, 5084], [1366, 5074], [1370, 5135], [1216, 5145]], [[73, 5119], [481, 5137], [478, 5187], [71, 5169]], [[71, 5274], [370, 5288], [368, 5340], [69, 5325]], [[1264, 5326], [1374, 5326], [1374, 5398], [1264, 5398]], [[1189, 5337], [1247, 5337], [1247, 5398], [1189, 5398]], [[75, 5370], [394, 5384], [392, 5433], [72, 5419]], [[73, 5520], [387, 5533], [385, 5590], [71, 5578]], [[1054, 5583], [1371, 5588], [1370, 5650], [1053, 5646]], [[74, 5622], [606, 5638], [605, 5682], [73, 5666]], [[73, 5773], [383, 5783], [381, 5840], [71, 5830]], [[1195, 5848], [1371, 5841], [1373, 5905], [1198, 5912]], [[71, 5872], [441, 5884], [439, 5934], [69, 5922]], [[77, 6027], [385, 6039], [383, 6092], [74, 6080]], [[1056, 6093], [1371, 6098], [1370, 6160], [1055, 6156]], [[74, 6128], [612, 6142], [610, 6186], [73, 6173]], [[74, 6283], [383, 6291], [382, 6343], [73, 6334]], [[1198, 6352], [1371, 6347], [1373, 6416], [1199, 6420]], [[74, 6384], [486, 6394], [485, 6438], [73, 6428]], [[70, 6537], [473, 6541], [472, 6596], [69, 6592]], [[1294, 6599], [1376, 6593], [1381, 6665], [1300, 6671]], [[73, 6642], [437, 6642], [437, 6687], [73, 6687]], [[73, 6797], [472, 6797], [472, 6849], [73, 6849]], [[1212, 6855], [1372, 6848], [1375, 6916], [1215, 6923]], [[73, 6893], [465, 6893], [465, 6942], [73, 6942]], [[72, 7038], [388, 7042], [388, 7105], [71, 7100]], [[1244, 7094], [1374, 7089], [1377, 7161], [1247, 7166]], [[67, 7135], [270, 7140], [269, 7204], [65, 7200]]], "text_det_params": {"limit_side_len": 64, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.0, "rec_texts": ["*春香***岁", "申请科室：胰腺肿瘤临床研究门诊", "报告日期：2025-5-290:00:00", "项目明细", "项目名称", "结果/单位", "白细胞计数", "参考值：3.5-9.5*10^9/L", "15.4*10^9/L", "↑", "淋巴细胞%", "参考值：20.0-50.0%", "", "15.0%", "单核细胞%", "参考值：3.0-10%", "4.4%", "中性粒细胞%", "参考值：40-75%", "个", "79.9%", "淋巴细胞数", "参考值：1.1-3.2*10^9/L", "2.3*10^9/L", "单核细胞数", "0.7*10^9/L", "参考值：0.1-0.6*10^9/L", "↑", "中性粒细胞数", "↑", "12.3*10^9/L", "参考值：1.8-6.3*10^9/", "红细胞计数", "3.43*10^12/L", "↓", "参考值：3.8-5.1*10^12/", "血红蛋白", "$个", "95g/l", "参考值：115-150g/", "红细胞压积", "↑", "29.8%", "参考值：女35-45%", "平均RBC体积", "86.9fl", "参考值：82.0-100fl", "RBC平均HGB", "27.7pg", "参考值：27.0-34.0pd", "平均HGB浓度", "319.0g/l", "参考值：316-354g/", "血小板计数", "183*10^9/L", "参考值：125-350*10^9/L", "平均PLT容积", "10.0fl", "参考值：7.4-10.4fl", "血小板压积", "0.18%", "参考值：0.15-0.30%", "PLT分布宽度", "11%", "山", "参考值：13-21%", "嗜酸性细胞数", "0.06*10^9/L", "参考值：0.02-0.52*10^9/L", "嗜酸性细胞%", "0.40%", "参考值：0.4-8.0%", "嗜碱性细胞数", "0.04*10^9/L", "参考值：0.00-0.06*10^9/L", "嗜碱性细胞%", "0.30%", "参考值：0.00-1.00%", "RBC分布宽度-SD", "41", "参考值：35-44FL", "RBC分布宽度-CV", "13.2%", "参考值：11.6-14.4%", "中性淋巴比值", "5.35", "参考值：."], "rec_scores": [0.9222445487976074, 0.9979954361915588, 0.9885157346725464, 0.9995241165161133, 0.999747097492218, 0.9977256655693054, 0.9990183115005493, 0.9513990879058838, 0.9757556915283203, 0.8173618316650391, 0.9984426498413086, 0.9959691762924194, 0.0, 0.9986557960510254, 0.9997633099555969, 0.9974477291107178, 0.9994570016860962, 0.9988589286804199, 0.9960671663284302, 0.9173786640167236, 0.9976764917373657, 0.9991382360458374, 0.936833381652832, 0.9839061498641968, 0.999889075756073, 0.9271252751350403, 0.9565221071243286, 0.9645429849624634, 0.9993166327476501, 0.9345273375511169, 0.9650264978408813, 0.9665566682815552, 0.9994457960128784, 0.9706912040710449, 0.9946308135986328, 0.9570042490959167, 0.9995545744895935, 0.6126527786254883, 0.9798450469970703, 0.9922552108764648, 0.9996598958969116, 0.7120522856712341, 0.9992589950561523, 0.9843664765357971, 0.999588131904602, 0.9852078557014465, 0.9890308380126953, 0.9998363256454468, 0.9995281100273132, 0.9861173629760742, 0.9993222951889038, 0.9731622934341431, 0.9729052782058716, 0.9994171261787415, 0.9500572085380554, 0.9727445244789124, 0.9993665814399719, 0.9714213013648987, 0.9930576086044312, 0.9995478391647339, 0.9992667436599731, 0.9905967116355896, 0.9982447028160095, 0.9995151162147522, 0.28451237082481384, 0.9941199421882629, 0.9997715353965759, 0.9820230603218079, 0.9501541256904602, 0.998104989528656, 0.9988377690315247, 0.9938535690307617, 0.9995880722999573, 0.9764155745506287, 0.952511191368103, 0.9986679553985596, 0.9994109869003296, 0.9940363168716431, 0.9983833432197571, 0.9997367858886719, 0.9924856424331665, 0.9950807690620422, 0.9996109008789062, 0.9950351715087891, 0.998545229434967, 0.9997276663780212, 0.9651010632514954], "rec_polys": [[[78, 193], [475, 340], [443, 423], [46, 276]], [[76, 381], [877, 550], [852, 666], [51, 497]], [[75, 488], [821, 654], [795, 766], [49, 600]], [[80, 829], [347, 894], [329, 966], [62, 901]], [[73, 1023], [301, 1072], [287, 1138], [59, 1089]], [[1120, 1153], [1361, 1142], [1364, 1209], [1123, 1220]], [[80, 1188], [352, 1249], [337, 1316], [64, 1255]], [[76, 1295], [574, 1388], [563, 1445], [65, 1352]], [[1061, 1354], [1364, 1345], [1366, 1410], [1063, 1418]], [[998, 1364], [1039, 1364], [1039, 1408], [998, 1408]], [[75, 1451], [347, 1504], [334, 1569], [62, 1515]], [[77, 1557], [499, 1628], [491, 1678], [69, 1606]], [[1131, 1600], [1183, 1600], [1183, 1653], [1131, 1653]], [[1203, 1595], [1362, 1590], [1364, 1651], [1205, 1656]], [[78, 1707], [343, 1754], [332, 1813], [67, 1766]], [[77, 1809], [426, 1864], [418, 1913], [69, 1859]], [[1223, 1840], [1364, 1833], [1368, 1895], [1227, 1903]], [[79, 1966], [391, 2011], [382, 2068], [71, 2023]], [[75, 2069], [422, 2110], [416, 2159], [69, 2119]], [[1127, 2093], [1179, 2093], [1179, 2145], [1127, 2145]], [[1197, 2087], [1363, 2080], [1366, 2140], [1200, 2147]], [[76, 2224], [342, 2254], [336, 2312], [69, 2283]], [[76, 2327], [540, 2369], [535, 2419], [72, 2377]], [[1087, 2333], [1365, 2321], [1368, 2385], [1090, 2398]], [[76, 2482], [341, 2505], [336, 2562], [71, 2539]], [[1059, 2576], [1365, 2566], [1368, 2630], [1061, 2641]], [[79, 2589], [550, 2622], [547, 2669], [76, 2635]], [[1020, 2587], [1065, 2587], [1065, 2629], [1020, 2629]], [[80, 2742], [392, 2766], [387, 2824], [75, 2799]], [[1001, 2839], [1035, 2839], [1035, 2876], [1001, 2876]], [[1068, 2830], [1362, 2822], [1364, 2879], [1070, 2887]], [[79, 2849], [548, 2873], [546, 2922], [76, 2899]], [[73, 3006], [338, 3016], [336, 3075], [71, 3065]], [[1037, 3080], [1362, 3074], [1363, 3131], [1038, 3137]], [[975, 3095], [999, 3095], [999, 3130], [975, 3130]], [[74, 3109], [563, 3127], [561, 3176], [73, 3159]], [[74, 3265], [286, 3272], [284, 3331], [73, 3324]], [[1149, 3327], [1251, 3327], [1251, 3393], [1149, 3393]], [[1219, 3323], [1371, 3323], [1371, 3398], [1219, 3398]], [[76, 3372], [454, 3382], [453, 3428], [75, 3418]], [[74, 3529], [336, 3537], [334, 3589], [73, 3580]], [[1127, 3584], [1178, 3584], [1178, 3638], [1127, 3638]], [[1198, 3583], [1365, 3583], [1365, 3643], [1198, 3643]], [[79, 3636], [467, 3636], [467, 3675], [79, 3675]], [[75, 3781], [385, 3785], [384, 3837], [75, 3833]], [[1221, 3827], [1372, 3827], [1372, 3894], [1221, 3894]], [[75, 3883], [455, 3879], [456, 3923], [75, 3927]], [[73, 4032], [389, 4026], [390, 4078], [74, 4084]], [[1200, 4079], [1373, 4086], [1371, 4152], [1197, 4145]], [[77, 4129], [491, 4129], [491, 4174], [77, 4174]], [[75, 4282], [388, 4282], [388, 4332], [75, 4332]], [[1166, 4321], [1376, 4321], [1376, 4393], [1166, 4393]], [[75, 4374], [469, 4380], [468, 4424], [75, 4418]], [[72, 4521], [338, 4525], [337, 4583], [71, 4578]], [[1082, 4573], [1371, 4573], [1371, 4636], [1082, 4636]], [[74, 4622], [576, 4632], [575, 4676], [73, 4667]], [[73, 4773], [372, 4785], [370, 4841], [70, 4828]], [[1227, 4822], [1373, 4817], [1375, 4885], [1230, 4890]], [[72, 4873], [439, 4883], [438, 4931], [71, 4921]], [[71, 5022], [336, 5032], [334, 5089], [69, 5079]], [[1212, 5084], [1366, 5074], [1370, 5135], [1216, 5145]], [[73, 5119], [481, 5137], [478, 5187], [71, 5169]], [[71, 5274], [370, 5288], [368, 5340], [69, 5325]], [[1264, 5326], [1374, 5326], [1374, 5398], [1264, 5398]], [[1189, 5337], [1247, 5337], [1247, 5398], [1189, 5398]], [[75, 5370], [394, 5384], [392, 5433], [72, 5419]], [[73, 5520], [387, 5533], [385, 5590], [71, 5578]], [[1054, 5583], [1371, 5588], [1370, 5650], [1053, 5646]], [[74, 5622], [606, 5638], [605, 5682], [73, 5666]], [[73, 5773], [383, 5783], [381, 5840], [71, 5830]], [[1195, 5848], [1371, 5841], [1373, 5905], [1198, 5912]], [[71, 5872], [441, 5884], [439, 5934], [69, 5922]], [[77, 6027], [385, 6039], [383, 6092], [74, 6080]], [[1056, 6093], [1371, 6098], [1370, 6160], [1055, 6156]], [[74, 6128], [612, 6142], [610, 6186], [73, 6173]], [[74, 6283], [383, 6291], [382, 6343], [73, 6334]], [[1198, 6352], [1371, 6347], [1373, 6416], [1199, 6420]], [[74, 6384], [486, 6394], [485, 6438], [73, 6428]], [[70, 6537], [473, 6541], [472, 6596], [69, 6592]], [[1294, 6599], [1376, 6593], [1381, 6665], [1300, 6671]], [[73, 6642], [437, 6642], [437, 6687], [73, 6687]], [[73, 6797], [472, 6797], [472, 6849], [73, 6849]], [[1212, 6855], [1372, 6848], [1375, 6916], [1215, 6923]], [[73, 6893], [465, 6893], [465, 6942], [73, 6942]], [[72, 7038], [388, 7042], [388, 7105], [71, 7100]], [[1244, 7094], [1374, 7089], [1377, 7161], [1247, 7166]], [[67, 7135], [270, 7140], [269, 7204], [65, 7200]]], "rec_boxes": [[46, 193, 475, 423], [51, 381, 877, 666], [49, 488, 821, 766], [62, 829, 347, 966], [59, 1023, 301, 1138], [1120, 1142, 1364, 1220], [64, 1188, 352, 1316], [65, 1295, 574, 1445], [1061, 1345, 1366, 1418], [998, 1364, 1039, 1408], [62, 1451, 347, 1569], [69, 1557, 499, 1678], [1131, 1600, 1183, 1653], [1203, 1590, 1364, 1656], [67, 1707, 343, 1813], [69, 1809, 426, 1913], [1223, 1833, 1368, 1903], [71, 1966, 391, 2068], [69, 2069, 422, 2159], [1127, 2093, 1179, 2145], [1197, 2080, 1366, 2147], [69, 2224, 342, 2312], [72, 2327, 540, 2419], [1087, 2321, 1368, 2398], [71, 2482, 341, 2562], [1059, 2566, 1368, 2641], [76, 2589, 550, 2669], [1020, 2587, 1065, 2629], [75, 2742, 392, 2824], [1001, 2839, 1035, 2876], [1068, 2822, 1364, 2887], [76, 2849, 548, 2922], [71, 3006, 338, 3075], [1037, 3074, 1363, 3137], [975, 3095, 999, 3130], [73, 3109, 563, 3176], [73, 3265, 286, 3331], [1149, 3327, 1251, 3393], [1219, 3323, 1371, 3398], [75, 3372, 454, 3428], [73, 3529, 336, 3589], [1127, 3584, 1178, 3638], [1198, 3583, 1365, 3643], [79, 3636, 467, 3675], [75, 3781, 385, 3837], [1221, 3827, 1372, 3894], [75, 3879, 456, 3927], [73, 4026, 390, 4084], [1197, 4079, 1373, 4152], [77, 4129, 491, 4174], [75, 4282, 388, 4332], [1166, 4321, 1376, 4393], [75, 4374, 469, 4424], [71, 4521, 338, 4583], [1082, 4573, 1371, 4636], [73, 4622, 576, 4676], [70, 4773, 372, 4841], [1227, 4817, 1375, 4890], [71, 4873, 439, 4931], [69, 5022, 336, 5089], [1212, 5074, 1370, 5145], [71, 5119, 481, 5187], [69, 5274, 370, 5340], [1264, 5326, 1374, 5398], [1189, 5337, 1247, 5398], [72, 5370, 394, 5433], [71, 5520, 387, 5590], [1053, 5583, 1371, 5650], [73, 5622, 606, 5682], [71, 5773, 383, 5840], [1195, 5841, 1373, 5912], [69, 5872, 441, 5934], [74, 6027, 385, 6092], [1055, 6093, 1371, 6160], [73, 6128, 612, 6186], [73, 6283, 383, 6343], [1198, 6347, 1373, 6420], [73, 6384, 486, 6438], [69, 6537, 473, 6596], [1294, 6593, 1381, 6671], [73, 6642, 437, 6687], [73, 6797, 472, 6849], [1212, 6848, 1375, 6923], [73, 6893, 465, 6942], [71, 7038, 388, 7105], [1244, 7089, 1377, 7166], [65, 7135, 270, 7204]]}