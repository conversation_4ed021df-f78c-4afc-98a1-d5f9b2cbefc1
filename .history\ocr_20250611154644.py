from paddleocr import PaddleOCR
import pymysql

# ocr = PaddleOCR(
#     use_doc_orientation_classify=False, # 通过 use_doc_orientation_classify 参数指定不使用文档方向分类模型
#     use_doc_unwarping=False, # 通过 use_doc_unwarping 参数指定不使用文本图像矫正模型
#     use_textline_orientation=False, # 通过 use_textline_orientation 参数指定不使用文本行方向分类模型
# )
# ocr = PaddleOCR(lang="en") # 通过 lang 参数来使用英文模型
# ocr = PaddleOCR(ocr_version="PP-OCRv4") # 通过 ocr_version 参数来使用 PP-OCR 其他版本
# ocr = PaddleOCR(device="gpu") # 通过 device 参数使得在模型推理时使用 GPU
# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'your_password',
    'database': 'medical_db',
    'charset': 'utf8mb4'
}

# 创建表（如果不存在）
conn = pymysql.connect(**DB_CONFIG)
try:
    with conn.cursor() as cursor:
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS medical_check_detail (
                id INT AUTO_INCREMENT PRIMARY KEY,
                index_name VARCHAR(255),
                index_value VARCHAR(50),
                reference_value VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
    conn.commit()
finally:
    conn.close()

ocr = PaddleOCR(
    text_detection_model_name="PP-OCRv5_server_det",
    text_recognition_model_name="PP-OCRv5_server_rec",
    use_doc_orientation_classify=False,
    use_doc_unwarping=True,
    use_textline_orientation=False,
) # 更换 PP-OCRv5_server 模型
result = ocr.predict("./20250521143205.jpg")
for res in result:
    res.save_to_json("output")