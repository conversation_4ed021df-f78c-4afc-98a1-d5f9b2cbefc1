{"input_path": "D:\\dev\\medicalReport\\ocr\\source\\20250620083741.jpg", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[72, 46], [209, 46], [209, 107], [72, 107]], [[900, 46], [990, 46], [990, 85], [900, 85]], [[229, 56], [288, 56], [288, 95], [229, 95]], [[1160, 50], [1240, 50], [1240, 99], [1160, 99]], [[1270, 54], [1350, 54], [1350, 107], [1270, 107]], [[906, 73], [982, 73], [982, 109], [906, 109]], [[591, 179], [843, 179], [843, 250], [591, 250]], [[33, 208], [110, 208], [110, 282], [33, 282]], [[514, 260], [919, 264], [918, 313], [513, 308]], [[26, 353], [296, 349], [297, 405], [27, 410]], [[78, 650], [459, 662], [456, 747], [75, 735]], [[96, 849], [841, 849], [841, 897], [96, 897]], [[92, 948], [775, 950], [775, 998], [92, 996]], [[86, 1226], [340, 1226], [340, 1296], [86, 1296]], [[81, 1408], [298, 1403], [299, 1467], [83, 1472]], [[1121, 1409], [1350, 1409], [1350, 1466], [1121, 1466]], [[86, 1559], [297, 1559], [297, 1615], [86, 1615]], [[1125, 1597], [1356, 1597], [1356, 1661], [1125, 1661]], [[88, 1649], [520, 1653], [519, 1702], [88, 1698]], [[90, 1796], [346, 1796], [346, 1849], [90, 1849]], [[1129, 1831], [1356, 1831], [1356, 1893], [1129, 1893]], [[92, 1889], [448, 1889], [448, 1930], [92, 1930]], [[90, 2028], [346, 2028], [346, 2081], [90, 2081]], [[1160, 2065], [1352, 2065], [1352, 2123], [1160, 2123]], [[94, 2125], [477, 2125], [477, 2159], [94, 2159]], [[90, 2260], [340, 2260], [340, 2311], [90, 2311]], [[1190, 2299], [1354, 2299], [1354, 2357], [1190, 2357]], [[90, 2355], [425, 2355], [425, 2397], [90, 2397]], [[90, 2496], [346, 2496], [346, 2549], [90, 2549]], [[1168, 2532], [1352, 2532], [1352, 2591], [1168, 2591]], [[92, 2587], [450, 2587], [450, 2629], [92, 2629]], [[88, 2730], [344, 2730], [344, 2778], [88, 2778]], [[1166, 2762], [1354, 2762], [1354, 2827], [1166, 2827]], [[92, 2823], [503, 2823], [503, 2863], [92, 2863]], [[89, 2958], [395, 2962], [394, 3017], [88, 3012]], [[1211, 2998], [1354, 2998], [1354, 3059], [1211, 3059]], [[92, 3057], [425, 3057], [425, 3097], [92, 3097]], [[86, 3192], [245, 3192], [245, 3250], [86, 3250]], [[1174, 3230], [1354, 3230], [1354, 3297], [1174, 3297]], [[90, 3284], [438, 3284], [438, 3333], [90, 3333]], [[86, 3422], [247, 3422], [247, 3486], [86, 3486]], [[1176, 3455], [1361, 3463], [1358, 3535], [1173, 3528]], [[88, 3516], [477, 3521], [476, 3569], [88, 3565]], [[84, 3657], [200, 3657], [200, 3720], [84, 3720]], [[1096, 3704], [1352, 3704], [1352, 3756], [1096, 3756]], [[1029, 3716], [1058, 3716], [1058, 3748], [1029, 3748]], [[92, 3754], [524, 3754], [524, 3797], [92, 3797]], [[84, 3893], [200, 3893], [200, 3954], [84, 3954]], [[1150, 3934], [1354, 3934], [1354, 3990], [1150, 3990]], [[92, 3990], [485, 3990], [485, 4030], [92, 4030]], [[84, 4123], [202, 4123], [202, 4186], [84, 4186]], [[1123, 4168], [1350, 4168], [1350, 4218], [1123, 4218]], [[92, 4222], [540, 4222], [540, 4262], [92, 4262]], [[84, 4355], [250, 4355], [250, 4420], [84, 4420]], [[1082, 4401], [1354, 4401], [1354, 4456], [1082, 4456]], [[92, 4454], [526, 4454], [526, 4496], [92, 4496]], [[84, 4589], [151, 4589], [151, 4654], [84, 4654]], [[1084, 4633], [1352, 4633], [1352, 4688], [1084, 4688]], [[92, 4686], [562, 4686], [562, 4728], [92, 4728]], [[84, 4823], [151, 4823], [151, 4889], [84, 4889]], [[1078, 4871], [1348, 4871], [1348, 4920], [1078, 4920]], [[92, 4922], [583, 4922], [583, 4962], [92, 4962]], [[84, 5057], [149, 5057], [149, 5119], [84, 5119]], [[1070, 5105], [1347, 5101], [1348, 5151], [1070, 5156]], [[92, 5156], [546, 5156], [546, 5198], [92, 5198]], [[84, 5291], [149, 5291], [149, 5353], [84, 5353]], [[1045, 5339], [1350, 5339], [1350, 5387], [1045, 5387]], [[92, 5387], [556, 5387], [556, 5428], [92, 5428]], [[84, 5523], [151, 5523], [151, 5587], [84, 5587]], [[1045, 5569], [1350, 5569], [1350, 5623], [1045, 5623]], [[92, 5621], [548, 5621], [548, 5664], [92, 5664]], [[90, 5760], [342, 5760], [342, 5811], [90, 5811]], [[1002, 5807], [1049, 5807], [1049, 5855], [1002, 5855]], [[1074, 5801], [1350, 5801], [1350, 5857], [1074, 5857]], [[92, 5855], [560, 5855], [560, 5896], [92, 5896]], [[82, 5990], [151, 5990], [151, 6055], [82, 6055]], [[1061, 6039], [1349, 6032], [1350, 6089], [1063, 6096]], [[92, 6089], [587, 6089], [587, 6129], [92, 6129]], [[85, 6229], [294, 6224], [295, 6280], [87, 6285]], [[1068, 6275], [1115, 6275], [1115, 6321], [1068, 6321]], [[1137, 6269], [1350, 6269], [1350, 6327], [1137, 6327]], [[88, 6317], [540, 6323], [540, 6372], [88, 6365]], [[86, 6458], [245, 6458], [245, 6517], [86, 6517]], [[1174, 6496], [1358, 6496], [1358, 6569], [1174, 6569]], [[92, 6555], [505, 6555], [505, 6597], [92, 6597]], [[90, 6694], [295, 6694], [295, 6750], [90, 6750]], [[1246, 6732], [1356, 6732], [1356, 6795], [1246, 6795]], [[92, 6789], [397, 6789], [397, 6831], [92, 6831]], [[90, 6932], [343, 6928], [344, 6978], [91, 6983]], [[1116, 6971], [1351, 6964], [1353, 7022], [1118, 7029]], [[92, 7025], [479, 7025], [479, 7065], [92, 7065]], [[90, 7162], [346, 7162], [346, 7214], [90, 7214]], [[1197, 7196], [1356, 7196], [1356, 7263], [1197, 7263]], [[92, 7256], [491, 7256], [491, 7299], [92, 7299]], [[88, 7396], [346, 7396], [346, 7452], [88, 7452]], [[1027, 7436], [1347, 7432], [1348, 7488], [1027, 7493]], [[998, 7448], [1039, 7448], [1039, 7486], [998, 7486]], [[92, 7488], [567, 7488], [567, 7531], [92, 7531]], [[86, 7625], [395, 7630], [394, 7686], [86, 7682]], [[1184, 7664], [1354, 7664], [1354, 7730], [1184, 7730]], [[90, 7720], [456, 7720], [456, 7769], [90, 7769]]], "text_det_params": {"limit_side_len": 64, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.6, "rec_texts": ["21:19", "0.90", "头条", "5G", "59", "KB/S", "报告查询", "×", "fwcs.linkingcloud.cn", "东院生化检测", "*春香***岁", "申请科室：胰腺肿瘤临床研究门诊", "报告日期：2025-6-180:00:00", "项目明细", "项目名称", "结果/单位", "总胆红素", "3.3umol/l", "参考值：女≤21umol/l", "直接胆红素", "2.3umol/l", "参考值：≤8umol/l", "碱性磷酸酶", "74.0U/L", "参考值：50-135U/L", "谷丙转氨酶", "17.2U/I", "参考值：7-40U/I", "谷草转氨酶", "21.8U/L", "参考值：13-35U/L", "乳酸脱氢酶", "200U/L", "参考值：120-250U/L", "谷氨酰转肽酶", "17U/L", "参考值：7-45U/L", "总蛋白", "69.0g/l", "参考值：65-85g/l", "白蛋白", "43.0g/l", "参考值：40-55.0g/l", "尿素", "2.19mmol/l", "↓", "参考值：3.1-8.8mmol/l", "肌酐", "61umol/I", "参考值：41-81umol/l", "尿酸", "216umol/l", "参考值：155-357umol/", "葡萄糖", "5.64mmol/l", "参考值：3.9-6.1mmol/l", "钙", "2.28mmol/l", "参考值：2.11-2.52mmol/l", "磷", "1.05mmol/L", "参考值：0.85-1.51mmol/L", "钾", "3.97mmolL", "参考值：3.5-5.3mmol/L", "钠", "140.0mmol/L", "参考值：137-147mmol/L", "氯", "106.0mmol/L", "参考值：96-108mmol/L", "总二氧化碳", "↓", "21.9mmol/L", "参考值：23-29.0mmol/L", "镁", "0.84mmol/L", "参考值：0.75-1.02mmol/L", "前白蛋白", "↓", "196mg/L", "参考值：250-400mg/L", "球蛋白", "26.0g/l", "参考值：20.0-40.0g/", "白球比例", "1.65", "参考值：1.2-2.4", "间接胆红素", "1.0umol/L", "参考值：0-12umol/L", "谷草同工酶", "<4U/L", "参考值：0.0-15.0U/L", "游离脂肪酸", "0.67mmol/L", "↑", "参考值：0.1-0.45mmol/L", "谷氨酸脱氢酶", "2.2U/L", "参考值：女0-5U/L"], "rec_scores": [0.9598345756530762, 0.9997524619102478, 0.9981105327606201, 0.9820412993431091, 0.9986110925674438, 0.9524692296981812, 0.9997634291648865, 0.8398107290267944, 0.9996606111526489, 0.999188244342804, 0.9446134567260742, 0.9869699478149414, 0.9810495972633362, 0.9995790719985962, 0.9999403953552246, 0.9952923655509949, 0.9994716048240662, 0.9577854871749878, 0.9696894884109497, 0.9997276067733765, 0.9638314843177795, 0.9787083268165588, 0.99864262342453, 0.9971185326576233, 0.9887187480926514, 0.9983903765678406, 0.984294056892395, 0.9693096280097961, 0.9980289340019226, 0.9974040985107422, 0.9860472679138184, 0.9972939491271973, 0.9971733093261719, 0.986027181148529, 0.9977248311042786, 0.9978435635566711, 0.9831753373146057, 0.9993122220039368, 0.9876155853271484, 0.9264547228813171, 0.9995393753051758, 0.9774982333183289, 0.988993763923645, 0.999901533126831, 0.9657787084579468, 0.991456925868988, 0.9870957732200623, 0.9710162878036499, 0.9371435642242432, 0.9578751921653748, 0.9997689723968506, 0.9678521752357483, 0.9916718006134033, 0.9999387860298157, 0.9746222496032715, 0.9911500215530396, 0.9994823932647705, 0.9733530282974243, 0.967087984085083, 0.9998006224632263, 0.9900668859481812, 0.9840356707572937, 0.9998395442962646, 0.9777048826217651, 0.9912218451499939, 0.9769619703292847, 0.9933643341064453, 0.993871808052063, 0.9997155070304871, 0.9972188472747803, 0.9944794178009033, 0.9936798810958862, 0.6973071694374084, 0.9969425201416016, 0.9815287590026855, 0.999402642250061, 0.9915066957473755, 0.9850718975067139, 0.9994085431098938, 0.9591928720474243, 0.9976877570152283, 0.9933580756187439, 0.9991448521614075, 0.9897063374519348, 0.9933230876922607, 0.9994384050369263, 0.9997953176498413, 0.9924108386039734, 0.9991171956062317, 0.9950844645500183, 0.98407381772995, 0.9971979260444641, 0.9892820119857788, 0.993784487247467, 0.9956819415092468, 0.995720386505127, 0.9750049114227295, 0.9936863780021667, 0.9916756749153137, 0.9957759976387024, 0.9933982491493225], "rec_polys": [[[72, 46], [209, 46], [209, 107], [72, 107]], [[900, 46], [990, 46], [990, 85], [900, 85]], [[229, 56], [288, 56], [288, 95], [229, 95]], [[1160, 50], [1240, 50], [1240, 99], [1160, 99]], [[1270, 54], [1350, 54], [1350, 107], [1270, 107]], [[906, 73], [982, 73], [982, 109], [906, 109]], [[591, 179], [843, 179], [843, 250], [591, 250]], [[33, 208], [110, 208], [110, 282], [33, 282]], [[514, 260], [919, 264], [918, 313], [513, 308]], [[26, 353], [296, 349], [297, 405], [27, 410]], [[78, 650], [459, 662], [456, 747], [75, 735]], [[96, 849], [841, 849], [841, 897], [96, 897]], [[92, 948], [775, 950], [775, 998], [92, 996]], [[86, 1226], [340, 1226], [340, 1296], [86, 1296]], [[81, 1408], [298, 1403], [299, 1467], [83, 1472]], [[1121, 1409], [1350, 1409], [1350, 1466], [1121, 1466]], [[86, 1559], [297, 1559], [297, 1615], [86, 1615]], [[1125, 1597], [1356, 1597], [1356, 1661], [1125, 1661]], [[88, 1649], [520, 1653], [519, 1702], [88, 1698]], [[90, 1796], [346, 1796], [346, 1849], [90, 1849]], [[1129, 1831], [1356, 1831], [1356, 1893], [1129, 1893]], [[92, 1889], [448, 1889], [448, 1930], [92, 1930]], [[90, 2028], [346, 2028], [346, 2081], [90, 2081]], [[1160, 2065], [1352, 2065], [1352, 2123], [1160, 2123]], [[94, 2125], [477, 2125], [477, 2159], [94, 2159]], [[90, 2260], [340, 2260], [340, 2311], [90, 2311]], [[1190, 2299], [1354, 2299], [1354, 2357], [1190, 2357]], [[90, 2355], [425, 2355], [425, 2397], [90, 2397]], [[90, 2496], [346, 2496], [346, 2549], [90, 2549]], [[1168, 2532], [1352, 2532], [1352, 2591], [1168, 2591]], [[92, 2587], [450, 2587], [450, 2629], [92, 2629]], [[88, 2730], [344, 2730], [344, 2778], [88, 2778]], [[1166, 2762], [1354, 2762], [1354, 2827], [1166, 2827]], [[92, 2823], [503, 2823], [503, 2863], [92, 2863]], [[89, 2958], [395, 2962], [394, 3017], [88, 3012]], [[1211, 2998], [1354, 2998], [1354, 3059], [1211, 3059]], [[92, 3057], [425, 3057], [425, 3097], [92, 3097]], [[86, 3192], [245, 3192], [245, 3250], [86, 3250]], [[1174, 3230], [1354, 3230], [1354, 3297], [1174, 3297]], [[90, 3284], [438, 3284], [438, 3333], [90, 3333]], [[86, 3422], [247, 3422], [247, 3486], [86, 3486]], [[1176, 3455], [1361, 3463], [1358, 3535], [1173, 3528]], [[88, 3516], [477, 3521], [476, 3569], [88, 3565]], [[84, 3657], [200, 3657], [200, 3720], [84, 3720]], [[1096, 3704], [1352, 3704], [1352, 3756], [1096, 3756]], [[1029, 3716], [1058, 3716], [1058, 3748], [1029, 3748]], [[92, 3754], [524, 3754], [524, 3797], [92, 3797]], [[84, 3893], [200, 3893], [200, 3954], [84, 3954]], [[1150, 3934], [1354, 3934], [1354, 3990], [1150, 3990]], [[92, 3990], [485, 3990], [485, 4030], [92, 4030]], [[84, 4123], [202, 4123], [202, 4186], [84, 4186]], [[1123, 4168], [1350, 4168], [1350, 4218], [1123, 4218]], [[92, 4222], [540, 4222], [540, 4262], [92, 4262]], [[84, 4355], [250, 4355], [250, 4420], [84, 4420]], [[1082, 4401], [1354, 4401], [1354, 4456], [1082, 4456]], [[92, 4454], [526, 4454], [526, 4496], [92, 4496]], [[84, 4589], [151, 4589], [151, 4654], [84, 4654]], [[1084, 4633], [1352, 4633], [1352, 4688], [1084, 4688]], [[92, 4686], [562, 4686], [562, 4728], [92, 4728]], [[84, 4823], [151, 4823], [151, 4889], [84, 4889]], [[1078, 4871], [1348, 4871], [1348, 4920], [1078, 4920]], [[92, 4922], [583, 4922], [583, 4962], [92, 4962]], [[84, 5057], [149, 5057], [149, 5119], [84, 5119]], [[1070, 5105], [1347, 5101], [1348, 5151], [1070, 5156]], [[92, 5156], [546, 5156], [546, 5198], [92, 5198]], [[84, 5291], [149, 5291], [149, 5353], [84, 5353]], [[1045, 5339], [1350, 5339], [1350, 5387], [1045, 5387]], [[92, 5387], [556, 5387], [556, 5428], [92, 5428]], [[84, 5523], [151, 5523], [151, 5587], [84, 5587]], [[1045, 5569], [1350, 5569], [1350, 5623], [1045, 5623]], [[92, 5621], [548, 5621], [548, 5664], [92, 5664]], [[90, 5760], [342, 5760], [342, 5811], [90, 5811]], [[1002, 5807], [1049, 5807], [1049, 5855], [1002, 5855]], [[1074, 5801], [1350, 5801], [1350, 5857], [1074, 5857]], [[92, 5855], [560, 5855], [560, 5896], [92, 5896]], [[82, 5990], [151, 5990], [151, 6055], [82, 6055]], [[1061, 6039], [1349, 6032], [1350, 6089], [1063, 6096]], [[92, 6089], [587, 6089], [587, 6129], [92, 6129]], [[85, 6229], [294, 6224], [295, 6280], [87, 6285]], [[1068, 6275], [1115, 6275], [1115, 6321], [1068, 6321]], [[1137, 6269], [1350, 6269], [1350, 6327], [1137, 6327]], [[88, 6317], [540, 6323], [540, 6372], [88, 6365]], [[86, 6458], [245, 6458], [245, 6517], [86, 6517]], [[1174, 6496], [1358, 6496], [1358, 6569], [1174, 6569]], [[92, 6555], [505, 6555], [505, 6597], [92, 6597]], [[90, 6694], [295, 6694], [295, 6750], [90, 6750]], [[1246, 6732], [1356, 6732], [1356, 6795], [1246, 6795]], [[92, 6789], [397, 6789], [397, 6831], [92, 6831]], [[90, 6932], [343, 6928], [344, 6978], [91, 6983]], [[1116, 6971], [1351, 6964], [1353, 7022], [1118, 7029]], [[92, 7025], [479, 7025], [479, 7065], [92, 7065]], [[90, 7162], [346, 7162], [346, 7214], [90, 7214]], [[1197, 7196], [1356, 7196], [1356, 7263], [1197, 7263]], [[92, 7256], [491, 7256], [491, 7299], [92, 7299]], [[88, 7396], [346, 7396], [346, 7452], [88, 7452]], [[1027, 7436], [1347, 7432], [1348, 7488], [1027, 7493]], [[998, 7448], [1039, 7448], [1039, 7486], [998, 7486]], [[92, 7488], [567, 7488], [567, 7531], [92, 7531]], [[86, 7625], [395, 7630], [394, 7686], [86, 7682]], [[1184, 7664], [1354, 7664], [1354, 7730], [1184, 7730]], [[90, 7720], [456, 7720], [456, 7769], [90, 7769]]], "rec_boxes": [[72, 46, 209, 107], [900, 46, 990, 85], [229, 56, 288, 95], [1160, 50, 1240, 99], [1270, 54, 1350, 107], [906, 73, 982, 109], [591, 179, 843, 250], [33, 208, 110, 282], [513, 260, 919, 313], [26, 349, 297, 410], [75, 650, 459, 747], [96, 849, 841, 897], [92, 948, 775, 998], [86, 1226, 340, 1296], [81, 1403, 299, 1472], [1121, 1409, 1350, 1466], [86, 1559, 297, 1615], [1125, 1597, 1356, 1661], [88, 1649, 520, 1702], [90, 1796, 346, 1849], [1129, 1831, 1356, 1893], [92, 1889, 448, 1930], [90, 2028, 346, 2081], [1160, 2065, 1352, 2123], [94, 2125, 477, 2159], [90, 2260, 340, 2311], [1190, 2299, 1354, 2357], [90, 2355, 425, 2397], [90, 2496, 346, 2549], [1168, 2532, 1352, 2591], [92, 2587, 450, 2629], [88, 2730, 344, 2778], [1166, 2762, 1354, 2827], [92, 2823, 503, 2863], [88, 2958, 395, 3017], [1211, 2998, 1354, 3059], [92, 3057, 425, 3097], [86, 3192, 245, 3250], [1174, 3230, 1354, 3297], [90, 3284, 438, 3333], [86, 3422, 247, 3486], [1173, 3455, 1361, 3535], [88, 3516, 477, 3569], [84, 3657, 200, 3720], [1096, 3704, 1352, 3756], [1029, 3716, 1058, 3748], [92, 3754, 524, 3797], [84, 3893, 200, 3954], [1150, 3934, 1354, 3990], [92, 3990, 485, 4030], [84, 4123, 202, 4186], [1123, 4168, 1350, 4218], [92, 4222, 540, 4262], [84, 4355, 250, 4420], [1082, 4401, 1354, 4456], [92, 4454, 526, 4496], [84, 4589, 151, 4654], [1084, 4633, 1352, 4688], [92, 4686, 562, 4728], [84, 4823, 151, 4889], [1078, 4871, 1348, 4920], [92, 4922, 583, 4962], [84, 5057, 149, 5119], [1070, 5101, 1348, 5156], [92, 5156, 546, 5198], [84, 5291, 149, 5353], [1045, 5339, 1350, 5387], [92, 5387, 556, 5428], [84, 5523, 151, 5587], [1045, 5569, 1350, 5623], [92, 5621, 548, 5664], [90, 5760, 342, 5811], [1002, 5807, 1049, 5855], [1074, 5801, 1350, 5857], [92, 5855, 560, 5896], [82, 5990, 151, 6055], [1061, 6032, 1350, 6096], [92, 6089, 587, 6129], [85, 6224, 295, 6285], [1068, 6275, 1115, 6321], [1137, 6269, 1350, 6327], [88, 6317, 540, 6372], [86, 6458, 245, 6517], [1174, 6496, 1358, 6569], [92, 6555, 505, 6597], [90, 6694, 295, 6750], [1246, 6732, 1356, 6795], [92, 6789, 397, 6831], [90, 6928, 344, 6983], [1116, 6964, 1353, 7029], [92, 7025, 479, 7065], [90, 7162, 346, 7214], [1197, 7196, 1356, 7263], [92, 7256, 491, 7299], [88, 7396, 346, 7452], [1027, 7432, 1348, 7493], [998, 7448, 1039, 7486], [92, 7488, 567, 7531], [86, 7625, 395, 7686], [1184, 7664, 1354, 7730], [90, 7720, 456, 7769]]}