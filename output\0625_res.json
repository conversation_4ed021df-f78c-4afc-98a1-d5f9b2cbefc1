{"input_path": "D:\\dev\\medicalReport\\ocr\\source\\0625.jpg", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[838, 37], [1194, 37], [1194, 120], [838, 120]], [[77, 48], [227, 48], [227, 107], [77, 107]], [[1161, 48], [1251, 48], [1251, 107], [1161, 107]], [[1269, 53], [1346, 53], [1346, 103], [1269, 103]], [[589, 179], [846, 179], [846, 256], [589, 256]], [[34, 208], [111, 208], [111, 284], [34, 284]], [[518, 267], [919, 267], [919, 311], [518, 311]], [[32, 353], [291, 353], [291, 405], [32, 405]], [[79, 653], [456, 663], [454, 746], [76, 736]], [[94, 845], [842, 847], [842, 898], [94, 897]], [[92, 946], [786, 946], [786, 1002], [92, 1002]], [[86, 1226], [341, 1226], [341, 1296], [86, 1296]], [[84, 1409], [297, 1404], [299, 1463], [85, 1468]], [[1121, 1408], [1350, 1408], [1350, 1467], [1121, 1467]], [[94, 1561], [343, 1561], [343, 1613], [94, 1613]], [[992, 1607], [1035, 1607], [1035, 1655], [992, 1655]], [[1059, 1598], [1354, 1598], [1354, 1661], [1059, 1661]], [[90, 1653], [560, 1651], [561, 1696], [90, 1697]], [[90, 1795], [341, 1795], [341, 1847], [90, 1847]], [[1140, 1837], [1189, 1837], [1189, 1889], [1140, 1889]], [[1211, 1834], [1354, 1834], [1354, 1894], [1211, 1894]], [[90, 1885], [495, 1889], [495, 1933], [90, 1929]], [[92, 2029], [339, 2029], [339, 2080], [92, 2080]], [[1219, 2061], [1354, 2066], [1352, 2129], [1216, 2124]], [[92, 2123], [424, 2123], [424, 2161], [92, 2161]], [[94, 2263], [388, 2263], [388, 2312], [94, 2312]], [[1123, 2309], [1166, 2309], [1166, 2355], [1123, 2355]], [[1194, 2303], [1350, 2303], [1350, 2356], [1194, 2356]], [[92, 2356], [422, 2356], [422, 2395], [92, 2395]], [[92, 2496], [343, 2496], [343, 2548], [92, 2548]], [[1086, 2535], [1350, 2535], [1350, 2587], [1086, 2587]], [[90, 2587], [540, 2585], [540, 2629], [90, 2631]], [[92, 2730], [343, 2730], [343, 2780], [92, 2780]], [[1014, 2776], [1056, 2776], [1056, 2819], [1014, 2819]], [[1082, 2767], [1348, 2767], [1348, 2819], [1082, 2819]], [[90, 2820], [551, 2819], [551, 2863], [90, 2865]], [[96, 2966], [390, 2966], [390, 3010], [96, 3010]], [[994, 3010], [1028, 3010], [1028, 3051], [994, 3051]], [[1065, 3003], [1350, 3003], [1350, 3054], [1065, 3054]], [[94, 3056], [551, 3056], [551, 3095], [94, 3095]], [[90, 3196], [343, 3196], [343, 3248], [90, 3248]], [[1035, 3233], [1352, 3233], [1352, 3290], [1035, 3290]], [[966, 3246], [999, 3246], [999, 3284], [966, 3284]], [[92, 3288], [562, 3288], [562, 3330], [92, 3330]], [[90, 3428], [292, 3428], [292, 3479], [90, 3479]], [[1142, 3468], [1208, 3468], [1208, 3527], [1142, 3527]], [[1211, 3463], [1356, 3463], [1356, 3533], [1211, 3533]], [[1191, 3483], [1224, 3483], [1224, 3514], [1191, 3514]], [[90, 3522], [459, 3522], [459, 3566], [90, 3566]], [[90, 3664], [345, 3664], [345, 3713], [90, 3713]], [[1118, 3706], [1172, 3706], [1172, 3757], [1118, 3757]], [[1189, 3700], [1354, 3700], [1354, 3759], [1189, 3759]], [[94, 3757], [472, 3757], [472, 3794], [94, 3794]], [[92, 3894], [390, 3894], [390, 3943], [92, 3943]], [[1217, 3932], [1358, 3932], [1358, 3993], [1217, 3993]], [[92, 3989], [459, 3989], [459, 4026], [92, 4026]], [[90, 4133], [394, 4133], [394, 4177], [90, 4177]], [[1187, 4159], [1360, 4173], [1354, 4240], [1181, 4225]], [[88, 4219], [499, 4223], [498, 4268], [88, 4264]], [[92, 4365], [394, 4365], [394, 4409], [92, 4409]], [[1154, 4394], [1356, 4399], [1355, 4465], [1153, 4460]], [[1089, 4409], [1132, 4409], [1132, 4453], [1089, 4453]], [[94, 4457], [472, 4457], [472, 4496], [94, 4496]], [[90, 4597], [343, 4597], [343, 4647], [90, 4647]], [[1074, 4634], [1352, 4634], [1352, 4691], [1074, 4691]], [[94, 4691], [576, 4691], [576, 4728], [94, 4728]], [[90, 4831], [377, 4831], [377, 4880], [90, 4880]], [[1236, 4864], [1356, 4864], [1356, 4927], [1236, 4927]], [[90, 4919], [443, 4923], [442, 4967], [90, 4963]], [[90, 5063], [343, 5063], [343, 5114], [90, 5114]], [[1202, 5101], [1354, 5101], [1354, 5160], [1202, 5160]], [[88, 5153], [486, 5157], [485, 5201], [88, 5197]], [[88, 5297], [377, 5297], [377, 5346], [88, 5346]], [[1168, 5339], [1217, 5339], [1217, 5390], [1168, 5390]], [[1241, 5337], [1352, 5337], [1352, 5392], [1241, 5392]], [[92, 5392], [401, 5392], [401, 5429], [92, 5429]], [[92, 5529], [390, 5529], [390, 5578], [92, 5578]], [[1069, 5569], [1350, 5569], [1350, 5621], [1069, 5621]], [[90, 5622], [609, 5622], [609, 5665], [90, 5665]], [[92, 5764], [390, 5764], [390, 5814], [92, 5814]], [[1191, 5805], [1352, 5805], [1352, 5858], [1191, 5858]], [[92, 5858], [444, 5858], [444, 5897], [92, 5897]], [[94, 6000], [392, 6000], [392, 6044], [94, 6044]], [[1056, 6037], [1350, 6037], [1350, 6088], [1056, 6088]], [[92, 6092], [615, 6092], [615, 6129], [92, 6129]], [[92, 6230], [390, 6230], [390, 6280], [92, 6280]], [[1191, 6269], [1352, 6269], [1352, 6328], [1191, 6328]], [[92, 6326], [489, 6326], [489, 6364], [92, 6364]], [[88, 6464], [478, 6464], [478, 6513], [88, 6513]], [[1204, 6506], [1252, 6506], [1252, 6558], [1204, 6558]], [[1271, 6501], [1356, 6501], [1356, 6561], [1271, 6561]], [[90, 6556], [444, 6556], [444, 6600], [90, 6600]], [[88, 6699], [482, 6699], [482, 6749], [88, 6749]], [[1127, 6738], [1176, 6738], [1176, 6793], [1127, 6793]], [[1200, 6734], [1352, 6734], [1352, 6793], [1200, 6793]], [[90, 6788], [472, 6788], [472, 6832], [90, 6832]], [[92, 6930], [392, 6930], [392, 6981], [92, 6981]], [[1232, 6966], [1356, 6966], [1356, 7031], [1232, 7031]], [[88, 7018], [278, 7018], [278, 7071], [88, 7071]]], "text_det_params": {"limit_side_len": 64, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.6, "rec_texts": ["10:43", "5", "83", "报告查询", "X", "fwcs.linkingcloud.cn", "东院XN9000", "*春香***岁", "申请科室：胰腺肿瘤临床研究门诊", "报告日期：2025-6-250:00:00", "项目明细", "项目名称", "结果/单位", "白细胞计数", "13.8*10^9/L", "参考值：3.5-9.5*10^9/L", "淋巴细胞%", "18.1%", "参考值：20.0-50.0%", "单核细胞%", "4.9%", "参考值：3.0-10%", "中性粒细胞%", "↑", "75.9%", "参考值：40-75%", "淋巴细胞数", "2.5*10^9/L", "参考值：1.1-3.2*10^9/L", "单核细胞数", "↑", "0.7*10^9/L", "参考值：0.1-0.6*10^9/L", "中性粒细胞数", "↑", "10.5*10^9/L", "参考值：1.8-6.3*10^9/L", "红细胞计数", "3.05*10^12/L", "↑", "参考值：3.8-5.1*10^12/L", "血红蛋白", "个", "84g/l", "参考值：115-150g/", "红细胞压积", "26.8%", "参考值：女35-45%", "平均RBC体积", "87.9fl", "参考值：82.0-100f", "RBC平均HGB", "27.5pg", "参考值：27.0-34.0pg", "平均HGB浓度", "313.0g/l", "↑", "参考值：316-354g/", "血小板计数", "166*10^9/L", "参考值：125-350*10^9/L", "平均PLT容积", "9.9fl", "参考值：7.4-10.4fl", "血小板压积", "0.16%", "参考值：0.15-0.30%", "PLT分布宽度", "10%", "参考值：13-21%", "嗜酸性细胞数", "0.13*10^9/L", "参考值：0.02-0.52*10^9/L", "嗜酸性细胞%", "0.90%", "参考值：0.4-8.0%", "嗜碱性细胞数", "0.03*10^9/L", "参考值：0.00-0.06*10^9/L", "嗜碱性细胞%", "0.20%", "参考值：0.00-1.00%", "RBC分布宽度-SD", "个", "48", "参考值：35-44FL", "RBC分布宽度-CV", "个", "15.5%", "参考值：11.6-14.4%", "中性淋巴比值", "4.20", "参考值：."], "rec_scores": [0.9858198165893555, 0.617871880531311, 0.998506486415863, 0.9997503161430359, 0.6424153447151184, 0.9995588064193726, 0.9986861944198608, 0.9638417959213257, 0.9950698018074036, 0.9848688244819641, 0.9995719790458679, 0.9998329877853394, 0.9985089302062988, 0.9992951154708862, 0.975548505783081, 0.9458084106445312, 0.9959476590156555, 0.9995396733283997, 0.9955323934555054, 0.9993869662284851, 0.9993752241134644, 0.9936899542808533, 0.9966891407966614, 0.9059023857116699, 0.9983146786689758, 0.9910581707954407, 0.9994948506355286, 0.9722908139228821, 0.9382167458534241, 0.999916672706604, 0.9505757689476013, 0.9271995425224304, 0.932593584060669, 0.9966250061988831, 0.9398185610771179, 0.9587284326553345, 0.9671344757080078, 0.999489963054657, 0.9898932576179504, 0.9628493189811707, 0.9640218615531921, 0.999722957611084, 0.8036989569664001, 0.9698378443717957, 0.9564535617828369, 0.9998263120651245, 0.9991058111190796, 0.9808181524276733, 0.9993765950202942, 0.9573850631713867, 0.9877634048461914, 0.9970273971557617, 0.9993842244148254, 0.9960502982139587, 0.9983171224594116, 0.9375448822975159, 0.807371973991394, 0.9653717875480652, 0.9990774989128113, 0.9651206731796265, 0.9837372899055481, 0.9994117617607117, 0.9832388758659363, 0.9743798971176147, 0.9996598362922668, 0.9995712041854858, 0.9933780431747437, 0.9984723925590515, 0.980570375919342, 0.990373432636261, 0.9992279410362244, 0.9688776135444641, 0.94920414686203, 0.9967842102050781, 0.9992240071296692, 0.9911219477653503, 0.999700129032135, 0.9700611233711243, 0.9697025418281555, 0.9966064095497131, 0.9990625381469727, 0.9932660460472107, 0.9969024658203125, 0.9087286591529846, 0.9996081590652466, 0.991598904132843, 0.9954751133918762, 0.912391722202301, 0.9993894696235657, 0.9968301057815552, 0.9977517127990723, 0.9996945858001709, 0.9647232890129089], "rec_polys": [[[77, 48], [227, 48], [227, 107], [77, 107]], [[1161, 48], [1251, 48], [1251, 107], [1161, 107]], [[1269, 53], [1346, 53], [1346, 103], [1269, 103]], [[589, 179], [846, 179], [846, 256], [589, 256]], [[34, 208], [111, 208], [111, 284], [34, 284]], [[518, 267], [919, 267], [919, 311], [518, 311]], [[32, 353], [291, 353], [291, 405], [32, 405]], [[79, 653], [456, 663], [454, 746], [76, 736]], [[94, 845], [842, 847], [842, 898], [94, 897]], [[92, 946], [786, 946], [786, 1002], [92, 1002]], [[86, 1226], [341, 1226], [341, 1296], [86, 1296]], [[84, 1409], [297, 1404], [299, 1463], [85, 1468]], [[1121, 1408], [1350, 1408], [1350, 1467], [1121, 1467]], [[94, 1561], [343, 1561], [343, 1613], [94, 1613]], [[1059, 1598], [1354, 1598], [1354, 1661], [1059, 1661]], [[90, 1653], [560, 1651], [561, 1696], [90, 1697]], [[90, 1795], [341, 1795], [341, 1847], [90, 1847]], [[1211, 1834], [1354, 1834], [1354, 1894], [1211, 1894]], [[90, 1885], [495, 1889], [495, 1933], [90, 1929]], [[92, 2029], [339, 2029], [339, 2080], [92, 2080]], [[1219, 2061], [1354, 2066], [1352, 2129], [1216, 2124]], [[92, 2123], [424, 2123], [424, 2161], [92, 2161]], [[94, 2263], [388, 2263], [388, 2312], [94, 2312]], [[1123, 2309], [1166, 2309], [1166, 2355], [1123, 2355]], [[1194, 2303], [1350, 2303], [1350, 2356], [1194, 2356]], [[92, 2356], [422, 2356], [422, 2395], [92, 2395]], [[92, 2496], [343, 2496], [343, 2548], [92, 2548]], [[1086, 2535], [1350, 2535], [1350, 2587], [1086, 2587]], [[90, 2587], [540, 2585], [540, 2629], [90, 2631]], [[92, 2730], [343, 2730], [343, 2780], [92, 2780]], [[1014, 2776], [1056, 2776], [1056, 2819], [1014, 2819]], [[1082, 2767], [1348, 2767], [1348, 2819], [1082, 2819]], [[90, 2820], [551, 2819], [551, 2863], [90, 2865]], [[96, 2966], [390, 2966], [390, 3010], [96, 3010]], [[994, 3010], [1028, 3010], [1028, 3051], [994, 3051]], [[1065, 3003], [1350, 3003], [1350, 3054], [1065, 3054]], [[94, 3056], [551, 3056], [551, 3095], [94, 3095]], [[90, 3196], [343, 3196], [343, 3248], [90, 3248]], [[1035, 3233], [1352, 3233], [1352, 3290], [1035, 3290]], [[966, 3246], [999, 3246], [999, 3284], [966, 3284]], [[92, 3288], [562, 3288], [562, 3330], [92, 3330]], [[90, 3428], [292, 3428], [292, 3479], [90, 3479]], [[1142, 3468], [1208, 3468], [1208, 3527], [1142, 3527]], [[1211, 3463], [1356, 3463], [1356, 3533], [1211, 3533]], [[90, 3522], [459, 3522], [459, 3566], [90, 3566]], [[90, 3664], [345, 3664], [345, 3713], [90, 3713]], [[1189, 3700], [1354, 3700], [1354, 3759], [1189, 3759]], [[94, 3757], [472, 3757], [472, 3794], [94, 3794]], [[92, 3894], [390, 3894], [390, 3943], [92, 3943]], [[1217, 3932], [1358, 3932], [1358, 3993], [1217, 3993]], [[92, 3989], [459, 3989], [459, 4026], [92, 4026]], [[90, 4133], [394, 4133], [394, 4177], [90, 4177]], [[1187, 4159], [1360, 4173], [1354, 4240], [1181, 4225]], [[88, 4219], [499, 4223], [498, 4268], [88, 4264]], [[92, 4365], [394, 4365], [394, 4409], [92, 4409]], [[1154, 4394], [1356, 4399], [1355, 4465], [1153, 4460]], [[1089, 4409], [1132, 4409], [1132, 4453], [1089, 4453]], [[94, 4457], [472, 4457], [472, 4496], [94, 4496]], [[90, 4597], [343, 4597], [343, 4647], [90, 4647]], [[1074, 4634], [1352, 4634], [1352, 4691], [1074, 4691]], [[94, 4691], [576, 4691], [576, 4728], [94, 4728]], [[90, 4831], [377, 4831], [377, 4880], [90, 4880]], [[1236, 4864], [1356, 4864], [1356, 4927], [1236, 4927]], [[90, 4919], [443, 4923], [442, 4967], [90, 4963]], [[90, 5063], [343, 5063], [343, 5114], [90, 5114]], [[1202, 5101], [1354, 5101], [1354, 5160], [1202, 5160]], [[88, 5153], [486, 5157], [485, 5201], [88, 5197]], [[88, 5297], [377, 5297], [377, 5346], [88, 5346]], [[1241, 5337], [1352, 5337], [1352, 5392], [1241, 5392]], [[92, 5392], [401, 5392], [401, 5429], [92, 5429]], [[92, 5529], [390, 5529], [390, 5578], [92, 5578]], [[1069, 5569], [1350, 5569], [1350, 5621], [1069, 5621]], [[90, 5622], [609, 5622], [609, 5665], [90, 5665]], [[92, 5764], [390, 5764], [390, 5814], [92, 5814]], [[1191, 5805], [1352, 5805], [1352, 5858], [1191, 5858]], [[92, 5858], [444, 5858], [444, 5897], [92, 5897]], [[94, 6000], [392, 6000], [392, 6044], [94, 6044]], [[1056, 6037], [1350, 6037], [1350, 6088], [1056, 6088]], [[92, 6092], [615, 6092], [615, 6129], [92, 6129]], [[92, 6230], [390, 6230], [390, 6280], [92, 6280]], [[1191, 6269], [1352, 6269], [1352, 6328], [1191, 6328]], [[92, 6326], [489, 6326], [489, 6364], [92, 6364]], [[88, 6464], [478, 6464], [478, 6513], [88, 6513]], [[1204, 6506], [1252, 6506], [1252, 6558], [1204, 6558]], [[1271, 6501], [1356, 6501], [1356, 6561], [1271, 6561]], [[90, 6556], [444, 6556], [444, 6600], [90, 6600]], [[88, 6699], [482, 6699], [482, 6749], [88, 6749]], [[1127, 6738], [1176, 6738], [1176, 6793], [1127, 6793]], [[1200, 6734], [1352, 6734], [1352, 6793], [1200, 6793]], [[90, 6788], [472, 6788], [472, 6832], [90, 6832]], [[92, 6930], [392, 6930], [392, 6981], [92, 6981]], [[1232, 6966], [1356, 6966], [1356, 7031], [1232, 7031]], [[88, 7018], [278, 7018], [278, 7071], [88, 7071]]], "rec_boxes": [[77, 48, 227, 107], [1161, 48, 1251, 107], [1269, 53, 1346, 103], [589, 179, 846, 256], [34, 208, 111, 284], [518, 267, 919, 311], [32, 353, 291, 405], [76, 653, 456, 746], [94, 845, 842, 898], [92, 946, 786, 1002], [86, 1226, 341, 1296], [84, 1404, 299, 1468], [1121, 1408, 1350, 1467], [94, 1561, 343, 1613], [1059, 1598, 1354, 1661], [90, 1651, 561, 1697], [90, 1795, 341, 1847], [1211, 1834, 1354, 1894], [90, 1885, 495, 1933], [92, 2029, 339, 2080], [1216, 2061, 1354, 2129], [92, 2123, 424, 2161], [94, 2263, 388, 2312], [1123, 2309, 1166, 2355], [1194, 2303, 1350, 2356], [92, 2356, 422, 2395], [92, 2496, 343, 2548], [1086, 2535, 1350, 2587], [90, 2585, 540, 2631], [92, 2730, 343, 2780], [1014, 2776, 1056, 2819], [1082, 2767, 1348, 2819], [90, 2819, 551, 2865], [96, 2966, 390, 3010], [994, 3010, 1028, 3051], [1065, 3003, 1350, 3054], [94, 3056, 551, 3095], [90, 3196, 343, 3248], [1035, 3233, 1352, 3290], [966, 3246, 999, 3284], [92, 3288, 562, 3330], [90, 3428, 292, 3479], [1142, 3468, 1208, 3527], [1211, 3463, 1356, 3533], [90, 3522, 459, 3566], [90, 3664, 345, 3713], [1189, 3700, 1354, 3759], [94, 3757, 472, 3794], [92, 3894, 390, 3943], [1217, 3932, 1358, 3993], [92, 3989, 459, 4026], [90, 4133, 394, 4177], [1181, 4159, 1360, 4240], [88, 4219, 499, 4268], [92, 4365, 394, 4409], [1153, 4394, 1356, 4465], [1089, 4409, 1132, 4453], [94, 4457, 472, 4496], [90, 4597, 343, 4647], [1074, 4634, 1352, 4691], [94, 4691, 576, 4728], [90, 4831, 377, 4880], [1236, 4864, 1356, 4927], [90, 4919, 443, 4967], [90, 5063, 343, 5114], [1202, 5101, 1354, 5160], [88, 5153, 486, 5201], [88, 5297, 377, 5346], [1241, 5337, 1352, 5392], [92, 5392, 401, 5429], [92, 5529, 390, 5578], [1069, 5569, 1350, 5621], [90, 5622, 609, 5665], [92, 5764, 390, 5814], [1191, 5805, 1352, 5858], [92, 5858, 444, 5897], [94, 6000, 392, 6044], [1056, 6037, 1350, 6088], [92, 6092, 615, 6129], [92, 6230, 390, 6280], [1191, 6269, 1352, 6328], [92, 6326, 489, 6364], [88, 6464, 478, 6513], [1204, 6506, 1252, 6558], [1271, 6501, 1356, 6561], [90, 6556, 444, 6600], [88, 6699, 482, 6749], [1127, 6738, 1176, 6793], [1200, 6734, 1352, 6793], [90, 6788, 472, 6832], [92, 6930, 392, 6981], [1232, 6966, 1356, 7031], [88, 7018, 278, 7071]]}