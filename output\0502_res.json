{"input_path": "D:\\dev\\medicalReport\\ocr\\source\\0502.jpg", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[65, 46], [211, 46], [211, 106], [65, 106]], [[959, 44], [1125, 44], [1125, 86], [959, 86]], [[231, 56], [292, 56], [292, 94], [231, 94]], [[908, 56], [945, 56], [945, 98], [908, 98]], [[1268, 58], [1325, 58], [1325, 100], [1268, 100]], [[970, 76], [1041, 76], [1041, 106], [970, 106]], [[1079, 79], [1117, 73], [1120, 97], [1083, 102]], [[247, 90], [278, 90], [278, 104], [247, 104]], [[577, 205], [861, 205], [861, 283], [577, 283]], [[569, 389], [867, 389], [867, 474], [569, 474]], [[70, 612], [301, 612], [301, 698], [70, 698]], [[76, 764], [869, 764], [869, 817], [76, 817]], [[76, 883], [499, 883], [499, 939], [76, 939]], [[80, 1007], [610, 1007], [610, 1055], [80, 1055]], [[78, 1126], [869, 1126], [869, 1174], [78, 1174]], [[70, 1330], [194, 1330], [194, 1397], [70, 1397]], [[1238, 1326], [1372, 1326], [1372, 1401], [1238, 1401]], [[1273, 1510], [1375, 1516], [1370, 1588], [1269, 1582]], [[80, 1521], [356, 1521], [356, 1577], [80, 1577]], [[78, 1607], [497, 1607], [497, 1655], [78, 1655]], [[1036, 1598], [1366, 1605], [1365, 1661], [1035, 1654]], [[80, 1788], [432, 1788], [432, 1844], [80, 1844]], [[1242, 1782], [1370, 1782], [1370, 1848], [1242, 1848]], [[80, 1876], [548, 1876], [548, 1918], [80, 1918]], [[1164, 1870], [1368, 1870], [1368, 1926], [1164, 1926]], [[78, 2053], [374, 2053], [374, 2107], [78, 2107]], [[1244, 2051], [1368, 2051], [1368, 2111], [1244, 2111]], [[80, 2143], [550, 2143], [550, 2185], [80, 2185]], [[1164, 2135], [1368, 2135], [1368, 2193], [1164, 2193]], [[82, 2324], [374, 2324], [374, 2372], [82, 2372]], [[1274, 2316], [1370, 2316], [1370, 2380], [1274, 2380]], [[80, 2410], [520, 2410], [520, 2450], [80, 2450]], [[1166, 2402], [1370, 2402], [1370, 2460], [1166, 2460]], [[1274, 2582], [1366, 2582], [1366, 2645], [1274, 2645]], [[82, 2594], [481, 2594], [481, 2635], [82, 2635]], [[80, 2675], [450, 2675], [450, 2715], [80, 2715]], [[1166, 2667], [1368, 2667], [1368, 2723], [1166, 2723]], [[80, 2857], [483, 2857], [483, 2905], [80, 2905]], [[1274, 2849], [1368, 2849], [1368, 2913], [1274, 2913]], [[80, 2942], [493, 2942], [493, 2982], [80, 2982]], [[1166, 2934], [1368, 2934], [1368, 2992], [1166, 2992]], [[1275, 3109], [1372, 3115], [1369, 3181], [1271, 3175]], [[82, 3122], [350, 3122], [350, 3170], [82, 3170]], [[80, 3208], [493, 3208], [493, 3249], [80, 3249]], [[1039, 3204], [1366, 3204], [1366, 3257], [1039, 3257]], [[76, 3385], [299, 3385], [299, 3441], [76, 3441]], [[1274, 3379], [1373, 3379], [1373, 3447], [1274, 3447]], [[1040, 3462], [1364, 3469], [1363, 3523], [1039, 3516]], [[80, 3475], [493, 3475], [493, 3515], [80, 3515]], [[78, 3654], [297, 3654], [297, 3704], [78, 3704]], [[1274, 3648], [1370, 3648], [1370, 3712], [1274, 3712]], [[80, 3740], [491, 3740], [491, 3782], [80, 3782]], [[1041, 3736], [1364, 3736], [1364, 3784], [1041, 3784]], [[82, 3923], [405, 3923], [405, 3965], [82, 3965]], [[1244, 3915], [1368, 3915], [1368, 3973], [1244, 3973]], [[78, 4005], [550, 4003], [550, 4045], [78, 4047]], [[1041, 4003], [1364, 4003], [1364, 4051], [1041, 4051]], [[82, 4188], [403, 4188], [403, 4230], [82, 4230]], [[1244, 4182], [1368, 4182], [1368, 4240], [1244, 4240]], [[80, 4272], [550, 4272], [550, 4314], [80, 4314]], [[1041, 4270], [1364, 4270], [1364, 4318], [1041, 4318]], [[78, 4453], [352, 4453], [352, 4501], [78, 4501]], [[1244, 4447], [1368, 4447], [1368, 4507], [1244, 4507]], [[80, 4539], [550, 4539], [550, 4579], [80, 4579]], [[1013, 4537], [1364, 4537], [1364, 4585], [1013, 4585]], [[76, 4715], [297, 4715], [297, 4770], [76, 4770]], [[1186, 4715], [1358, 4715], [1358, 4770], [1186, 4770]], [[80, 4804], [520, 4804], [520, 4846], [80, 4846]], [[1124, 4796], [1364, 4801], [1363, 4856], [1123, 4852]], [[76, 4984], [352, 4984], [352, 5032], [76, 5032]], [[1170, 4982], [1356, 4982], [1356, 5038], [1170, 5038]], [[80, 5071], [550, 5071], [550, 5111], [80, 5111]], [[1164, 5063], [1368, 5063], [1368, 5119], [1164, 5119]], [[82, 5253], [460, 5253], [460, 5295], [82, 5295]], [[1244, 5245], [1370, 5245], [1370, 5305], [1244, 5305]], [[82, 5337], [579, 5337], [579, 5376], [82, 5376]], [[1172, 5329], [1368, 5329], [1368, 5384], [1172, 5384]], [[78, 5520], [463, 5516], [464, 5562], [79, 5566]], [[1170, 5514], [1358, 5514], [1358, 5570], [1170, 5570]], [[1145, 5589], [1371, 5603], [1367, 5665], [1141, 5651]], [[78, 5604], [550, 5602], [550, 5644], [78, 5646]], [[1256, 5775], [1372, 5775], [1372, 5843], [1256, 5843]], [[82, 5787], [518, 5787], [518, 5829], [82, 5829]], [[1122, 5859], [1368, 5863], [1367, 5925], [1121, 5920]], [[80, 5871], [524, 5871], [524, 5911], [80, 5911]], [[80, 6052], [462, 6052], [462, 6094], [80, 6094]], [[1246, 6044], [1370, 6044], [1370, 6104], [1246, 6104]], [[80, 6136], [552, 6136], [552, 6176], [80, 6176]], [[1164, 6128], [1368, 6128], [1368, 6184], [1164, 6184]], [[78, 6317], [352, 6317], [352, 6365], [78, 6365]], [[1258, 6309], [1370, 6309], [1370, 6371], [1258, 6371]], [[80, 6403], [522, 6403], [522, 6443], [80, 6443]], [[1041, 6399], [1364, 6399], [1364, 6447], [1041, 6447]], [[1246, 6577], [1368, 6577], [1368, 6638], [1246, 6638]], [[80, 6587], [464, 6587], [464, 6628], [80, 6628]], [[80, 6668], [524, 6668], [524, 6710], [80, 6710]], [[1174, 6660], [1368, 6660], [1368, 6716], [1174, 6716]], [[74, 6848], [354, 6848], [354, 6901], [74, 6901]], [[1244, 6844], [1370, 6844], [1370, 6903], [1244, 6903]], [[1162, 6923], [1370, 6923], [1370, 6987], [1162, 6987]], [[78, 6933], [583, 6933], [583, 6979], [78, 6979]], [[76, 7113], [468, 7113], [468, 7167], [76, 7167]], [[1285, 7105], [1372, 7105], [1372, 7175], [1285, 7175]], [[78, 7199], [470, 7199], [470, 7246], [78, 7246]], [[1162, 7191], [1370, 7191], [1370, 7254], [1162, 7254]], [[76, 7404], [470, 7404], [470, 7460], [76, 7460]], [[76, 7523], [526, 7523], [526, 7583], [76, 7583]], [[76, 7643], [875, 7643], [875, 7703], [76, 7703]], [[245, 7817], [1193, 7817], [1193, 7864], [245, 7864]]], "text_det_params": {"limit_side_len": 64, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.0, "rec_texts": ["21:53", "49.0 5GA", "头条", "*", "76", "KB/S", "山", "高考季", "报告详情", "检验报告", "李春香", "证件号：332623195201264044", "单号：36671763", "申请科室：10100234", "检验日期：2025-05-0214:29:56", "项目", "结果", "5.4", "白细胞计数", "参考范围：3.5-9.5", "单位：10E9/L", "中性粒细胞(%)", "47.3", "参考范围：40.0-75.0", "单位：%", "淋巴细胞(%)", "45.8", "参考范围：20.0-50.0", "单位：%", "单核细胞(%)", "3.7", "参考范围：3.0-10.0", "单位：%", "3.1", "嗜酸性粒细胞(%)", "参考范围：0.4-8", "单位：%", "嗜碱性粒细胞(%)", "0.1", "参考范围：0.0-1.0", "单位：%", "2.5", "中性粒细胞", "参考范围：1.8-6.3", "单位：10E9/L", "淋巴细胞", "2.5", "单位：10E9/L", "参考范围：1.1-3.2", "单核细胞", "0.2", "参考范围：0.1-0.6", "单位：10E9/L", "嗜酸性粒细胞", "0.17", "参考范围：0.02-0.52", "单位：10E9/L", "嗜碱性粒细胞", "0.00", "参考范围：0.00-0.06", "单位：10E9/L", "红细胞计数", "3.93", "参考范围：3.80-5.10", "单位：10E12/L", "血红蛋白", "106↓", "参考范围：115-150", "单位：g/L", "红细胞压积", "32.8 ↓", "参考范围：35.0-45.0", "单位：%", "平均红细胞体积", "83.4", "参考范围：82.0-100.0", "单位：fl", "平均血红蛋白量", "26.8 ↓", "单位：pg", "参考范围：27.0-34.0", "322", "平均血红蛋白浓度", "单位：g/L", "参考范围：316-354", "红细胞分布宽度", "12.6", "参考范围：11.5-14.5", "单位：%", "血小板计数", "140", "参考范围：125-350", "单位：10E9/L", "10.3", "平均血小板体积", "参考范围：7.4-12.5", "单位：fl", "血小板压积", "0.14", "单位：%", "参考范围：0.05-0.282", "血小板分布宽度", "16", "参考范围：10-20", "单位：%", "报告医师：傅鑫", "审核医师：程启媛", "审核时间：2025-05-0214:32:37", "此报告单仅作参考，以医院实际纸质报告为准"], "rec_scores": [0.9602562189102173, 0.9231206774711609, 0.9988722205162048, 0.2531892657279968, 0.9994803667068481, 0.9791809916496277, 0.2743625342845917, 0.8019048571586609, 0.9994144439697266, 0.9998154640197754, 0.9997064471244812, 0.9978041052818298, 0.9916113018989563, 0.98988276720047, 0.979255199432373, 0.9999361038208008, 0.9994986057281494, 0.9998337626457214, 0.9992648363113403, 0.9941473007202148, 0.9939045310020447, 0.9153581261634827, 0.999775767326355, 0.962127149105072, 0.989130437374115, 0.9125766754150391, 0.9998646974563599, 0.9422271847724915, 0.9845002889633179, 0.921407163143158, 0.999741792678833, 0.9917656779289246, 0.9768320918083191, 0.9997923374176025, 0.9260563254356384, 0.9663912057876587, 0.9866399765014648, 0.9053414463996887, 0.9997360706329346, 0.9906046986579895, 0.9824782609939575, 0.9998355507850647, 0.9988158941268921, 0.9481269717216492, 0.9947877526283264, 0.9981704950332642, 0.9999401569366455, 0.986903190612793, 0.9923007488250732, 0.9997326731681824, 0.9997071623802185, 0.979684054851532, 0.9697919487953186, 0.9971640706062317, 0.9996400475502014, 0.9800471663475037, 0.9728972315788269, 0.9945337176322937, 0.999786376953125, 0.9874948263168335, 0.9946444034576416, 0.9981836080551147, 0.9999181032180786, 0.9888052344322205, 0.9933613538742065, 0.9996428489685059, 0.998924970626831, 0.9937689900398254, 0.9919955730438232, 0.9989398717880249, 0.9638814330101013, 0.9868912696838379, 0.9863653779029846, 0.9956619143486023, 0.9997451305389404, 0.9895009398460388, 0.9336905479431152, 0.9973990321159363, 0.969666063785553, 0.9934846758842468, 0.9825989007949829, 0.9997730255126953, 0.9957824945449829, 0.9895973205566406, 0.9938141703605652, 0.9882503747940063, 0.9999570846557617, 0.9750261306762695, 0.9864752888679504, 0.9989694356918335, 0.9998698234558105, 0.985493004322052, 0.9941684007644653, 0.9999672174453735, 0.9939712285995483, 0.9620312452316284, 0.9738132357597351, 0.9994414448738098, 0.9997563362121582, 0.9845882654190063, 0.9645401239395142, 0.9990355372428894, 0.999711275100708, 0.9944945573806763, 0.9923949241638184, 0.996688961982727, 0.9952127933502197, 0.9908957481384277, 0.9989263415336609], "rec_polys": [[[65, 46], [211, 46], [211, 106], [65, 106]], [[959, 44], [1125, 44], [1125, 86], [959, 86]], [[231, 56], [292, 56], [292, 94], [231, 94]], [[908, 56], [945, 56], [945, 98], [908, 98]], [[1268, 58], [1325, 58], [1325, 100], [1268, 100]], [[970, 76], [1041, 76], [1041, 106], [970, 106]], [[1079, 79], [1117, 73], [1120, 97], [1083, 102]], [[247, 90], [278, 90], [278, 104], [247, 104]], [[577, 205], [861, 205], [861, 283], [577, 283]], [[569, 389], [867, 389], [867, 474], [569, 474]], [[70, 612], [301, 612], [301, 698], [70, 698]], [[76, 764], [869, 764], [869, 817], [76, 817]], [[76, 883], [499, 883], [499, 939], [76, 939]], [[80, 1007], [610, 1007], [610, 1055], [80, 1055]], [[78, 1126], [869, 1126], [869, 1174], [78, 1174]], [[70, 1330], [194, 1330], [194, 1397], [70, 1397]], [[1238, 1326], [1372, 1326], [1372, 1401], [1238, 1401]], [[1273, 1510], [1375, 1516], [1370, 1588], [1269, 1582]], [[80, 1521], [356, 1521], [356, 1577], [80, 1577]], [[78, 1607], [497, 1607], [497, 1655], [78, 1655]], [[1036, 1598], [1366, 1605], [1365, 1661], [1035, 1654]], [[80, 1788], [432, 1788], [432, 1844], [80, 1844]], [[1242, 1782], [1370, 1782], [1370, 1848], [1242, 1848]], [[80, 1876], [548, 1876], [548, 1918], [80, 1918]], [[1164, 1870], [1368, 1870], [1368, 1926], [1164, 1926]], [[78, 2053], [374, 2053], [374, 2107], [78, 2107]], [[1244, 2051], [1368, 2051], [1368, 2111], [1244, 2111]], [[80, 2143], [550, 2143], [550, 2185], [80, 2185]], [[1164, 2135], [1368, 2135], [1368, 2193], [1164, 2193]], [[82, 2324], [374, 2324], [374, 2372], [82, 2372]], [[1274, 2316], [1370, 2316], [1370, 2380], [1274, 2380]], [[80, 2410], [520, 2410], [520, 2450], [80, 2450]], [[1166, 2402], [1370, 2402], [1370, 2460], [1166, 2460]], [[1274, 2582], [1366, 2582], [1366, 2645], [1274, 2645]], [[82, 2594], [481, 2594], [481, 2635], [82, 2635]], [[80, 2675], [450, 2675], [450, 2715], [80, 2715]], [[1166, 2667], [1368, 2667], [1368, 2723], [1166, 2723]], [[80, 2857], [483, 2857], [483, 2905], [80, 2905]], [[1274, 2849], [1368, 2849], [1368, 2913], [1274, 2913]], [[80, 2942], [493, 2942], [493, 2982], [80, 2982]], [[1166, 2934], [1368, 2934], [1368, 2992], [1166, 2992]], [[1275, 3109], [1372, 3115], [1369, 3181], [1271, 3175]], [[82, 3122], [350, 3122], [350, 3170], [82, 3170]], [[80, 3208], [493, 3208], [493, 3249], [80, 3249]], [[1039, 3204], [1366, 3204], [1366, 3257], [1039, 3257]], [[76, 3385], [299, 3385], [299, 3441], [76, 3441]], [[1274, 3379], [1373, 3379], [1373, 3447], [1274, 3447]], [[1040, 3462], [1364, 3469], [1363, 3523], [1039, 3516]], [[80, 3475], [493, 3475], [493, 3515], [80, 3515]], [[78, 3654], [297, 3654], [297, 3704], [78, 3704]], [[1274, 3648], [1370, 3648], [1370, 3712], [1274, 3712]], [[80, 3740], [491, 3740], [491, 3782], [80, 3782]], [[1041, 3736], [1364, 3736], [1364, 3784], [1041, 3784]], [[82, 3923], [405, 3923], [405, 3965], [82, 3965]], [[1244, 3915], [1368, 3915], [1368, 3973], [1244, 3973]], [[78, 4005], [550, 4003], [550, 4045], [78, 4047]], [[1041, 4003], [1364, 4003], [1364, 4051], [1041, 4051]], [[82, 4188], [403, 4188], [403, 4230], [82, 4230]], [[1244, 4182], [1368, 4182], [1368, 4240], [1244, 4240]], [[80, 4272], [550, 4272], [550, 4314], [80, 4314]], [[1041, 4270], [1364, 4270], [1364, 4318], [1041, 4318]], [[78, 4453], [352, 4453], [352, 4501], [78, 4501]], [[1244, 4447], [1368, 4447], [1368, 4507], [1244, 4507]], [[80, 4539], [550, 4539], [550, 4579], [80, 4579]], [[1013, 4537], [1364, 4537], [1364, 4585], [1013, 4585]], [[76, 4715], [297, 4715], [297, 4770], [76, 4770]], [[1186, 4715], [1358, 4715], [1358, 4770], [1186, 4770]], [[80, 4804], [520, 4804], [520, 4846], [80, 4846]], [[1124, 4796], [1364, 4801], [1363, 4856], [1123, 4852]], [[76, 4984], [352, 4984], [352, 5032], [76, 5032]], [[1170, 4982], [1356, 4982], [1356, 5038], [1170, 5038]], [[80, 5071], [550, 5071], [550, 5111], [80, 5111]], [[1164, 5063], [1368, 5063], [1368, 5119], [1164, 5119]], [[82, 5253], [460, 5253], [460, 5295], [82, 5295]], [[1244, 5245], [1370, 5245], [1370, 5305], [1244, 5305]], [[82, 5337], [579, 5337], [579, 5376], [82, 5376]], [[1172, 5329], [1368, 5329], [1368, 5384], [1172, 5384]], [[78, 5520], [463, 5516], [464, 5562], [79, 5566]], [[1170, 5514], [1358, 5514], [1358, 5570], [1170, 5570]], [[1145, 5589], [1371, 5603], [1367, 5665], [1141, 5651]], [[78, 5604], [550, 5602], [550, 5644], [78, 5646]], [[1256, 5775], [1372, 5775], [1372, 5843], [1256, 5843]], [[82, 5787], [518, 5787], [518, 5829], [82, 5829]], [[1122, 5859], [1368, 5863], [1367, 5925], [1121, 5920]], [[80, 5871], [524, 5871], [524, 5911], [80, 5911]], [[80, 6052], [462, 6052], [462, 6094], [80, 6094]], [[1246, 6044], [1370, 6044], [1370, 6104], [1246, 6104]], [[80, 6136], [552, 6136], [552, 6176], [80, 6176]], [[1164, 6128], [1368, 6128], [1368, 6184], [1164, 6184]], [[78, 6317], [352, 6317], [352, 6365], [78, 6365]], [[1258, 6309], [1370, 6309], [1370, 6371], [1258, 6371]], [[80, 6403], [522, 6403], [522, 6443], [80, 6443]], [[1041, 6399], [1364, 6399], [1364, 6447], [1041, 6447]], [[1246, 6577], [1368, 6577], [1368, 6638], [1246, 6638]], [[80, 6587], [464, 6587], [464, 6628], [80, 6628]], [[80, 6668], [524, 6668], [524, 6710], [80, 6710]], [[1174, 6660], [1368, 6660], [1368, 6716], [1174, 6716]], [[74, 6848], [354, 6848], [354, 6901], [74, 6901]], [[1244, 6844], [1370, 6844], [1370, 6903], [1244, 6903]], [[1162, 6923], [1370, 6923], [1370, 6987], [1162, 6987]], [[78, 6933], [583, 6933], [583, 6979], [78, 6979]], [[76, 7113], [468, 7113], [468, 7167], [76, 7167]], [[1285, 7105], [1372, 7105], [1372, 7175], [1285, 7175]], [[78, 7199], [470, 7199], [470, 7246], [78, 7246]], [[1162, 7191], [1370, 7191], [1370, 7254], [1162, 7254]], [[76, 7404], [470, 7404], [470, 7460], [76, 7460]], [[76, 7523], [526, 7523], [526, 7583], [76, 7583]], [[76, 7643], [875, 7643], [875, 7703], [76, 7703]], [[245, 7817], [1193, 7817], [1193, 7864], [245, 7864]]], "rec_boxes": [[65, 46, 211, 106], [959, 44, 1125, 86], [231, 56, 292, 94], [908, 56, 945, 98], [1268, 58, 1325, 100], [970, 76, 1041, 106], [1079, 73, 1120, 102], [247, 90, 278, 104], [577, 205, 861, 283], [569, 389, 867, 474], [70, 612, 301, 698], [76, 764, 869, 817], [76, 883, 499, 939], [80, 1007, 610, 1055], [78, 1126, 869, 1174], [70, 1330, 194, 1397], [1238, 1326, 1372, 1401], [1269, 1510, 1375, 1588], [80, 1521, 356, 1577], [78, 1607, 497, 1655], [1035, 1598, 1366, 1661], [80, 1788, 432, 1844], [1242, 1782, 1370, 1848], [80, 1876, 548, 1918], [1164, 1870, 1368, 1926], [78, 2053, 374, 2107], [1244, 2051, 1368, 2111], [80, 2143, 550, 2185], [1164, 2135, 1368, 2193], [82, 2324, 374, 2372], [1274, 2316, 1370, 2380], [80, 2410, 520, 2450], [1166, 2402, 1370, 2460], [1274, 2582, 1366, 2645], [82, 2594, 481, 2635], [80, 2675, 450, 2715], [1166, 2667, 1368, 2723], [80, 2857, 483, 2905], [1274, 2849, 1368, 2913], [80, 2942, 493, 2982], [1166, 2934, 1368, 2992], [1271, 3109, 1372, 3181], [82, 3122, 350, 3170], [80, 3208, 493, 3249], [1039, 3204, 1366, 3257], [76, 3385, 299, 3441], [1274, 3379, 1373, 3447], [1039, 3462, 1364, 3523], [80, 3475, 493, 3515], [78, 3654, 297, 3704], [1274, 3648, 1370, 3712], [80, 3740, 491, 3782], [1041, 3736, 1364, 3784], [82, 3923, 405, 3965], [1244, 3915, 1368, 3973], [78, 4003, 550, 4047], [1041, 4003, 1364, 4051], [82, 4188, 403, 4230], [1244, 4182, 1368, 4240], [80, 4272, 550, 4314], [1041, 4270, 1364, 4318], [78, 4453, 352, 4501], [1244, 4447, 1368, 4507], [80, 4539, 550, 4579], [1013, 4537, 1364, 4585], [76, 4715, 297, 4770], [1186, 4715, 1358, 4770], [80, 4804, 520, 4846], [1123, 4796, 1364, 4856], [76, 4984, 352, 5032], [1170, 4982, 1356, 5038], [80, 5071, 550, 5111], [1164, 5063, 1368, 5119], [82, 5253, 460, 5295], [1244, 5245, 1370, 5305], [82, 5337, 579, 5376], [1172, 5329, 1368, 5384], [78, 5516, 464, 5566], [1170, 5514, 1358, 5570], [1141, 5589, 1371, 5665], [78, 5602, 550, 5646], [1256, 5775, 1372, 5843], [82, 5787, 518, 5829], [1121, 5859, 1368, 5925], [80, 5871, 524, 5911], [80, 6052, 462, 6094], [1246, 6044, 1370, 6104], [80, 6136, 552, 6176], [1164, 6128, 1368, 6184], [78, 6317, 352, 6365], [1258, 6309, 1370, 6371], [80, 6403, 522, 6443], [1041, 6399, 1364, 6447], [1246, 6577, 1368, 6638], [80, 6587, 464, 6628], [80, 6668, 524, 6710], [1174, 6660, 1368, 6716], [74, 6848, 354, 6901], [1244, 6844, 1370, 6903], [1162, 6923, 1370, 6987], [78, 6933, 583, 6979], [76, 7113, 468, 7167], [1285, 7105, 1372, 7175], [78, 7199, 470, 7246], [1162, 7191, 1370, 7254], [76, 7404, 470, 7460], [76, 7523, 526, 7583], [76, 7643, 875, 7703], [245, 7817, 1193, 7864]]}