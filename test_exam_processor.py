#!/usr/bin/env python3
"""
测试检查报告处理器功能
"""

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from medical_ocr.config.config import DB_CONFIG, OCR_CONFIG
from medical_ocr.db.database_manager import DatabaseManager
from medical_ocr.ocr.exam_report_processor import ExamReportProcessor

def test_exam_processor():
    """测试检查报告处理器的基本功能"""
    try:
        print("=" * 50)
        print("测试检查报告处理器")
        print("=" * 50)
        
        # 初始化数据库管理器
        print("1. 初始化数据库连接...")
        db_manager = DatabaseManager(DB_CONFIG)
        print("   数据库连接成功")
        
        # 初始化检查报告处理器
        print("2. 初始化检查报告处理器...")
        processor = ExamReportProcessor(db_manager, OCR_CONFIG)
        print("   检查报告处理器初始化成功")
        
        # 测试文本解析功能
        print("3. 测试文本解析功能...")
        test_text = """
        检验日期：2024-12-01
        检查方法：CT平扫+增强扫描
        放射性诊断：肺部未见明显异常，心脏大小正常
        """
        
        # 模拟OCR结果
        class MockOCRResult:
            def __init__(self, texts):
                self.rec_texts = texts
            
            def save_to_json(self, output_dir):
                pass
        
        mock_result = MockOCRResult([
            "检验日期：2024-12-01",
            "检查方法：CT平扫+增强扫描",
            "放射性诊断：肺部未见明显异常，心脏大小正常"
        ])
        
        # 测试解析
        data = processor._parse_exam_ocr_result(mock_result)
        if data:
            print("   文本解析成功:")
            print(f"   - 检查日期: {data['medical_date']}")
            print(f"   - 检查方法: {data['exam_info']}")
            print(f"   - 放射性诊断: {data['exam_diag']}")
            print(f"   - 检查类型: {data['exam_type']}")
            print(f"   - 医院: {data['hospital']}")
        else:
            print("   文本解析失败")
            return False
        
        # 测试数据库保存
        print("4. 测试数据库保存...")
        try:
            processor._save_exam_to_database(data)
            print("   数据库保存成功")
        except Exception as e:
            print(f"   数据库保存失败: {str(e)}")
            return False
        
        print("=" * 50)
        print("所有测试通过！")
        print("=" * 50)
        return True
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_exam_processor()
    if success:
        print("\n检查报告处理器功能正常，可以开始使用！")
        print("\n使用方法：")
        print("1. 将检查报告图片放入 source2 目录")
        print("2. 运行 python run.py")
        print("3. 选择选项 2 - 检查报告")
        print("4. 处理完成后，图片会移动到 processed2 目录")
        print("5. 数据会保存到 medical_exam 表中")
    else:
        print("\n检查报告处理器测试失败，请检查配置和数据库连接")
