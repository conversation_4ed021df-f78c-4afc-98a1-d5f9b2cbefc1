{"input_path": "D:\\dev\\medicalReport\\ocr\\source\\0618.jpg", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[68, 48], [216, 48], [216, 101], [68, 101]], [[248, 61], [287, 61], [287, 96], [248, 96]], [[299, 60], [358, 54], [361, 89], [302, 95]], [[495, 61], [549, 61], [549, 96], [495, 96]], [[966, 52], [1052, 52], [1052, 87], [966, 87]], [[1264, 53], [1341, 53], [1341, 105], [1264, 105]], [[969, 72], [1041, 72], [1041, 105], [969, 105]], [[311, 88], [349, 88], [349, 103], [311, 103]], [[589, 180], [842, 180], [842, 252], [589, 252]], [[34, 208], [111, 208], [111, 284], [34, 284]], [[516, 263], [921, 263], [921, 313], [516, 313]], [[32, 353], [291, 353], [291, 405], [32, 405]], [[80, 653], [456, 663], [454, 746], [78, 736]], [[92, 843], [844, 845], [844, 900], [92, 898]], [[90, 946], [778, 946], [778, 1002], [90, 1002]], [[86, 1226], [341, 1226], [341, 1296], [86, 1296]], [[84, 1409], [297, 1404], [299, 1463], [85, 1468]], [[1121, 1408], [1350, 1408], [1350, 1467], [1121, 1467]], [[94, 1561], [343, 1561], [343, 1613], [94, 1613]], [[1084, 1600], [1352, 1600], [1352, 1659], [1084, 1659]], [[90, 1653], [560, 1651], [561, 1696], [90, 1697]], [[90, 1795], [339, 1795], [339, 1847], [90, 1847]], [[1206, 1834], [1354, 1834], [1354, 1894], [1206, 1894]], [[90, 1885], [495, 1889], [495, 1933], [90, 1929]], [[92, 2029], [339, 2029], [339, 2080], [92, 2080]], [[1144, 2078], [1179, 2078], [1179, 2117], [1144, 2117]], [[1213, 2069], [1350, 2069], [1350, 2125], [1213, 2125]], [[94, 2123], [424, 2123], [424, 2161], [94, 2161]], [[94, 2263], [388, 2263], [388, 2312], [94, 2312]], [[1196, 2301], [1352, 2301], [1352, 2360], [1196, 2360]], [[92, 2356], [422, 2356], [422, 2395], [92, 2395]], [[92, 2496], [343, 2496], [343, 2548], [92, 2548]], [[1086, 2535], [1350, 2535], [1350, 2587], [1086, 2587]], [[90, 2587], [540, 2585], [540, 2629], [90, 2631]], [[92, 2730], [343, 2730], [343, 2780], [92, 2780]], [[1020, 2776], [1061, 2776], [1061, 2819], [1020, 2819]], [[1091, 2769], [1350, 2769], [1350, 2820], [1091, 2820]], [[90, 2820], [551, 2819], [551, 2863], [90, 2865]], [[96, 2966], [390, 2966], [390, 3010], [96, 3010]], [[1082, 3001], [1350, 3001], [1350, 3058], [1082, 3058]], [[94, 3056], [551, 3056], [551, 3095], [94, 3095]], [[89, 3194], [343, 3198], [343, 3250], [88, 3245]], [[1033, 3233], [1350, 3233], [1350, 3290], [1033, 3290]], [[968, 3248], [1003, 3248], [1003, 3284], [968, 3284]], [[92, 3288], [562, 3288], [562, 3330], [92, 3330]], [[90, 3428], [292, 3428], [292, 3479], [90, 3479]], [[1136, 3461], [1358, 3461], [1358, 3535], [1136, 3535]], [[90, 3522], [459, 3522], [459, 3566], [90, 3566]], [[90, 3664], [345, 3664], [345, 3713], [90, 3713]], [[1118, 3706], [1170, 3706], [1170, 3757], [1118, 3757]], [[1189, 3700], [1352, 3700], [1352, 3759], [1189, 3759]], [[94, 3757], [472, 3757], [472, 3794], [94, 3794]], [[92, 3896], [392, 3896], [392, 3945], [92, 3945]], [[1217, 3931], [1354, 3931], [1354, 3991], [1217, 3991]], [[92, 3989], [461, 3989], [461, 4026], [92, 4026]], [[90, 4133], [394, 4133], [394, 4177], [90, 4177]], [[1198, 4159], [1359, 4171], [1354, 4240], [1193, 4227]], [[88, 4219], [499, 4223], [498, 4268], [88, 4264]], [[92, 4365], [394, 4365], [394, 4409], [92, 4409]], [[1095, 4406], [1146, 4406], [1146, 4455], [1095, 4455]], [[1164, 4398], [1356, 4398], [1356, 4464], [1164, 4464]], [[94, 4457], [472, 4457], [472, 4496], [94, 4496]], [[90, 4597], [343, 4597], [343, 4647], [90, 4647]], [[1067, 4634], [1352, 4634], [1352, 4691], [1067, 4691]], [[94, 4691], [576, 4691], [576, 4728], [94, 4728]], [[90, 4831], [377, 4831], [377, 4880], [90, 4880]], [[1222, 4869], [1354, 4869], [1354, 4925], [1222, 4925]], [[88, 4919], [443, 4923], [442, 4967], [88, 4963]], [[90, 5063], [343, 5063], [343, 5114], [90, 5114]], [[1192, 5101], [1354, 5101], [1354, 5160], [1192, 5160]], [[88, 5153], [486, 5157], [485, 5201], [88, 5197]], [[88, 5297], [377, 5297], [377, 5346], [88, 5346]], [[1178, 5337], [1230, 5337], [1230, 5390], [1178, 5390]], [[1251, 5335], [1352, 5335], [1352, 5394], [1251, 5394]], [[92, 5390], [401, 5390], [401, 5429], [92, 5429]], [[92, 5530], [392, 5530], [392, 5580], [92, 5580]], [[1067, 5569], [1350, 5569], [1350, 5621], [1067, 5621]], [[90, 5622], [609, 5622], [609, 5665], [90, 5665]], [[92, 5764], [390, 5764], [390, 5814], [92, 5814]], [[1200, 5801], [1354, 5801], [1354, 5862], [1200, 5862]], [[92, 5858], [444, 5858], [444, 5897], [92, 5897]], [[94, 6000], [392, 6000], [392, 6044], [94, 6044]], [[979, 6046], [1022, 6046], [1022, 6088], [979, 6088]], [[1052, 6037], [1350, 6037], [1350, 6088], [1052, 6088]], [[94, 6092], [617, 6092], [617, 6129], [94, 6129]], [[92, 6230], [390, 6230], [390, 6280], [92, 6280]], [[1140, 6276], [1183, 6276], [1183, 6324], [1140, 6324]], [[1213, 6270], [1352, 6270], [1352, 6326], [1213, 6326]], [[92, 6326], [489, 6326], [489, 6364], [92, 6364]], [[88, 6464], [478, 6464], [478, 6513], [88, 6513]], [[1208, 6506], [1260, 6506], [1260, 6559], [1208, 6559]], [[1275, 6501], [1354, 6501], [1354, 6561], [1275, 6561]], [[90, 6556], [444, 6556], [444, 6600], [90, 6600]], [[90, 6701], [480, 6701], [480, 6745], [90, 6745]], [[1131, 6742], [1174, 6742], [1174, 6790], [1131, 6790]], [[1202, 6734], [1352, 6734], [1352, 6795], [1202, 6795]], [[90, 6788], [472, 6788], [472, 6832], [90, 6832]], [[92, 6930], [394, 6930], [394, 6981], [92, 6981]], [[1241, 6966], [1356, 6966], [1356, 7031], [1241, 7031]], [[88, 7018], [278, 7018], [278, 7071], [88, 7071]]], "text_det_params": {"limit_side_len": 64, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.6, "rec_texts": ["08:19", "头条", "小红书", "0.25", "86", "KB/S", "报告查询", "X", "fwcs.linkingcloud.cn", "东院XN9000", "*春香***岁", "申请科室：胰腺肿瘤临床研究门诊", "报告日期：2025-6-180:00:00", "项目明细", "项目名称", "结果/单位", "白细胞计数", "8.3*10^9/L", "参考值：3.5-9.5*10^9/L", "淋巴细胞%", "33.1%", "参考值：20.0-50.0%", "单核细胞%", "↑", "11.8%", "参考值：3.0-10%", "中性粒细胞%", "52.2%", "参考值：40-75%", "淋巴细胞数", "2.8*10^9/L", "参考值：1.1-3.2*10^9/L", "单核细胞数", "↑", "1.0*10^9/L", "参考值：0.1-0.6*10^9/L", "中性粒细胞数", "4.3*10^9/L", "参考值：1.8-6.3*10^9/L", "红细胞计数", "3.43*10^12/L", "↓", "参考值：3.8-5.1*10^12/L", "血红蛋白", "93g/l", "参考值：115-150g/", "红细胞压积", "29.9%", "参考值：女35-45%", "平均RBC体积", "87.2fl", "参考值：82.0-100fl", "RBC平均HGB", "27.1pg", "参考值：27.0-34.0pg", "平均HGB浓度", "311.0g/l", "参考值：316-354g/", "血小板计数", "244*10^9/L", "参考值：125-350*10^9/L", "平均PLT容积", "10.2fl", "参考值：7.4-10.4l", "血小板压积", "0.25%", "参考值：0.15-0.30%", "PLT分布宽度", "11%", "参考值：13-21%", "嗜酸性细胞数", "0.15*10^9/L", "参考值：0.02-0.52*10^9/L", "嗜酸性细胞%", "1.80%", "参考值：0.4-8.0%", "嗜碱性细胞数", "↑", "0.09*10^9/L", "参考值：0.00-0.06*10^9/L", "嗜碱性细胞%", "↑", "1.10%", "参考值：0.00-1.00%", "RBC分布宽度-SD", "个", "47", "参考值：35-44FL", "RBC分布宽度-CV", "↑", "15.2%", "参考值：11.6-14.4%", "中性淋巴比值", "1.54", "参考值：."], "rec_scores": [0.9722379446029663, 0.9968783855438232, 0.9973405003547668, 0.9990313053131104, 0.9984683394432068, 0.949738085269928, 0.9997411966323853, 0.6316301226615906, 0.9984359741210938, 0.998546838760376, 0.9639495015144348, 0.9933459162712097, 0.9864518046379089, 0.9995788335800171, 0.9998359680175781, 0.9986330270767212, 0.9992848634719849, 0.9771191477775574, 0.9441612958908081, 0.997977614402771, 0.9995401501655579, 0.9953978657722473, 0.999407947063446, 0.9831467270851135, 0.9993179440498352, 0.9884423613548279, 0.9966614842414856, 0.9997633695602417, 0.9925589561462402, 0.9994829893112183, 0.972141444683075, 0.9634516835212708, 0.999917209148407, 0.9393396973609924, 0.9597052335739136, 0.9517643451690674, 0.9962961673736572, 0.9618948101997375, 0.9658105373382568, 0.9996742010116577, 0.9762997627258301, 0.9958886504173279, 0.9652507901191711, 0.9997258186340332, 0.980194091796875, 0.964006781578064, 0.9998153448104858, 0.9983881115913391, 0.9848747253417969, 0.9991942644119263, 0.9568687081336975, 0.9651358723640442, 0.9985005855560303, 0.9994688630104065, 0.9928439855575562, 0.9985474944114685, 0.953720211982727, 0.9563852548599243, 0.9991064071655273, 0.9801872372627258, 0.9828087687492371, 0.999395489692688, 0.9571720957756042, 0.9786742925643921, 0.9996598362922668, 0.9995185732841492, 0.9926745295524597, 0.9985408186912537, 0.9982795715332031, 0.98918616771698, 0.9993032813072205, 0.9733321666717529, 0.958362877368927, 0.9972096085548401, 0.9981396794319153, 0.9886302947998047, 0.9993283152580261, 0.9605885148048401, 0.9637280106544495, 0.9833261370658875, 0.996099054813385, 0.91114342212677, 0.9888619184494019, 0.9932817220687866, 0.9969517588615417, 0.9516066908836365, 0.9996066689491272, 0.9910951256752014, 0.9915639758110046, 0.9377316832542419, 0.9996644854545593, 0.9960843920707703, 0.9983389973640442, 0.9997814893722534, 0.9686206579208374], "rec_polys": [[[68, 48], [216, 48], [216, 101], [68, 101]], [[299, 60], [358, 54], [361, 89], [302, 95]], [[495, 61], [549, 61], [549, 96], [495, 96]], [[966, 52], [1052, 52], [1052, 87], [966, 87]], [[1264, 53], [1341, 53], [1341, 105], [1264, 105]], [[969, 72], [1041, 72], [1041, 105], [969, 105]], [[589, 180], [842, 180], [842, 252], [589, 252]], [[34, 208], [111, 208], [111, 284], [34, 284]], [[516, 263], [921, 263], [921, 313], [516, 313]], [[32, 353], [291, 353], [291, 405], [32, 405]], [[80, 653], [456, 663], [454, 746], [78, 736]], [[92, 843], [844, 845], [844, 900], [92, 898]], [[90, 946], [778, 946], [778, 1002], [90, 1002]], [[86, 1226], [341, 1226], [341, 1296], [86, 1296]], [[84, 1409], [297, 1404], [299, 1463], [85, 1468]], [[1121, 1408], [1350, 1408], [1350, 1467], [1121, 1467]], [[94, 1561], [343, 1561], [343, 1613], [94, 1613]], [[1084, 1600], [1352, 1600], [1352, 1659], [1084, 1659]], [[90, 1653], [560, 1651], [561, 1696], [90, 1697]], [[90, 1795], [339, 1795], [339, 1847], [90, 1847]], [[1206, 1834], [1354, 1834], [1354, 1894], [1206, 1894]], [[90, 1885], [495, 1889], [495, 1933], [90, 1929]], [[92, 2029], [339, 2029], [339, 2080], [92, 2080]], [[1144, 2078], [1179, 2078], [1179, 2117], [1144, 2117]], [[1213, 2069], [1350, 2069], [1350, 2125], [1213, 2125]], [[94, 2123], [424, 2123], [424, 2161], [94, 2161]], [[94, 2263], [388, 2263], [388, 2312], [94, 2312]], [[1196, 2301], [1352, 2301], [1352, 2360], [1196, 2360]], [[92, 2356], [422, 2356], [422, 2395], [92, 2395]], [[92, 2496], [343, 2496], [343, 2548], [92, 2548]], [[1086, 2535], [1350, 2535], [1350, 2587], [1086, 2587]], [[90, 2587], [540, 2585], [540, 2629], [90, 2631]], [[92, 2730], [343, 2730], [343, 2780], [92, 2780]], [[1020, 2776], [1061, 2776], [1061, 2819], [1020, 2819]], [[1091, 2769], [1350, 2769], [1350, 2820], [1091, 2820]], [[90, 2820], [551, 2819], [551, 2863], [90, 2865]], [[96, 2966], [390, 2966], [390, 3010], [96, 3010]], [[1082, 3001], [1350, 3001], [1350, 3058], [1082, 3058]], [[94, 3056], [551, 3056], [551, 3095], [94, 3095]], [[89, 3194], [343, 3198], [343, 3250], [88, 3245]], [[1033, 3233], [1350, 3233], [1350, 3290], [1033, 3290]], [[968, 3248], [1003, 3248], [1003, 3284], [968, 3284]], [[92, 3288], [562, 3288], [562, 3330], [92, 3330]], [[90, 3428], [292, 3428], [292, 3479], [90, 3479]], [[1136, 3461], [1358, 3461], [1358, 3535], [1136, 3535]], [[90, 3522], [459, 3522], [459, 3566], [90, 3566]], [[90, 3664], [345, 3664], [345, 3713], [90, 3713]], [[1189, 3700], [1352, 3700], [1352, 3759], [1189, 3759]], [[94, 3757], [472, 3757], [472, 3794], [94, 3794]], [[92, 3896], [392, 3896], [392, 3945], [92, 3945]], [[1217, 3931], [1354, 3931], [1354, 3991], [1217, 3991]], [[92, 3989], [461, 3989], [461, 4026], [92, 4026]], [[90, 4133], [394, 4133], [394, 4177], [90, 4177]], [[1198, 4159], [1359, 4171], [1354, 4240], [1193, 4227]], [[88, 4219], [499, 4223], [498, 4268], [88, 4264]], [[92, 4365], [394, 4365], [394, 4409], [92, 4409]], [[1164, 4398], [1356, 4398], [1356, 4464], [1164, 4464]], [[94, 4457], [472, 4457], [472, 4496], [94, 4496]], [[90, 4597], [343, 4597], [343, 4647], [90, 4647]], [[1067, 4634], [1352, 4634], [1352, 4691], [1067, 4691]], [[94, 4691], [576, 4691], [576, 4728], [94, 4728]], [[90, 4831], [377, 4831], [377, 4880], [90, 4880]], [[1222, 4869], [1354, 4869], [1354, 4925], [1222, 4925]], [[88, 4919], [443, 4923], [442, 4967], [88, 4963]], [[90, 5063], [343, 5063], [343, 5114], [90, 5114]], [[1192, 5101], [1354, 5101], [1354, 5160], [1192, 5160]], [[88, 5153], [486, 5157], [485, 5201], [88, 5197]], [[88, 5297], [377, 5297], [377, 5346], [88, 5346]], [[1251, 5335], [1352, 5335], [1352, 5394], [1251, 5394]], [[92, 5390], [401, 5390], [401, 5429], [92, 5429]], [[92, 5530], [392, 5530], [392, 5580], [92, 5580]], [[1067, 5569], [1350, 5569], [1350, 5621], [1067, 5621]], [[90, 5622], [609, 5622], [609, 5665], [90, 5665]], [[92, 5764], [390, 5764], [390, 5814], [92, 5814]], [[1200, 5801], [1354, 5801], [1354, 5862], [1200, 5862]], [[92, 5858], [444, 5858], [444, 5897], [92, 5897]], [[94, 6000], [392, 6000], [392, 6044], [94, 6044]], [[979, 6046], [1022, 6046], [1022, 6088], [979, 6088]], [[1052, 6037], [1350, 6037], [1350, 6088], [1052, 6088]], [[94, 6092], [617, 6092], [617, 6129], [94, 6129]], [[92, 6230], [390, 6230], [390, 6280], [92, 6280]], [[1140, 6276], [1183, 6276], [1183, 6324], [1140, 6324]], [[1213, 6270], [1352, 6270], [1352, 6326], [1213, 6326]], [[92, 6326], [489, 6326], [489, 6364], [92, 6364]], [[88, 6464], [478, 6464], [478, 6513], [88, 6513]], [[1208, 6506], [1260, 6506], [1260, 6559], [1208, 6559]], [[1275, 6501], [1354, 6501], [1354, 6561], [1275, 6561]], [[90, 6556], [444, 6556], [444, 6600], [90, 6600]], [[90, 6701], [480, 6701], [480, 6745], [90, 6745]], [[1131, 6742], [1174, 6742], [1174, 6790], [1131, 6790]], [[1202, 6734], [1352, 6734], [1352, 6795], [1202, 6795]], [[90, 6788], [472, 6788], [472, 6832], [90, 6832]], [[92, 6930], [394, 6930], [394, 6981], [92, 6981]], [[1241, 6966], [1356, 6966], [1356, 7031], [1241, 7031]], [[88, 7018], [278, 7018], [278, 7071], [88, 7071]]], "rec_boxes": [[68, 48, 216, 101], [299, 54, 361, 95], [495, 61, 549, 96], [966, 52, 1052, 87], [1264, 53, 1341, 105], [969, 72, 1041, 105], [589, 180, 842, 252], [34, 208, 111, 284], [516, 263, 921, 313], [32, 353, 291, 405], [78, 653, 456, 746], [92, 843, 844, 900], [90, 946, 778, 1002], [86, 1226, 341, 1296], [84, 1404, 299, 1468], [1121, 1408, 1350, 1467], [94, 1561, 343, 1613], [1084, 1600, 1352, 1659], [90, 1651, 561, 1697], [90, 1795, 339, 1847], [1206, 1834, 1354, 1894], [90, 1885, 495, 1933], [92, 2029, 339, 2080], [1144, 2078, 1179, 2117], [1213, 2069, 1350, 2125], [94, 2123, 424, 2161], [94, 2263, 388, 2312], [1196, 2301, 1352, 2360], [92, 2356, 422, 2395], [92, 2496, 343, 2548], [1086, 2535, 1350, 2587], [90, 2585, 540, 2631], [92, 2730, 343, 2780], [1020, 2776, 1061, 2819], [1091, 2769, 1350, 2820], [90, 2819, 551, 2865], [96, 2966, 390, 3010], [1082, 3001, 1350, 3058], [94, 3056, 551, 3095], [88, 3194, 343, 3250], [1033, 3233, 1350, 3290], [968, 3248, 1003, 3284], [92, 3288, 562, 3330], [90, 3428, 292, 3479], [1136, 3461, 1358, 3535], [90, 3522, 459, 3566], [90, 3664, 345, 3713], [1189, 3700, 1352, 3759], [94, 3757, 472, 3794], [92, 3896, 392, 3945], [1217, 3931, 1354, 3991], [92, 3989, 461, 4026], [90, 4133, 394, 4177], [1193, 4159, 1359, 4240], [88, 4219, 499, 4268], [92, 4365, 394, 4409], [1164, 4398, 1356, 4464], [94, 4457, 472, 4496], [90, 4597, 343, 4647], [1067, 4634, 1352, 4691], [94, 4691, 576, 4728], [90, 4831, 377, 4880], [1222, 4869, 1354, 4925], [88, 4919, 443, 4967], [90, 5063, 343, 5114], [1192, 5101, 1354, 5160], [88, 5153, 486, 5201], [88, 5297, 377, 5346], [1251, 5335, 1352, 5394], [92, 5390, 401, 5429], [92, 5530, 392, 5580], [1067, 5569, 1350, 5621], [90, 5622, 609, 5665], [92, 5764, 390, 5814], [1200, 5801, 1354, 5862], [92, 5858, 444, 5897], [94, 6000, 392, 6044], [979, 6046, 1022, 6088], [1052, 6037, 1350, 6088], [94, 6092, 617, 6129], [92, 6230, 390, 6280], [1140, 6276, 1183, 6324], [1213, 6270, 1352, 6326], [92, 6326, 489, 6364], [88, 6464, 478, 6513], [1208, 6506, 1260, 6559], [1275, 6501, 1354, 6561], [90, 6556, 444, 6600], [90, 6701, 480, 6745], [1131, 6742, 1174, 6790], [1202, 6734, 1352, 6795], [90, 6788, 472, 6832], [92, 6930, 394, 6981], [1241, 6966, 1356, 7031], [88, 7018, 278, 7071]]}