{"input_path": "./20250521143205.jpg", "page_index": null, "model_settings": {"use_doc_preprocessor": false, "use_seal_recognition": true, "use_table_recognition": true, "use_formula_recognition": true, "use_chart_recognition": false, "use_region_detection": true}, "parsing_res_list": [{"block_label": "header", "block_content": "80.00KB/S 55G ", "block_bbox": [837, 37, 1335, 105]}, {"block_label": "content", "block_content": "东院XN9000\n*春香***岁\n\n申请科室：胰腺肿瘤临床研究门诊\n\n报告日期：2025-5-210:00:00\n项目明细\n\n项目名称结果/单位\n白细胞计数\n\n\n5.5*10^9/L \n参考值：3.5-9.5*10^9/L \n\n淋巴细胞%\n\n\n39.4%\n参考值：20.0-50.0%\n\n单核细胞%\n\n\n8.8%\n参考值：3.0-10%\n\n中性粒细胞%\n\n\n49.8%\n参考值：40-75%\n\n淋巴细胞数\n\n\n2.2*10^9/L \n参考值：1.1-3.2*10^9/L \n\n单核细胞数\n\n\n0.5*10^9/L \n参考值：0.1-0.6*10^9/L \n\n中性粒细胞数\n\n\n2.7*10^9/L \n参考值：1.8-6.3*10^9/L \n\n红细胞计数\n\n\n↓3.75*10^12/L \n参考值：3.8-5.1*10^12/L \n\n血红蛋白\n\n\n山100g/l \n参考值：115-150g/l \n\n红细胞压积\n\n\n32.3%\n参考值：女35-45%\n\n平均RBC体积\n\n\n86.1fl \n参考值：82.0-100fl \n\nRBC平均HGB \n\n\n↓26.7pg \n参考值：27.0-34.0pg \n\n平均HGB浓度\n\n\n↓310.0g/l \n参考值：316-354g/\n\n血小板计数\n\n\n182*10^9/L \n参考值：125-350*10^9/L \n\n平均PLT容积\n\n\n9.5fl \n参考值：7.4-10.4f \n\n血小板压积\n\n\n0.17%\n参考值：0.15-0.30%\n\nPLT分布宽度\n\n\n10%\n参考值：13-21%\n\n嗜酸性细胞数\n\n\n0.09*10^9/L \n参考值：0.02-0.52*10^9/L \n\n嗜酸性细胞%\n\n\n1.60%\n参考值：0.4-8.0%\n\n嗜碱性细胞数\n\n\n0.02*10^9/L \n参考值：0.00-0.06*10^9/L \n\n嗜碱性细胞%\n\n\n0.40%\n参考值：0.00-1.00%\n\nRBC分布宽度-SD \n\n41\n参考值：35-44FL \n\nRBC分布宽度-CV \n\n\n13.2%\n参考值：11.6-14.4%\n\n中性淋巴比值\n\n1.23\n参考值：.", "block_bbox": [74, 365, 1381, 7133]}], "layout_det_res": {"input_path": null, "page_index": null, "boxes": [{"cls_id": 5, "label": "content", "score": 0.6594232320785522, "coordinate": [74.0902099609375, 365.771728515625, 1381.0286865234375, 7133.4169921875]}, {"cls_id": 12, "label": "header", "score": 0.6296787858009338, "coordinate": [837.8397216796875, 37.88267517089844, 1335.4715576171875, 105.55720520019531]}]}, "overall_ocr_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_preprocessor": false, "use_textline_orientation": true}, "dt_polys": [[[68, 53], [202, 53], [202, 101], [68, 101]], [[834, 50], [881, 50], [881, 105], [834, 105]], [[888, 44], [981, 48], [979, 89], [886, 84]], [[1067, 46], [1157, 46], [1157, 107], [1067, 107]], [[476, 57], [534, 57], [534, 94], [476, 94]], [[1153, 50], [1230, 50], [1230, 103], [1153, 103]], [[898, 75], [971, 75], [971, 109], [898, 109]], [[591, 182], [844, 182], [844, 252], [591, 252]], [[34, 208], [111, 208], [111, 284], [34, 284]], [[518, 267], [919, 267], [919, 311], [518, 311]], [[32, 353], [291, 353], [291, 405], [32, 405]], [[77, 649], [458, 662], [455, 752], [74, 739]], [[94, 845], [844, 845], [844, 900], [94, 900]], [[92, 946], [776, 946], [776, 1002], [92, 1002]], [[86, 1224], [339, 1224], [339, 1294], [86, 1294]], [[84, 1411], [297, 1404], [299, 1463], [86, 1470]], [[1121, 1408], [1350, 1408], [1350, 1467], [1121, 1467]], [[94, 1561], [345, 1561], [345, 1613], [94, 1613]], [[1084, 1600], [1352, 1600], [1352, 1659], [1084, 1659]], [[90, 1653], [560, 1651], [561, 1696], [90, 1697]], [[92, 1795], [341, 1795], [341, 1847], [92, 1847]], [[1191, 1834], [1354, 1834], [1354, 1894], [1191, 1894]], [[90, 1885], [495, 1889], [495, 1933], [90, 1929]], [[92, 2029], [339, 2029], [339, 2080], [92, 2080]], [[1221, 2066], [1354, 2066], [1354, 2126], [1221, 2126]], [[94, 2121], [422, 2121], [422, 2159], [94, 2159]], [[94, 2263], [388, 2263], [388, 2312], [94, 2312]], [[1191, 2301], [1352, 2301], [1352, 2360], [1191, 2360]], [[94, 2356], [422, 2356], [422, 2395], [94, 2395]], [[92, 2496], [343, 2496], [343, 2548], [92, 2548]], [[1087, 2535], [1348, 2535], [1348, 2587], [1087, 2587]], [[90, 2587], [540, 2585], [540, 2629], [90, 2631]], [[92, 2730], [343, 2730], [343, 2780], [92, 2780]], [[1082, 2767], [1348, 2767], [1348, 2819], [1082, 2819]], [[90, 2820], [551, 2819], [551, 2863], [90, 2865]], [[96, 2966], [390, 2966], [390, 3010], [96, 3010]], [[1087, 3001], [1352, 3001], [1352, 3058], [1087, 3058]], [[94, 3056], [551, 3056], [551, 3095], [94, 3095]], [[89, 3194], [343, 3198], [343, 3250], [88, 3245]], [[1039, 3233], [1352, 3233], [1352, 3290], [1039, 3290]], [[969, 3246], [1005, 3246], [1005, 3284], [969, 3284]], [[92, 3288], [564, 3288], [564, 3330], [92, 3330]], [[90, 3428], [292, 3428], [292, 3479], [90, 3479]], [[1116, 3473], [1171, 3468], [1177, 3521], [1122, 3527]], [[1194, 3465], [1356, 3465], [1356, 3533], [1194, 3533]], [[92, 3522], [461, 3522], [461, 3566], [92, 3566]], [[90, 3664], [345, 3664], [345, 3713], [90, 3713]], [[1121, 3706], [1170, 3706], [1170, 3759], [1121, 3759]], [[1191, 3700], [1352, 3700], [1352, 3759], [1191, 3759]], [[94, 3757], [472, 3757], [472, 3794], [94, 3794]], [[92, 3894], [390, 3894], [390, 3943], [92, 3943]], [[1222, 3932], [1356, 3932], [1356, 3993], [1222, 3993]], [[94, 3991], [461, 3991], [461, 4028], [94, 4028]], [[90, 4133], [394, 4133], [394, 4177], [90, 4177]], [[1181, 4161], [1357, 4173], [1353, 4237], [1176, 4226]], [[1116, 4174], [1155, 4174], [1155, 4218], [1116, 4218]], [[90, 4219], [499, 4223], [498, 4268], [90, 4264]], [[92, 4363], [394, 4367], [393, 4413], [92, 4409]], [[1088, 4407], [1129, 4407], [1129, 4453], [1088, 4453]], [[1153, 4398], [1356, 4398], [1356, 4464], [1153, 4464]], [[94, 4455], [474, 4455], [474, 4494], [94, 4494]], [[90, 4597], [343, 4597], [343, 4647], [90, 4647]], [[1078, 4634], [1352, 4634], [1352, 4691], [1078, 4691]], [[92, 4691], [577, 4689], [578, 4728], [92, 4730]], [[92, 4831], [377, 4831], [377, 4880], [92, 4880]], [[1236, 4864], [1356, 4864], [1356, 4927], [1236, 4927]], [[94, 4925], [441, 4925], [441, 4963], [94, 4963]], [[90, 5061], [341, 5061], [341, 5112], [90, 5112]], [[1204, 5101], [1354, 5101], [1354, 5160], [1204, 5160]], [[94, 5158], [484, 5158], [484, 5197], [94, 5197]], [[88, 5297], [377, 5297], [377, 5346], [88, 5346]], [[1168, 5339], [1219, 5339], [1219, 5390], [1168, 5390]], [[1239, 5337], [1352, 5337], [1352, 5394], [1239, 5394]], [[92, 5392], [401, 5392], [401, 5429], [92, 5429]], [[92, 5530], [392, 5530], [392, 5580], [92, 5580]], [[1052, 5569], [1350, 5569], [1350, 5621], [1052, 5621]], [[92, 5622], [609, 5622], [609, 5665], [92, 5665]], [[92, 5764], [390, 5764], [390, 5814], [92, 5814]], [[1200, 5803], [1354, 5803], [1354, 5862], [1200, 5862]], [[92, 5858], [442, 5858], [442, 5897], [92, 5897]], [[94, 6000], [392, 6000], [392, 6044], [94, 6044]], [[1054, 6037], [1348, 6037], [1348, 6088], [1054, 6088]], [[94, 6092], [617, 6092], [617, 6129], [94, 6129]], [[92, 6230], [390, 6230], [390, 6280], [92, 6280]], [[1187, 6269], [1352, 6269], [1352, 6328], [1187, 6328]], [[94, 6326], [489, 6326], [489, 6364], [94, 6364]], [[90, 6464], [476, 6464], [476, 6513], [90, 6513]], [[1288, 6502], [1356, 6502], [1356, 6561], [1288, 6561]], [[92, 6556], [444, 6556], [444, 6600], [92, 6600]], [[88, 6699], [482, 6699], [482, 6749], [88, 6749]], [[1204, 6734], [1354, 6734], [1354, 6795], [1204, 6795]], [[90, 6788], [472, 6788], [472, 6832], [90, 6832]], [[92, 6930], [394, 6930], [394, 6981], [92, 6981]], [[1245, 6966], [1356, 6966], [1356, 7031], [1245, 7031]], [[88, 7020], [276, 7020], [276, 7071], [88, 7071]]], "text_det_params": {"limit_side_len": 736, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.0, "rec_texts": ["14:30", "8", "0.00", "5", "bilibii", "5G", "KB/S", "报告查询", "X", "fwcs.linkingcloud.cn", "东院XN9000", "*春香***岁", "申请科室：胰腺肿瘤临床研究门诊", "报告日期：2025-5-210:00:00", "项目明细", "项目名称", "结果/单位", "白细胞计数", "5.5*10^9/L", "参考值：3.5-9.5*10^9/L", "淋巴细胞%", "39.4%", "参考值：20.0-50.0%", "单核细胞%", "8.8%", "参考值：3.0-10%", "中性粒细胞%", "49.8%", "参考值：40-75%", "淋巴细胞数", "2.2*10^9/L", "参考值：1.1-3.2*10^9/L", "单核细胞数", "0.5*10^9/L", "参考值：0.1-0.6*10^9/L", "中性粒细胞数", "2.7*10^9/L", "参考值：1.8-6.3*10^9/L", "红细胞计数", "3.75*10^12/L", "↓", "参考值：3.8-5.1*10^12/L", "血红蛋白", "山", "100g/l", "参考值：115-150g/l", "红细胞压积", "", "32.3%", "参考值：女35-45%", "平均RBC体积", "86.1fl", "参考值：82.0-100fl", "RBC平均HGB", "26.7pg", "↓", "参考值：27.0-34.0pg", "平均HGB浓度", "↓", "310.0g/l", "参考值：316-354g/", "血小板计数", "182*10^9/L", "参考值：125-350*10^9/L", "平均PLT容积", "9.5fl", "参考值：7.4-10.4f", "血小板压积", "0.17%", "参考值：0.15-0.30%", "PLT分布宽度", "", "10%", "参考值：13-21%", "嗜酸性细胞数", "0.09*10^9/L", "参考值：0.02-0.52*10^9/L", "嗜酸性细胞%", "1.60%", "参考值：0.4-8.0%", "嗜碱性细胞数", "0.02*10^9/L", "参考值：0.00-0.06*10^9/L", "嗜碱性细胞%", "0.40%", "参考值：0.00-1.00%", "RBC分布宽度-SD", "41", "参考值：35-44FL", "RBC分布宽度-CV", "13.2%", "参考值：11.6-14.4%", "中性淋巴比值", "1.23", "参考值：."], "rec_scores": [0.9806796908378601, 0.40748342871665955, 0.9984455108642578, 0.8608766794204712, 0.6564812064170837, 0.983217716217041, 0.9760918617248535, 0.9997730255126953, 0.6428521275520325, 0.9991809129714966, 0.9986352920532227, 0.9270957112312317, 0.9885369539260864, 0.9849369525909424, 0.9995481967926025, 0.9998537302017212, 0.9985130429267883, 0.9994176030158997, 0.9821575880050659, 0.9627956748008728, 0.9980791211128235, 0.9994876980781555, 0.9953334927558899, 0.9991884231567383, 0.9994223117828369, 0.9857224822044373, 0.9964346289634705, 0.9995626211166382, 0.9898289442062378, 0.9995487332344055, 0.980848491191864, 0.9797593355178833, 0.9999176859855652, 0.953438401222229, 0.9604651927947998, 0.9972400665283203, 0.9809108972549438, 0.9815853834152222, 0.9996854662895203, 0.961536169052124, 0.9881325364112854, 0.9709434509277344, 0.9997267127037048, 0.6395927667617798, 0.9820961356163025, 0.9883784055709839, 0.9998186230659485, 0.0, 0.9989055395126343, 0.9885497689247131, 0.9994538426399231, 0.96843022108078, 0.992889404296875, 0.995805025100708, 0.9992653727531433, 0.986416220664978, 0.9897065758705139, 0.9988034963607788, 0.9889178276062012, 0.956344723701477, 0.9716319441795349, 0.9991127252578735, 0.9783014059066772, 0.9823284149169922, 0.9993523359298706, 0.9902780652046204, 0.9829488396644592, 0.9993971586227417, 0.9995177388191223, 0.9949955940246582, 0.998475968837738, 0.0, 0.9912691712379456, 0.989798903465271, 0.9996885657310486, 0.9652161002159119, 0.9789049029350281, 0.9968424439430237, 0.9979059100151062, 0.9887008666992188, 0.9997929930686951, 0.9615840315818787, 0.9825809597969055, 0.9965255260467529, 0.9994252920150757, 0.9913684725761414, 0.9973845481872559, 0.9995782375335693, 0.9834303259849548, 0.9948217272758484, 0.9995290040969849, 0.9958301186561584, 0.998322069644928, 0.9996522665023804, 0.9797202944755554], "rec_polys": [[[68, 53], [202, 53], [202, 101], [68, 101]], [[834, 50], [881, 50], [881, 105], [834, 105]], [[888, 44], [981, 48], [979, 89], [886, 84]], [[1067, 46], [1157, 46], [1157, 107], [1067, 107]], [[476, 57], [534, 57], [534, 94], [476, 94]], [[1153, 50], [1230, 50], [1230, 103], [1153, 103]], [[898, 75], [971, 75], [971, 109], [898, 109]], [[591, 182], [844, 182], [844, 252], [591, 252]], [[34, 208], [111, 208], [111, 284], [34, 284]], [[518, 267], [919, 267], [919, 311], [518, 311]], [[32, 353], [291, 353], [291, 405], [32, 405]], [[77, 649], [458, 662], [455, 752], [74, 739]], [[94, 845], [844, 845], [844, 900], [94, 900]], [[92, 946], [776, 946], [776, 1002], [92, 1002]], [[86, 1224], [339, 1224], [339, 1294], [86, 1294]], [[84, 1411], [297, 1404], [299, 1463], [86, 1470]], [[1121, 1408], [1350, 1408], [1350, 1467], [1121, 1467]], [[94, 1561], [345, 1561], [345, 1613], [94, 1613]], [[1084, 1600], [1352, 1600], [1352, 1659], [1084, 1659]], [[90, 1653], [560, 1651], [561, 1696], [90, 1697]], [[92, 1795], [341, 1795], [341, 1847], [92, 1847]], [[1191, 1834], [1354, 1834], [1354, 1894], [1191, 1894]], [[90, 1885], [495, 1889], [495, 1933], [90, 1929]], [[92, 2029], [339, 2029], [339, 2080], [92, 2080]], [[1221, 2066], [1354, 2066], [1354, 2126], [1221, 2126]], [[94, 2121], [422, 2121], [422, 2159], [94, 2159]], [[94, 2263], [388, 2263], [388, 2312], [94, 2312]], [[1191, 2301], [1352, 2301], [1352, 2360], [1191, 2360]], [[94, 2356], [422, 2356], [422, 2395], [94, 2395]], [[92, 2496], [343, 2496], [343, 2548], [92, 2548]], [[1087, 2535], [1348, 2535], [1348, 2587], [1087, 2587]], [[90, 2587], [540, 2585], [540, 2629], [90, 2631]], [[92, 2730], [343, 2730], [343, 2780], [92, 2780]], [[1082, 2767], [1348, 2767], [1348, 2819], [1082, 2819]], [[90, 2820], [551, 2819], [551, 2863], [90, 2865]], [[96, 2966], [390, 2966], [390, 3010], [96, 3010]], [[1087, 3001], [1352, 3001], [1352, 3058], [1087, 3058]], [[94, 3056], [551, 3056], [551, 3095], [94, 3095]], [[89, 3194], [343, 3198], [343, 3250], [88, 3245]], [[1039, 3233], [1352, 3233], [1352, 3290], [1039, 3290]], [[969, 3246], [1005, 3246], [1005, 3284], [969, 3284]], [[92, 3288], [564, 3288], [564, 3330], [92, 3330]], [[90, 3428], [292, 3428], [292, 3479], [90, 3479]], [[1116, 3473], [1171, 3468], [1177, 3521], [1122, 3527]], [[1194, 3465], [1356, 3465], [1356, 3533], [1194, 3533]], [[92, 3522], [461, 3522], [461, 3566], [92, 3566]], [[90, 3664], [345, 3664], [345, 3713], [90, 3713]], [[1121, 3706], [1170, 3706], [1170, 3759], [1121, 3759]], [[1191, 3700], [1352, 3700], [1352, 3759], [1191, 3759]], [[94, 3757], [472, 3757], [472, 3794], [94, 3794]], [[92, 3894], [390, 3894], [390, 3943], [92, 3943]], [[1222, 3932], [1356, 3932], [1356, 3993], [1222, 3993]], [[94, 3991], [461, 3991], [461, 4028], [94, 4028]], [[90, 4133], [394, 4133], [394, 4177], [90, 4177]], [[1181, 4161], [1357, 4173], [1353, 4237], [1176, 4226]], [[1116, 4174], [1155, 4174], [1155, 4218], [1116, 4218]], [[90, 4219], [499, 4223], [498, 4268], [90, 4264]], [[92, 4363], [394, 4367], [393, 4413], [92, 4409]], [[1088, 4407], [1129, 4407], [1129, 4453], [1088, 4453]], [[1153, 4398], [1356, 4398], [1356, 4464], [1153, 4464]], [[94, 4455], [474, 4455], [474, 4494], [94, 4494]], [[90, 4597], [343, 4597], [343, 4647], [90, 4647]], [[1078, 4634], [1352, 4634], [1352, 4691], [1078, 4691]], [[92, 4691], [577, 4689], [578, 4728], [92, 4730]], [[92, 4831], [377, 4831], [377, 4880], [92, 4880]], [[1236, 4864], [1356, 4864], [1356, 4927], [1236, 4927]], [[94, 4925], [441, 4925], [441, 4963], [94, 4963]], [[90, 5061], [341, 5061], [341, 5112], [90, 5112]], [[1204, 5101], [1354, 5101], [1354, 5160], [1204, 5160]], [[94, 5158], [484, 5158], [484, 5197], [94, 5197]], [[88, 5297], [377, 5297], [377, 5346], [88, 5346]], [[1168, 5339], [1219, 5339], [1219, 5390], [1168, 5390]], [[1239, 5337], [1352, 5337], [1352, 5394], [1239, 5394]], [[92, 5392], [401, 5392], [401, 5429], [92, 5429]], [[92, 5530], [392, 5530], [392, 5580], [92, 5580]], [[1052, 5569], [1350, 5569], [1350, 5621], [1052, 5621]], [[92, 5622], [609, 5622], [609, 5665], [92, 5665]], [[92, 5764], [390, 5764], [390, 5814], [92, 5814]], [[1200, 5803], [1354, 5803], [1354, 5862], [1200, 5862]], [[92, 5858], [442, 5858], [442, 5897], [92, 5897]], [[94, 6000], [392, 6000], [392, 6044], [94, 6044]], [[1054, 6037], [1348, 6037], [1348, 6088], [1054, 6088]], [[94, 6092], [617, 6092], [617, 6129], [94, 6129]], [[92, 6230], [390, 6230], [390, 6280], [92, 6280]], [[1187, 6269], [1352, 6269], [1352, 6328], [1187, 6328]], [[94, 6326], [489, 6326], [489, 6364], [94, 6364]], [[90, 6464], [476, 6464], [476, 6513], [90, 6513]], [[1288, 6502], [1356, 6502], [1356, 6561], [1288, 6561]], [[92, 6556], [444, 6556], [444, 6600], [92, 6600]], [[88, 6699], [482, 6699], [482, 6749], [88, 6749]], [[1204, 6734], [1354, 6734], [1354, 6795], [1204, 6795]], [[90, 6788], [472, 6788], [472, 6832], [90, 6832]], [[92, 6930], [394, 6930], [394, 6981], [92, 6981]], [[1245, 6966], [1356, 6966], [1356, 7031], [1245, 7031]], [[88, 7020], [276, 7020], [276, 7071], [88, 7071]]], "rec_boxes": [[68, 53, 202, 101], [834, 50, 881, 105], [886, 44, 981, 89], [1067, 46, 1157, 107], [476, 57, 534, 94], [1153, 50, 1230, 103], [898, 75, 971, 109], [591, 182, 844, 252], [34, 208, 111, 284], [518, 267, 919, 311], [32, 353, 291, 405], [74, 649, 458, 752], [94, 845, 844, 900], [92, 946, 776, 1002], [86, 1224, 339, 1294], [84, 1404, 299, 1470], [1121, 1408, 1350, 1467], [94, 1561, 345, 1613], [1084, 1600, 1352, 1659], [90, 1651, 561, 1697], [92, 1795, 341, 1847], [1191, 1834, 1354, 1894], [90, 1885, 495, 1933], [92, 2029, 339, 2080], [1221, 2066, 1354, 2126], [94, 2121, 422, 2159], [94, 2263, 388, 2312], [1191, 2301, 1352, 2360], [94, 2356, 422, 2395], [92, 2496, 343, 2548], [1087, 2535, 1348, 2587], [90, 2585, 540, 2631], [92, 2730, 343, 2780], [1082, 2767, 1348, 2819], [90, 2819, 551, 2865], [96, 2966, 390, 3010], [1087, 3001, 1352, 3058], [94, 3056, 551, 3095], [88, 3194, 343, 3250], [1039, 3233, 1352, 3290], [969, 3246, 1005, 3284], [92, 3288, 564, 3330], [90, 3428, 292, 3479], [1116, 3468, 1177, 3527], [1194, 3465, 1356, 3533], [92, 3522, 461, 3566], [90, 3664, 345, 3713], [1121, 3706, 1170, 3759], [1191, 3700, 1352, 3759], [94, 3757, 472, 3794], [92, 3894, 390, 3943], [1222, 3932, 1356, 3993], [94, 3991, 461, 4028], [90, 4133, 394, 4177], [1176, 4161, 1357, 4237], [1116, 4174, 1155, 4218], [90, 4219, 499, 4268], [92, 4363, 394, 4413], [1088, 4407, 1129, 4453], [1153, 4398, 1356, 4464], [94, 4455, 474, 4494], [90, 4597, 343, 4647], [1078, 4634, 1352, 4691], [92, 4689, 578, 4730], [92, 4831, 377, 4880], [1236, 4864, 1356, 4927], [94, 4925, 441, 4963], [90, 5061, 341, 5112], [1204, 5101, 1354, 5160], [94, 5158, 484, 5197], [88, 5297, 377, 5346], [1168, 5339, 1219, 5390], [1239, 5337, 1352, 5394], [92, 5392, 401, 5429], [92, 5530, 392, 5580], [1052, 5569, 1350, 5621], [92, 5622, 609, 5665], [92, 5764, 390, 5814], [1200, 5803, 1354, 5862], [92, 5858, 442, 5897], [94, 6000, 392, 6044], [1054, 6037, 1348, 6088], [94, 6092, 617, 6129], [92, 6230, 390, 6280], [1187, 6269, 1352, 6328], [94, 6326, 489, 6364], [90, 6464, 476, 6513], [1288, 6502, 1356, 6561], [92, 6556, 444, 6600], [88, 6699, 482, 6749], [1204, 6734, 1354, 6795], [90, 6788, 472, 6832], [92, 6930, 394, 6981], [1245, 6966, 1356, 7031], [88, 7020, 276, 7071]]}}