{"input_path": "D:\\dev\\medicalReport\\ocr\\source\\0604.jpg", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": true, "use_doc_unwarping": true}, "angle": 0}, "dt_polys": [[[73, 252], [469, 361], [443, 451], [47, 343]], [[74, 456], [866, 570], [851, 671], [59, 557]], [[72, 564], [779, 671], [764, 764], [57, 658]], [[75, 894], [339, 940], [326, 1012], [62, 967]], [[71, 1087], [296, 1122], [286, 1189], [61, 1153]], [[1118, 1168], [1360, 1153], [1364, 1218], [1122, 1233]], [[78, 1250], [347, 1299], [334, 1364], [66, 1315]], [[72, 1351], [567, 1420], [558, 1483], [63, 1413]], [[1075, 1368], [1361, 1356], [1364, 1421], [1078, 1433]], [[1011, 1379], [1063, 1379], [1063, 1430], [1011, 1430]], [[73, 1506], [341, 1549], [330, 1612], [62, 1569]], [[74, 1609], [494, 1662], [487, 1712], [67, 1659]], [[1123, 1616], [1174, 1616], [1174, 1666], [1123, 1666]], [[1190, 1610], [1361, 1601], [1364, 1660], [1193, 1669]], [[75, 1758], [338, 1792], [330, 1851], [67, 1817]], [[75, 1858], [422, 1894], [417, 1946], [69, 1909]], [[1225, 1851], [1362, 1843], [1366, 1905], [1229, 1914]], [[77, 2012], [386, 2041], [381, 2099], [71, 2070]], [[1125, 2104], [1179, 2104], [1179, 2156], [1125, 2156]], [[1195, 2098], [1363, 2089], [1366, 2149], [1199, 2159]], [[74, 2112], [418, 2142], [413, 2192], [70, 2162]], [[74, 2267], [337, 2284], [333, 2343], [70, 2326]], [[1102, 2343], [1365, 2332], [1368, 2394], [1104, 2405]], [[75, 2371], [533, 2392], [531, 2436], [72, 2415]], [[74, 2519], [339, 2534], [335, 2591], [70, 2577]], [[1098, 2589], [1365, 2577], [1368, 2639], [1101, 2652]], [[76, 2625], [544, 2640], [543, 2686], [75, 2671]], [[77, 2779], [387, 2794], [384, 2851], [74, 2836]], [[1075, 2842], [1365, 2829], [1368, 2891], [1078, 2904]], [[1014, 2854], [1059, 2854], [1059, 2896], [1014, 2896]], [[76, 2884], [546, 2896], [545, 2942], [75, 2931]], [[70, 3037], [336, 3044], [335, 3103], [69, 3096]], [[1036, 3096], [1361, 3081], [1364, 3138], [1039, 3153]], [[969, 3108], [1003, 3108], [1003, 3146], [969, 3146]], [[76, 3142], [557, 3152], [556, 3196], [75, 3186]], [[72, 3293], [284, 3298], [282, 3357], [71, 3352]], [[1149, 3327], [1377, 3322], [1379, 3409], [1151, 3414]], [[74, 3396], [454, 3402], [453, 3452], [73, 3446]], [[73, 3557], [336, 3557], [336, 3608], [73, 3608]], [[1196, 3589], [1367, 3584], [1369, 3650], [1198, 3655]], [[1131, 3601], [1174, 3601], [1174, 3645], [1131, 3645]], [[75, 3656], [466, 3650], [467, 3695], [75, 3701]], [[75, 3804], [384, 3800], [385, 3851], [75, 3855]], [[1218, 3839], [1369, 3834], [1371, 3897], [1220, 3902]], [[75, 3901], [453, 3895], [454, 3940], [75, 3945]], [[71, 4051], [387, 4042], [389, 4094], [72, 4102]], [[1200, 4086], [1373, 4093], [1371, 4160], [1197, 4152]], [[71, 4144], [493, 4140], [493, 4190], [72, 4194]], [[75, 4297], [388, 4297], [388, 4347], [75, 4347]], [[1159, 4328], [1374, 4328], [1374, 4400], [1159, 4400]], [[75, 4393], [468, 4389], [469, 4433], [75, 4437]], [[75, 4540], [334, 4540], [334, 4591], [75, 4591]], [[1005, 4588], [1046, 4588], [1046, 4632], [1005, 4632]], [[1076, 4580], [1367, 4580], [1367, 4637], [1076, 4637]], [[73, 4637], [574, 4641], [574, 4684], [73, 4680]], [[74, 4788], [368, 4796], [366, 4848], [73, 4840]], [[1248, 4828], [1373, 4821], [1377, 4891], [1252, 4898]], [[74, 4886], [435, 4892], [434, 4936], [73, 4930]], [[71, 5031], [336, 5039], [334, 5096], [69, 5088]], [[1146, 5087], [1204, 5087], [1204, 5146], [1146, 5146]], [[1220, 5086], [1367, 5079], [1370, 5139], [1223, 5146]], [[74, 5132], [477, 5142], [475, 5186], [73, 5177]], [[70, 5283], [370, 5291], [368, 5341], [69, 5333]], [[1176, 5339], [1234, 5339], [1234, 5398], [1176, 5398]], [[1246, 5332], [1372, 5327], [1375, 5397], [1249, 5402]], [[74, 5381], [394, 5387], [393, 5431], [73, 5425]], [[73, 5524], [387, 5535], [385, 5592], [71, 5581]], [[1055, 5583], [1369, 5590], [1368, 5652], [1053, 5646]], [[74, 5626], [606, 5634], [605, 5678], [73, 5670]], [[72, 5775], [383, 5781], [382, 5838], [71, 5832]], [[1214, 5850], [1367, 5843], [1370, 5905], [1217, 5912]], [[72, 5874], [437, 5880], [436, 5926], [71, 5920]], [[74, 6027], [385, 6033], [384, 6085], [73, 6079]], [[1064, 6090], [1369, 6096], [1368, 6159], [1063, 6152]], [[75, 6129], [611, 6129], [611, 6173], [75, 6173]], [[75, 6281], [379, 6281], [379, 6333], [75, 6333]], [[1198, 6350], [1371, 6350], [1371, 6410], [1198, 6410]], [[77, 6385], [480, 6385], [480, 6423], [77, 6423]], [[67, 6536], [471, 6524], [473, 6579], [69, 6591]], [[1301, 6596], [1374, 6596], [1374, 6663], [1301, 6663]], [[69, 6635], [436, 6623], [437, 6673], [71, 6685]], [[67, 6790], [475, 6774], [477, 6831], [69, 6847]], [[1215, 6849], [1371, 6849], [1371, 6909], [1215, 6909]], [[69, 6888], [465, 6872], [468, 6922], [71, 6938]], [[70, 7035], [386, 7021], [389, 7083], [73, 7098]], [[1245, 7088], [1376, 7088], [1376, 7160], [1245, 7160]], [[65, 7131], [268, 7124], [271, 7188], [67, 7195]]], "text_det_params": {"limit_side_len": 64, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.0, "rec_texts": ["*春香***岁", "申请科室：胰腺肿瘤临床研究门诊", "报告日期：2025-6-40:00:00", "项目明细", "项目名称", "结果/单位", "白细胞计数", "参考值：3.5-9.5*10^9/L", "2.4*10^9/L", "", "淋巴细胞%", "参考值：20.0-50.0%", "↑", "69.9%", "单核细胞%", "参考值：3.0-10%", "3.8%", "中性粒细胞%", "", "24.2%", "参考值：40-75%", "淋巴细胞数", "1.7*10^9/L", "参考值：1.1-3.2*10^9/L", "单核细胞数", "0.1*10^9/L", "参考值：0.1-0.6*10^9/L", "中性粒细胞数", "0.6*10^9/L", "↑", "参考值：1.8-6.3*10^9/L", "红细胞计数", "3.29*10^12/L", "↓", "参考值：3.8-5.1*10^12/L", "血红蛋白", "↓91g/l", "参考值：115-150g/l", "红细胞压积", "28.3%", "↑", "参考值：女35-45%", "平均RBC体积", "86.0fl", "参考值：82.0-100fl", "RBC平均HGB", "27.7pg", "参考值：27.0-34.0pg", "平均HGB浓度", "322.0g/l", "参考值：316-354g/l", "血小板计数", "↑", "109*10^9/L", "参考值：125-350*10^9/L", "平均PLT容积", "9.8fl", "参考值：7.4-10.4fl", "血小板压积", "", "0.11%", "参考值：0.15-0.30%", "PLT分布宽度", "", "10%", "参考值：13-21%", "嗜酸性细胞数", "0.04*10^9/L", "参考值：0.02-0.52*10^9/L", "嗜酸性细胞%", "1.70%", "参考值：0.4-8.0%", "嗜碱性细胞数", "0.01*10^9/L", "参考值：0.00-0.06*10^9/L", "嗜碱性细胞%", "0.40%", "参考值：0.00-1.00%", "RBC分布宽度-SD", "41", "参考值：35-44FL", "RBC分布宽度-CV", "13.2%", "参考值：11.6-14.4%", "中性淋巴比值", "0.35", "参考值：."], "rec_scores": [0.9515231251716614, 0.9964405298233032, 0.9708669781684875, 0.9994625449180603, 0.9998653531074524, 0.9966087341308594, 0.9989228248596191, 0.9825864434242249, 0.9606054425239563, 0.0, 0.9973623156547546, 0.9974837303161621, 0.9060308337211609, 0.9972541928291321, 0.9996464848518372, 0.9969483017921448, 0.9994876384735107, 0.9991471171379089, 0.0, 0.9984533190727234, 0.9947308301925659, 0.999416172504425, 0.9639577865600586, 0.9155969619750977, 0.999906063079834, 0.9656949043273926, 0.9373112916946411, 0.9994447827339172, 0.9718919992446899, 0.9754436016082764, 0.9852966666221619, 0.9995372891426086, 0.968487024307251, 0.9943341016769409, 0.9476543068885803, 0.9994879961013794, 0.8922902941703796, 0.98021000623703, 0.9998415112495422, 0.9991669654846191, 0.8098567724227905, 0.9878358840942383, 0.9995327591896057, 0.9830217957496643, 0.9767764210700989, 0.9996843934059143, 0.9993863105773926, 0.9952009916305542, 0.9993061423301697, 0.9880786538124084, 0.948775589466095, 0.9993295669555664, 0.8251527547836304, 0.968413233757019, 0.9778037667274475, 0.9992236495018005, 0.989392101764679, 0.958271861076355, 0.9996101260185242, 0.0, 0.9969655275344849, 0.9928620457649231, 0.9976416826248169, 0.0, 0.9944697022438049, 0.9936317205429077, 0.9997686743736267, 0.9731090664863586, 0.9768818616867065, 0.9971736073493958, 0.9974080920219421, 0.9937219619750977, 0.9995973706245422, 0.9873519539833069, 0.9808371663093567, 0.9978321194648743, 0.9990372657775879, 0.98887699842453, 0.9985766410827637, 0.9994806051254272, 0.9944483041763306, 0.995673656463623, 0.9996330142021179, 0.9960284233093262, 0.9967843890190125, 0.999672532081604, 0.9652169942855835], "rec_polys": [[[73, 252], [469, 361], [443, 451], [47, 343]], [[74, 456], [866, 570], [851, 671], [59, 557]], [[72, 564], [779, 671], [764, 764], [57, 658]], [[75, 894], [339, 940], [326, 1012], [62, 967]], [[71, 1087], [296, 1122], [286, 1189], [61, 1153]], [[1118, 1168], [1360, 1153], [1364, 1218], [1122, 1233]], [[78, 1250], [347, 1299], [334, 1364], [66, 1315]], [[72, 1351], [567, 1420], [558, 1483], [63, 1413]], [[1075, 1368], [1361, 1356], [1364, 1421], [1078, 1433]], [[1011, 1379], [1063, 1379], [1063, 1430], [1011, 1430]], [[73, 1506], [341, 1549], [330, 1612], [62, 1569]], [[74, 1609], [494, 1662], [487, 1712], [67, 1659]], [[1123, 1616], [1174, 1616], [1174, 1666], [1123, 1666]], [[1190, 1610], [1361, 1601], [1364, 1660], [1193, 1669]], [[75, 1758], [338, 1792], [330, 1851], [67, 1817]], [[75, 1858], [422, 1894], [417, 1946], [69, 1909]], [[1225, 1851], [1362, 1843], [1366, 1905], [1229, 1914]], [[77, 2012], [386, 2041], [381, 2099], [71, 2070]], [[1125, 2104], [1179, 2104], [1179, 2156], [1125, 2156]], [[1195, 2098], [1363, 2089], [1366, 2149], [1199, 2159]], [[74, 2112], [418, 2142], [413, 2192], [70, 2162]], [[74, 2267], [337, 2284], [333, 2343], [70, 2326]], [[1102, 2343], [1365, 2332], [1368, 2394], [1104, 2405]], [[75, 2371], [533, 2392], [531, 2436], [72, 2415]], [[74, 2519], [339, 2534], [335, 2591], [70, 2577]], [[1098, 2589], [1365, 2577], [1368, 2639], [1101, 2652]], [[76, 2625], [544, 2640], [543, 2686], [75, 2671]], [[77, 2779], [387, 2794], [384, 2851], [74, 2836]], [[1075, 2842], [1365, 2829], [1368, 2891], [1078, 2904]], [[1014, 2854], [1059, 2854], [1059, 2896], [1014, 2896]], [[76, 2884], [546, 2896], [545, 2942], [75, 2931]], [[70, 3037], [336, 3044], [335, 3103], [69, 3096]], [[1036, 3096], [1361, 3081], [1364, 3138], [1039, 3153]], [[969, 3108], [1003, 3108], [1003, 3146], [969, 3146]], [[76, 3142], [557, 3152], [556, 3196], [75, 3186]], [[72, 3293], [284, 3298], [282, 3357], [71, 3352]], [[1149, 3327], [1377, 3322], [1379, 3409], [1151, 3414]], [[74, 3396], [454, 3402], [453, 3452], [73, 3446]], [[73, 3557], [336, 3557], [336, 3608], [73, 3608]], [[1196, 3589], [1367, 3584], [1369, 3650], [1198, 3655]], [[1131, 3601], [1174, 3601], [1174, 3645], [1131, 3645]], [[75, 3656], [466, 3650], [467, 3695], [75, 3701]], [[75, 3804], [384, 3800], [385, 3851], [75, 3855]], [[1218, 3839], [1369, 3834], [1371, 3897], [1220, 3902]], [[75, 3901], [453, 3895], [454, 3940], [75, 3945]], [[71, 4051], [387, 4042], [389, 4094], [72, 4102]], [[1200, 4086], [1373, 4093], [1371, 4160], [1197, 4152]], [[71, 4144], [493, 4140], [493, 4190], [72, 4194]], [[75, 4297], [388, 4297], [388, 4347], [75, 4347]], [[1159, 4328], [1374, 4328], [1374, 4400], [1159, 4400]], [[75, 4393], [468, 4389], [469, 4433], [75, 4437]], [[75, 4540], [334, 4540], [334, 4591], [75, 4591]], [[1005, 4588], [1046, 4588], [1046, 4632], [1005, 4632]], [[1076, 4580], [1367, 4580], [1367, 4637], [1076, 4637]], [[73, 4637], [574, 4641], [574, 4684], [73, 4680]], [[74, 4788], [368, 4796], [366, 4848], [73, 4840]], [[1248, 4828], [1373, 4821], [1377, 4891], [1252, 4898]], [[74, 4886], [435, 4892], [434, 4936], [73, 4930]], [[71, 5031], [336, 5039], [334, 5096], [69, 5088]], [[1146, 5087], [1204, 5087], [1204, 5146], [1146, 5146]], [[1220, 5086], [1367, 5079], [1370, 5139], [1223, 5146]], [[74, 5132], [477, 5142], [475, 5186], [73, 5177]], [[70, 5283], [370, 5291], [368, 5341], [69, 5333]], [[1176, 5339], [1234, 5339], [1234, 5398], [1176, 5398]], [[1246, 5332], [1372, 5327], [1375, 5397], [1249, 5402]], [[74, 5381], [394, 5387], [393, 5431], [73, 5425]], [[73, 5524], [387, 5535], [385, 5592], [71, 5581]], [[1055, 5583], [1369, 5590], [1368, 5652], [1053, 5646]], [[74, 5626], [606, 5634], [605, 5678], [73, 5670]], [[72, 5775], [383, 5781], [382, 5838], [71, 5832]], [[1214, 5850], [1367, 5843], [1370, 5905], [1217, 5912]], [[72, 5874], [437, 5880], [436, 5926], [71, 5920]], [[74, 6027], [385, 6033], [384, 6085], [73, 6079]], [[1064, 6090], [1369, 6096], [1368, 6159], [1063, 6152]], [[75, 6129], [611, 6129], [611, 6173], [75, 6173]], [[75, 6281], [379, 6281], [379, 6333], [75, 6333]], [[1198, 6350], [1371, 6350], [1371, 6410], [1198, 6410]], [[77, 6385], [480, 6385], [480, 6423], [77, 6423]], [[67, 6536], [471, 6524], [473, 6579], [69, 6591]], [[1301, 6596], [1374, 6596], [1374, 6663], [1301, 6663]], [[69, 6635], [436, 6623], [437, 6673], [71, 6685]], [[67, 6790], [475, 6774], [477, 6831], [69, 6847]], [[1215, 6849], [1371, 6849], [1371, 6909], [1215, 6909]], [[69, 6888], [465, 6872], [468, 6922], [71, 6938]], [[70, 7035], [386, 7021], [389, 7083], [73, 7098]], [[1245, 7088], [1376, 7088], [1376, 7160], [1245, 7160]], [[65, 7131], [268, 7124], [271, 7188], [67, 7195]]], "rec_boxes": [[47, 252, 469, 451], [59, 456, 866, 671], [57, 564, 779, 764], [62, 894, 339, 1012], [61, 1087, 296, 1189], [1118, 1153, 1364, 1233], [66, 1250, 347, 1364], [63, 1351, 567, 1483], [1075, 1356, 1364, 1433], [1011, 1379, 1063, 1430], [62, 1506, 341, 1612], [67, 1609, 494, 1712], [1123, 1616, 1174, 1666], [1190, 1601, 1364, 1669], [67, 1758, 338, 1851], [69, 1858, 422, 1946], [1225, 1843, 1366, 1914], [71, 2012, 386, 2099], [1125, 2104, 1179, 2156], [1195, 2089, 1366, 2159], [70, 2112, 418, 2192], [70, 2267, 337, 2343], [1102, 2332, 1368, 2405], [72, 2371, 533, 2436], [70, 2519, 339, 2591], [1098, 2577, 1368, 2652], [75, 2625, 544, 2686], [74, 2779, 387, 2851], [1075, 2829, 1368, 2904], [1014, 2854, 1059, 2896], [75, 2884, 546, 2942], [69, 3037, 336, 3103], [1036, 3081, 1364, 3153], [969, 3108, 1003, 3146], [75, 3142, 557, 3196], [71, 3293, 284, 3357], [1149, 3322, 1379, 3414], [73, 3396, 454, 3452], [73, 3557, 336, 3608], [1196, 3584, 1369, 3655], [1131, 3601, 1174, 3645], [75, 3650, 467, 3701], [75, 3800, 385, 3855], [1218, 3834, 1371, 3902], [75, 3895, 454, 3945], [71, 4042, 389, 4102], [1197, 4086, 1373, 4160], [71, 4140, 493, 4194], [75, 4297, 388, 4347], [1159, 4328, 1374, 4400], [75, 4389, 469, 4437], [75, 4540, 334, 4591], [1005, 4588, 1046, 4632], [1076, 4580, 1367, 4637], [73, 4637, 574, 4684], [73, 4788, 368, 4848], [1248, 4821, 1377, 4898], [73, 4886, 435, 4936], [69, 5031, 336, 5096], [1146, 5087, 1204, 5146], [1220, 5079, 1370, 5146], [73, 5132, 477, 5186], [69, 5283, 370, 5341], [1176, 5339, 1234, 5398], [1246, 5327, 1375, 5402], [73, 5381, 394, 5431], [71, 5524, 387, 5592], [1053, 5583, 1369, 5652], [73, 5626, 606, 5678], [71, 5775, 383, 5838], [1214, 5843, 1370, 5912], [71, 5874, 437, 5926], [73, 6027, 385, 6085], [1063, 6090, 1369, 6159], [75, 6129, 611, 6173], [75, 6281, 379, 6333], [1198, 6350, 1371, 6410], [77, 6385, 480, 6423], [67, 6524, 473, 6591], [1301, 6596, 1374, 6663], [69, 6623, 437, 6685], [67, 6774, 477, 6847], [1215, 6849, 1371, 6909], [69, 6872, 468, 6938], [70, 7021, 389, 7098], [1245, 7088, 1376, 7160], [65, 7124, 271, 7195]]}