{"input_path": "D:\\dev\\medicalReport\\ocr\\source\\0528.jpg", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": true, "use_doc_unwarping": true}, "angle": 0}, "dt_polys": [[[71, 245], [467, 347], [441, 443], [45, 341]], [[73, 453], [859, 564], [845, 657], [60, 546]], [[68, 554], [807, 650], [793, 757], [54, 660]], [[76, 893], [339, 937], [327, 1009], [64, 965]], [[72, 1085], [296, 1120], [285, 1187], [61, 1151]], [[1118, 1153], [1360, 1138], [1365, 1205], [1122, 1220]], [[78, 1248], [347, 1296], [335, 1360], [66, 1313]], [[73, 1353], [565, 1414], [557, 1471], [66, 1410]], [[1014, 1366], [1065, 1366], [1065, 1419], [1014, 1419]], [[1043, 1358], [1362, 1339], [1366, 1409], [1047, 1428]], [[74, 1506], [340, 1544], [331, 1608], [65, 1571]], [[75, 1609], [495, 1657], [489, 1707], [69, 1659]], [[1123, 1607], [1174, 1607], [1174, 1657], [1123, 1657]], [[1189, 1601], [1360, 1589], [1364, 1648], [1194, 1660]], [[75, 1760], [338, 1790], [331, 1847], [68, 1817]], [[1232, 1837], [1366, 1829], [1370, 1899], [1237, 1907]], [[75, 1858], [420, 1890], [415, 1942], [70, 1910]], [[76, 2013], [384, 2037], [379, 2095], [72, 2070]], [[1136, 2097], [1189, 2097], [1189, 2150], [1136, 2150]], [[1208, 2091], [1362, 2081], [1366, 2142], [1212, 2151]], [[74, 2112], [416, 2139], [412, 2188], [70, 2162]], [[73, 2267], [337, 2280], [334, 2339], [70, 2326]], [[1097, 2339], [1364, 2322], [1368, 2387], [1102, 2404]], [[74, 2371], [535, 2388], [533, 2432], [73, 2415]], [[75, 2520], [337, 2532], [334, 2587], [72, 2575]], [[1096, 2582], [1364, 2567], [1368, 2634], [1100, 2648]], [[76, 2623], [542, 2641], [541, 2687], [74, 2669]], [[76, 2779], [387, 2790], [385, 2845], [74, 2835]], [[1077, 2836], [1363, 2821], [1366, 2884], [1080, 2899]], [[1012, 2846], [1058, 2846], [1058, 2890], [1012, 2890]], [[74, 2881], [542, 2893], [541, 2942], [73, 2930]], [[71, 3039], [336, 3039], [336, 3097], [71, 3097]], [[1042, 3090], [1359, 3076], [1362, 3133], [1044, 3147]], [[977, 3100], [1016, 3100], [1016, 3144], [977, 3144]], [[75, 3141], [555, 3145], [555, 3189], [75, 3185]], [[75, 3295], [283, 3295], [283, 3352], [75, 3352]], [[1221, 3323], [1372, 3323], [1372, 3400], [1221, 3400]], [[1146, 3336], [1206, 3336], [1206, 3395], [1146, 3395]], [[77, 3402], [450, 3402], [450, 3441], [77, 3441]], [[75, 3555], [334, 3555], [334, 3605], [75, 3605]], [[1121, 3590], [1174, 3590], [1174, 3643], [1121, 3643]], [[1194, 3584], [1367, 3584], [1367, 3645], [1194, 3645]], [[75, 3653], [465, 3649], [465, 3691], [75, 3695]], [[77, 3800], [382, 3800], [382, 3850], [77, 3850]], [[1221, 3833], [1371, 3833], [1371, 3894], [1221, 3894]], [[73, 3900], [453, 3892], [454, 3936], [74, 3944]], [[71, 4049], [387, 4043], [388, 4092], [72, 4098]], [[1116, 4083], [1166, 4083], [1166, 4133], [1116, 4133]], [[1186, 4080], [1370, 4092], [1366, 4153], [1182, 4141]], [[73, 4141], [489, 4137], [490, 4186], [73, 4190]], [[77, 4293], [386, 4293], [386, 4343], [77, 4343]], [[1089, 4330], [1144, 4330], [1144, 4383], [1089, 4383]], [[1162, 4326], [1371, 4326], [1371, 4393], [1162, 4393]], [[75, 4391], [467, 4391], [467, 4435], [75, 4435]], [[69, 4535], [335, 4530], [336, 4589], [70, 4594]], [[1069, 4577], [1367, 4577], [1367, 4634], [1069, 4634]], [[73, 4636], [566, 4639], [566, 4682], [73, 4678]], [[74, 4786], [368, 4794], [366, 4846], [73, 4838]], [[1246, 4824], [1374, 4819], [1377, 4887], [1249, 4892]], [[74, 4884], [435, 4890], [434, 4934], [73, 4928]], [[71, 5029], [336, 5038], [334, 5096], [69, 5088]], [[1199, 5084], [1366, 5075], [1370, 5136], [1202, 5145]], [[74, 5132], [477, 5140], [476, 5185], [73, 5177]], [[72, 5281], [370, 5290], [368, 5341], [71, 5333]], [[1176, 5335], [1234, 5335], [1234, 5398], [1176, 5398]], [[1246, 5331], [1372, 5325], [1375, 5395], [1249, 5400]], [[74, 5379], [394, 5387], [393, 5431], [73, 5423]], [[73, 5524], [387, 5535], [385, 5592], [71, 5581]], [[1055, 5582], [1369, 5588], [1368, 5652], [1053, 5646]], [[74, 5624], [604, 5632], [603, 5678], [73, 5670]], [[72, 5775], [383, 5781], [382, 5838], [71, 5832]], [[1222, 5850], [1367, 5843], [1370, 5903], [1225, 5910]], [[72, 5876], [437, 5882], [436, 5928], [71, 5922]], [[74, 6027], [383, 6033], [382, 6085], [73, 6079]], [[1059, 6091], [1369, 6100], [1368, 6159], [1057, 6150]], [[73, 6127], [611, 6131], [611, 6175], [73, 6171]], [[75, 6283], [381, 6283], [381, 6333], [75, 6333]], [[1202, 6348], [1371, 6348], [1371, 6414], [1202, 6414]], [[73, 6385], [483, 6381], [484, 6425], [73, 6429]], [[69, 6536], [471, 6526], [473, 6583], [70, 6593]], [[1301, 6598], [1374, 6598], [1374, 6663], [1301, 6663]], [[71, 6639], [436, 6627], [437, 6671], [72, 6683]], [[67, 6792], [475, 6776], [477, 6831], [69, 6847]], [[1211, 6847], [1371, 6842], [1373, 6909], [1213, 6914]], [[69, 6888], [465, 6872], [468, 6923], [71, 6939]], [[70, 7035], [386, 7021], [389, 7085], [73, 7100]], [[1245, 7086], [1376, 7086], [1376, 7158], [1245, 7158]], [[67, 7131], [269, 7126], [270, 7191], [69, 7195]]], "text_det_params": {"limit_side_len": 64, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.0, "rec_texts": ["*春香***岁", "申请科室：胰腺肿瘤临床研究门诊", "报告日期：2025-5-28 0:00:00", "项目明细", "项目名称", "结果/单位", "白细胞计数", "参考值：3.5-9.5*10^9/L", "", "2.7*10^9/L", "淋巴细胞%", "参考值：20.0-50.0%", "↑", "62.4%", "单核细胞%", "4.1%", "参考值：3.0-10%", "中性粒细胞%", "", "31.7%", "参考值：40-75%", "淋巴细胞数", "1.7*10^9/L", "参考值：1.1-3.2*10^9/L", "单核细胞数", "0.1*10^9/L", "参考值：0.1-0.6*10^9/L", "中性粒细胞数", "0.9*10^9/L", "↑", "参考值：1.8-6.3*10^9/L", "红细胞计数", "3.51*10^12/L", "↓", "参考值：3.8-5.1*10^12/L", "血红蛋白", "94g/l", "", "参考值：115-150g/", "红细胞压积", "", "30.0%", "参考值：女35-45%", "平均RBC体积", "85.5fl", "参考值：82.0-100fl", "RBC平均HGB", "↓", "26.8pg", "参考值：27.0-34.0po", "平均HGB浓度", "", "313.0g/l", "参考值：316-354g/l", "血小板计数", "206*10^9/L", "参考值：125-350*10^9/", "平均PLT容积", "9.9fl", "参考值：7.4-10.4fl", "血小板压积", "0.20%", "参考值：0.15-0.30%", "PLT分布宽度", "!", "10%", "参考值：13-21%", "嗜酸性细胞数", "0.03*10^9/L", "参考值：0.02-0.52*10^9/L", "嗜酸性细胞%", "1.10%", "参考值：0.4-8.0%", "嗜碱性细胞数", "0.02*10^9/L", "参考值：0.00-0.06*109/L", "嗜碱性细胞%", "0.70%", "参考值：0.00-1.00%", "RBC分布宽度-SD", "41", "参考值：35-44FL", "RBC分布宽度-CV", "13.2%", "参考值：11.6-14.4%", "中性淋巴比值", "0.53", "参考值：."], "rec_scores": [0.8986653685569763, 0.993494987487793, 0.9548975229263306, 0.9995552897453308, 0.9998939037322998, 0.9970901608467102, 0.9991553425788879, 0.9788216352462769, 0.0, 0.9084585905075073, 0.9985748529434204, 0.9976457953453064, 0.7410409450531006, 0.9981318712234497, 0.9996768832206726, 0.9996376633644104, 0.9969192743301392, 0.9989050030708313, 0.0, 0.9987039566040039, 0.9955369830131531, 0.9992766380310059, 0.9682298898696899, 0.9654748439788818, 0.999935507774353, 0.9773005247116089, 0.9562962651252747, 0.9995482563972473, 0.9507161974906921, 0.9322686195373535, 0.9574446082115173, 0.9996877908706665, 0.9637036919593811, 0.975936233997345, 0.9528388977050781, 0.999515175819397, 0.9815632700920105, 0.0, 0.9870792627334595, 0.9998327493667603, 0.0, 0.9992982745170593, 0.9908682107925415, 0.9993758797645569, 0.9795606136322021, 0.9889302849769592, 0.9996075630187988, 0.9834137558937073, 0.9992904663085938, 0.9671056270599365, 0.9982748031616211, 0.0, 0.9570111036300659, 0.9556307792663574, 0.9994303584098816, 0.9797723889350891, 0.9823810458183289, 0.9991585612297058, 0.978171169757843, 0.952966570854187, 0.999547004699707, 0.9993502497673035, 0.9919437170028687, 0.999016284942627, 0.21371953189373016, 0.9876992106437683, 0.993475615978241, 0.999748945236206, 0.9811307787895203, 0.9694852828979492, 0.9984143376350403, 0.9965861439704895, 0.9936067461967468, 0.9993631839752197, 0.9877345561981201, 0.9885958433151245, 0.9983814358711243, 0.9991569519042969, 0.995249330997467, 0.9984423518180847, 0.9994792938232422, 0.9897702932357788, 0.9941233396530151, 0.999577522277832, 0.9964122772216797, 0.9980344176292419, 0.9995883107185364, 0.9619964361190796], "rec_polys": [[[71, 245], [467, 347], [441, 443], [45, 341]], [[73, 453], [859, 564], [845, 657], [60, 546]], [[68, 554], [807, 650], [793, 757], [54, 660]], [[76, 893], [339, 937], [327, 1009], [64, 965]], [[72, 1085], [296, 1120], [285, 1187], [61, 1151]], [[1118, 1153], [1360, 1138], [1365, 1205], [1122, 1220]], [[78, 1248], [347, 1296], [335, 1360], [66, 1313]], [[73, 1353], [565, 1414], [557, 1471], [66, 1410]], [[1014, 1366], [1065, 1366], [1065, 1419], [1014, 1419]], [[1043, 1358], [1362, 1339], [1366, 1409], [1047, 1428]], [[74, 1506], [340, 1544], [331, 1608], [65, 1571]], [[75, 1609], [495, 1657], [489, 1707], [69, 1659]], [[1123, 1607], [1174, 1607], [1174, 1657], [1123, 1657]], [[1189, 1601], [1360, 1589], [1364, 1648], [1194, 1660]], [[75, 1760], [338, 1790], [331, 1847], [68, 1817]], [[1232, 1837], [1366, 1829], [1370, 1899], [1237, 1907]], [[75, 1858], [420, 1890], [415, 1942], [70, 1910]], [[76, 2013], [384, 2037], [379, 2095], [72, 2070]], [[1136, 2097], [1189, 2097], [1189, 2150], [1136, 2150]], [[1208, 2091], [1362, 2081], [1366, 2142], [1212, 2151]], [[74, 2112], [416, 2139], [412, 2188], [70, 2162]], [[73, 2267], [337, 2280], [334, 2339], [70, 2326]], [[1097, 2339], [1364, 2322], [1368, 2387], [1102, 2404]], [[74, 2371], [535, 2388], [533, 2432], [73, 2415]], [[75, 2520], [337, 2532], [334, 2587], [72, 2575]], [[1096, 2582], [1364, 2567], [1368, 2634], [1100, 2648]], [[76, 2623], [542, 2641], [541, 2687], [74, 2669]], [[76, 2779], [387, 2790], [385, 2845], [74, 2835]], [[1077, 2836], [1363, 2821], [1366, 2884], [1080, 2899]], [[1012, 2846], [1058, 2846], [1058, 2890], [1012, 2890]], [[74, 2881], [542, 2893], [541, 2942], [73, 2930]], [[71, 3039], [336, 3039], [336, 3097], [71, 3097]], [[1042, 3090], [1359, 3076], [1362, 3133], [1044, 3147]], [[977, 3100], [1016, 3100], [1016, 3144], [977, 3144]], [[75, 3141], [555, 3145], [555, 3189], [75, 3185]], [[75, 3295], [283, 3295], [283, 3352], [75, 3352]], [[1221, 3323], [1372, 3323], [1372, 3400], [1221, 3400]], [[1146, 3336], [1206, 3336], [1206, 3395], [1146, 3395]], [[77, 3402], [450, 3402], [450, 3441], [77, 3441]], [[75, 3555], [334, 3555], [334, 3605], [75, 3605]], [[1121, 3590], [1174, 3590], [1174, 3643], [1121, 3643]], [[1194, 3584], [1367, 3584], [1367, 3645], [1194, 3645]], [[75, 3653], [465, 3649], [465, 3691], [75, 3695]], [[77, 3800], [382, 3800], [382, 3850], [77, 3850]], [[1221, 3833], [1371, 3833], [1371, 3894], [1221, 3894]], [[73, 3900], [453, 3892], [454, 3936], [74, 3944]], [[71, 4049], [387, 4043], [388, 4092], [72, 4098]], [[1116, 4083], [1166, 4083], [1166, 4133], [1116, 4133]], [[1186, 4080], [1370, 4092], [1366, 4153], [1182, 4141]], [[73, 4141], [489, 4137], [490, 4186], [73, 4190]], [[77, 4293], [386, 4293], [386, 4343], [77, 4343]], [[1089, 4330], [1144, 4330], [1144, 4383], [1089, 4383]], [[1162, 4326], [1371, 4326], [1371, 4393], [1162, 4393]], [[75, 4391], [467, 4391], [467, 4435], [75, 4435]], [[69, 4535], [335, 4530], [336, 4589], [70, 4594]], [[1069, 4577], [1367, 4577], [1367, 4634], [1069, 4634]], [[73, 4636], [566, 4639], [566, 4682], [73, 4678]], [[74, 4786], [368, 4794], [366, 4846], [73, 4838]], [[1246, 4824], [1374, 4819], [1377, 4887], [1249, 4892]], [[74, 4884], [435, 4890], [434, 4934], [73, 4928]], [[71, 5029], [336, 5038], [334, 5096], [69, 5088]], [[1199, 5084], [1366, 5075], [1370, 5136], [1202, 5145]], [[74, 5132], [477, 5140], [476, 5185], [73, 5177]], [[72, 5281], [370, 5290], [368, 5341], [71, 5333]], [[1176, 5335], [1234, 5335], [1234, 5398], [1176, 5398]], [[1246, 5331], [1372, 5325], [1375, 5395], [1249, 5400]], [[74, 5379], [394, 5387], [393, 5431], [73, 5423]], [[73, 5524], [387, 5535], [385, 5592], [71, 5581]], [[1055, 5582], [1369, 5588], [1368, 5652], [1053, 5646]], [[74, 5624], [604, 5632], [603, 5678], [73, 5670]], [[72, 5775], [383, 5781], [382, 5838], [71, 5832]], [[1222, 5850], [1367, 5843], [1370, 5903], [1225, 5910]], [[72, 5876], [437, 5882], [436, 5928], [71, 5922]], [[74, 6027], [383, 6033], [382, 6085], [73, 6079]], [[1059, 6091], [1369, 6100], [1368, 6159], [1057, 6150]], [[73, 6127], [611, 6131], [611, 6175], [73, 6171]], [[75, 6283], [381, 6283], [381, 6333], [75, 6333]], [[1202, 6348], [1371, 6348], [1371, 6414], [1202, 6414]], [[73, 6385], [483, 6381], [484, 6425], [73, 6429]], [[69, 6536], [471, 6526], [473, 6583], [70, 6593]], [[1301, 6598], [1374, 6598], [1374, 6663], [1301, 6663]], [[71, 6639], [436, 6627], [437, 6671], [72, 6683]], [[67, 6792], [475, 6776], [477, 6831], [69, 6847]], [[1211, 6847], [1371, 6842], [1373, 6909], [1213, 6914]], [[69, 6888], [465, 6872], [468, 6923], [71, 6939]], [[70, 7035], [386, 7021], [389, 7085], [73, 7100]], [[1245, 7086], [1376, 7086], [1376, 7158], [1245, 7158]], [[67, 7131], [269, 7126], [270, 7191], [69, 7195]]], "rec_boxes": [[45, 245, 467, 443], [60, 453, 859, 657], [54, 554, 807, 757], [64, 893, 339, 1009], [61, 1085, 296, 1187], [1118, 1138, 1365, 1220], [66, 1248, 347, 1360], [66, 1353, 565, 1471], [1014, 1366, 1065, 1419], [1043, 1339, 1366, 1428], [65, 1506, 340, 1608], [69, 1609, 495, 1707], [1123, 1607, 1174, 1657], [1189, 1589, 1364, 1660], [68, 1760, 338, 1847], [1232, 1829, 1370, 1907], [70, 1858, 420, 1942], [72, 2013, 384, 2095], [1136, 2097, 1189, 2150], [1208, 2081, 1366, 2151], [70, 2112, 416, 2188], [70, 2267, 337, 2339], [1097, 2322, 1368, 2404], [73, 2371, 535, 2432], [72, 2520, 337, 2587], [1096, 2567, 1368, 2648], [74, 2623, 542, 2687], [74, 2779, 387, 2845], [1077, 2821, 1366, 2899], [1012, 2846, 1058, 2890], [73, 2881, 542, 2942], [71, 3039, 336, 3097], [1042, 3076, 1362, 3147], [977, 3100, 1016, 3144], [75, 3141, 555, 3189], [75, 3295, 283, 3352], [1221, 3323, 1372, 3400], [1146, 3336, 1206, 3395], [77, 3402, 450, 3441], [75, 3555, 334, 3605], [1121, 3590, 1174, 3643], [1194, 3584, 1367, 3645], [75, 3649, 465, 3695], [77, 3800, 382, 3850], [1221, 3833, 1371, 3894], [73, 3892, 454, 3944], [71, 4043, 388, 4098], [1116, 4083, 1166, 4133], [1182, 4080, 1370, 4153], [73, 4137, 490, 4190], [77, 4293, 386, 4343], [1089, 4330, 1144, 4383], [1162, 4326, 1371, 4393], [75, 4391, 467, 4435], [69, 4530, 336, 4594], [1069, 4577, 1367, 4634], [73, 4636, 566, 4682], [73, 4786, 368, 4846], [1246, 4819, 1377, 4892], [73, 4884, 435, 4934], [69, 5029, 336, 5096], [1199, 5075, 1370, 5145], [73, 5132, 477, 5185], [71, 5281, 370, 5341], [1176, 5335, 1234, 5398], [1246, 5325, 1375, 5400], [73, 5379, 394, 5431], [71, 5524, 387, 5592], [1053, 5582, 1369, 5652], [73, 5624, 604, 5678], [71, 5775, 383, 5838], [1222, 5843, 1370, 5910], [71, 5876, 437, 5928], [73, 6027, 383, 6085], [1057, 6091, 1369, 6159], [73, 6127, 611, 6175], [75, 6283, 381, 6333], [1202, 6348, 1371, 6414], [73, 6381, 484, 6429], [69, 6526, 473, 6593], [1301, 6598, 1374, 6663], [71, 6627, 437, 6683], [67, 6776, 477, 6847], [1211, 6842, 1373, 6914], [69, 6872, 468, 6939], [70, 7021, 389, 7100], [1245, 7086, 1376, 7158], [67, 7126, 270, 7195]]}