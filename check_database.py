#!/usr/bin/env python3
"""
检查数据库中的数据
"""

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from medical_ocr.config.config import DB_CONFIG
from medical_ocr.db.database_manager import DatabaseManager

def check_database():
    """检查数据库中的数据"""
    try:
        print("=" * 60)
        print("检查数据库中的数据")
        print("=" * 60)
        
        # 初始化数据库管理器
        db_manager = DatabaseManager(DB_CONFIG)
        conn = db_manager.get_connection()
        
        with conn.cursor() as cursor:
            # 检查medical_exam表是否存在
            cursor.execute("SHOW TABLES LIKE 'medical_exam'")
            if cursor.fetchone():
                print("✓ medical_exam表存在")
                
                # 查看表结构
                print("\n表结构:")
                cursor.execute("DESCRIBE medical_exam")
                for row in cursor:
                    print(f"  {row['Field']}: {row['Type']} {row['Null']} {row['Key']} {row['Default']}")
                
                # 查看数据
                print("\n表数据:")
                cursor.execute("SELECT * FROM medical_exam ORDER BY exam_id DESC LIMIT 5")
                rows = cursor.fetchall()
                if rows:
                    print(f"共有 {len(rows)} 条记录（显示最新5条）:")
                    for row in rows:
                        print(f"  ID: {row['exam_id']}")
                        print(f"  用户ID: {row['user_id']}")
                        print(f"  检查日期: {row['medical_date']}")
                        print(f"  检查方法: {row['exam_info']}")
                        print(f"  放射性诊断: {row['exam_diag']}")
                        print(f"  检查类型: {row['exam_type']}")
                        print(f"  医院: {row['hospital']}")
                        print("-" * 40)
                else:
                    print("  表中暂无数据")
            else:
                print("✗ medical_exam表不存在")
                
            # 检查medical_check表的数据（原检验报告）
            print("\n检验报告数据 (medical_check表):")
            cursor.execute("SELECT COUNT(*) as count FROM medical_check")
            count_result = cursor.fetchone()
            print(f"检验报告记录数: {count_result['count']}")
            
        conn.close()
        print("\n数据库检查完成！")
        
    except Exception as e:
        print(f"检查数据库时出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_database()
