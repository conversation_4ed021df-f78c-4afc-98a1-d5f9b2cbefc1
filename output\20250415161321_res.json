{"input_path": "D:\\dev\\medicalReport\\ocr\\source\\20250415161321.jpg", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": true, "use_doc_unwarping": true}, "angle": 0}, "dt_polys": [[[586, 97], [847, 111], [843, 198], [582, 185]], [[27, 130], [263, 229], [238, 290], [2, 190]], [[511, 183], [922, 208], [918, 267], [507, 242]], [[74, 484], [270, 536], [248, 621], [52, 569]], [[227, 537], [455, 589], [439, 657], [212, 605]], [[78, 674], [556, 760], [541, 844], [63, 757]], [[66, 773], [793, 858], [781, 961], [54, 876]], [[77, 1073], [340, 1124], [326, 1198], [63, 1146]], [[75, 1261], [296, 1304], [283, 1373], [62, 1329]], [[1116, 1323], [1354, 1305], [1359, 1366], [1121, 1384]], [[78, 1422], [346, 1475], [334, 1540], [66, 1487]], [[72, 1519], [565, 1577], [557, 1649], [63, 1591]], [[1075, 1514], [1357, 1499], [1360, 1566], [1079, 1581]], [[76, 1672], [336, 1711], [328, 1770], [67, 1731]], [[1199, 1750], [1357, 1740], [1361, 1804], [1203, 1814]], [[77, 1770], [489, 1813], [484, 1864], [71, 1821]], [[77, 1920], [334, 1948], [328, 2001], [71, 1973]], [[1219, 1991], [1356, 1981], [1361, 2046], [1224, 2056]], [[80, 2019], [416, 2048], [412, 2094], [76, 2065]], [[80, 2165], [378, 2190], [374, 2244], [76, 2218]], [[1203, 2230], [1357, 2223], [1360, 2285], [1206, 2293]], [[79, 2262], [413, 2285], [410, 2331], [76, 2308]], [[77, 2413], [333, 2426], [330, 2479], [74, 2466]], [[1094, 2473], [1356, 2462], [1358, 2521], [1097, 2532]], [[78, 2510], [529, 2526], [528, 2571], [76, 2555]], [[79, 2655], [333, 2671], [330, 2724], [76, 2709]], [[1077, 2712], [1356, 2699], [1358, 2758], [1080, 2771]], [[78, 2755], [539, 2769], [537, 2814], [76, 2800]], [[79, 2904], [383, 2919], [381, 2972], [76, 2957]], [[1075, 2952], [1355, 2936], [1358, 2995], [1078, 3010]], [[1009, 2966], [1052, 2966], [1052, 3010], [1009, 3010]], [[77, 3004], [542, 3012], [541, 3057], [77, 3049]], [[76, 3150], [332, 3156], [331, 3211], [75, 3205]], [[1040, 3195], [1355, 3178], [1358, 3235], [1043, 3252]], [[81, 3253], [551, 3253], [551, 3291], [81, 3291]], [[75, 3397], [279, 3393], [280, 3448], [76, 3452]], [[1205, 3417], [1365, 3411], [1368, 3489], [1207, 3494]], [[77, 3494], [448, 3490], [448, 3535], [77, 3539]], [[75, 3640], [331, 3636], [332, 3689], [76, 3693]], [[1195, 3677], [1356, 3665], [1361, 3728], [1199, 3740]], [[78, 3739], [461, 3729], [462, 3769], [79, 3779]], [[77, 3875], [381, 3875], [381, 3927], [77, 3927]], [[1210, 3922], [1360, 3910], [1365, 3971], [1214, 3983]], [[77, 3970], [450, 3966], [450, 4012], [77, 4016]], [[76, 4111], [385, 4117], [384, 4164], [75, 4158]], [[1192, 4158], [1367, 4163], [1366, 4231], [1190, 4226]], [[76, 4203], [488, 4214], [487, 4259], [75, 4249]], [[78, 4350], [383, 4360], [381, 4407], [76, 4397]], [[1080, 4403], [1136, 4403], [1136, 4456], [1080, 4456]], [[1157, 4399], [1363, 4399], [1363, 4460], [1157, 4460]], [[76, 4443], [465, 4453], [464, 4497], [75, 4486]], [[74, 4583], [334, 4592], [333, 4645], [73, 4636]], [[1063, 4639], [1360, 4633], [1362, 4691], [1064, 4698]], [[76, 4678], [569, 4696], [567, 4740], [74, 4722]], [[77, 4820], [368, 4835], [366, 4888], [74, 4873]], [[1148, 4887], [1200, 4887], [1200, 4942], [1148, 4942]], [[1223, 4881], [1363, 4873], [1366, 4938], [1227, 4945]], [[76, 4919], [434, 4932], [432, 4977], [75, 4965]], [[74, 5063], [334, 5072], [332, 5129], [73, 5120]], [[1197, 5129], [1361, 5120], [1364, 5183], [1201, 5192]], [[75, 5154], [475, 5173], [473, 5226], [72, 5208]], [[75, 5304], [368, 5321], [365, 5374], [72, 5357]], [[1178, 5381], [1226, 5381], [1226, 5436], [1178, 5436]], [[1248, 5373], [1362, 5366], [1366, 5432], [1252, 5439]], [[73, 5399], [394, 5412], [392, 5465], [71, 5453]], [[77, 5549], [383, 5564], [381, 5617], [74, 5602]], [[1069, 5620], [1359, 5620], [1359, 5673], [1069, 5673]], [[76, 5644], [595, 5664], [593, 5710], [74, 5690]], [[76, 5793], [377, 5803], [376, 5856], [74, 5846]], [[1203, 5868], [1360, 5858], [1364, 5921], [1206, 5931]], [[73, 5886], [436, 5900], [434, 5951], [71, 5937]], [[76, 6038], [381, 6046], [380, 6099], [75, 6091]], [[1051, 6109], [1359, 6102], [1360, 6161], [1053, 6168]], [[74, 6134], [584, 6150], [582, 6194], [73, 6178]], [[74, 6281], [377, 6291], [376, 6344], [73, 6334]], [[1193, 6350], [1361, 6341], [1364, 6404], [1197, 6413]], [[74, 6381], [480, 6392], [479, 6437], [73, 6427]], [[70, 6531], [467, 6538], [466, 6589], [69, 6583]], [[1282, 6583], [1365, 6583], [1365, 6648], [1282, 6648]], [[77, 6634], [429, 6634], [429, 6674], [77, 6674]], [[71, 6782], [471, 6782], [471, 6834], [71, 6834]], [[1206, 6829], [1360, 6819], [1364, 6884], [1210, 6894]], [[77, 6881], [459, 6881], [459, 6921], [77, 6921]], [[75, 7027], [379, 7027], [379, 7080], [75, 7080]], [[1237, 7064], [1369, 7059], [1371, 7129], [1239, 7134]], [[71, 7122], [262, 7122], [262, 7175], [71, 7175]], [[69, 7270], [317, 7270], [317, 7331], [69, 7331]], [[1031, 7314], [1091, 7314], [1091, 7373], [1031, 7373]], [[1102, 7308], [1363, 7308], [1363, 7382], [1102, 7382]], [[72, 7367], [467, 7373], [466, 7432], [71, 7426]]], "text_det_params": {"limit_side_len": 64, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.0, "rec_texts": ["报告查询", "门诊血常规", "fwcs.linkingcloud.cn", "*春香", "香***岁", "申请科室：胰腺外科", "报告日期：2025-4-15 0:00:00", "项目明细", "项目名称", "结果/单位", "白细胞计数", "参考值：3.5-9.5*10^9/L", "9.5*10^9/L", "淋巴细胞%", "21.5%", "参考值：20.0-50.0%", "单核细胞%", "5.6%", "参考值：3.0-10%", "中性粒细胞%", "71.2%", "参考值：40-75%", "淋巴细胞数", "2.1*10^9/L", "参考值：1.1-3.2*10^9/L", "单核细胞数", "0.5*10^9/L", "参考值：0.1-0.6*10^9/L", "中性粒细胞数", "6.8*10^9/L", "↑", "参考值：1.8-6.3*10^9/L", "红细胞计数", "4.19*10^12/L", "参考值：3.8-5.1*10^12/1", "血红蛋白", "116g/l", "参考值：115-150g/l", "红细胞压积", "37.0%", "参考值：女35-45%", "平均RBC体积", "88.3fl", "参考值：82.0-100fl", "RBC平均HGB", "27.7pg", "参考值：27.0-34.0pg", "平均HGB浓度", "", "314.0g/l", "参考值：316-354g/", "血小板计数", "205*10^9/L", "参考值：125-350*10^9/L", "平均PLT容积", "个", "10.6fl", "参考值：7.4-10.4fl", "血小板压积", "0.22%", "参考值：0.15-0.30%", "PLT分布宽度", "↓", "12%", "参考值：13-21%", "嗜酸性细胞数", "0.14*10^9/L", "参考值：0.02-0.52*10^9/", "嗜酸性细胞%", "1.50%", "参考值：0.4-8.0%", "嗜碱性细胞数", "0.02*10^9/L", "参考值：0.00-0.06*10^9/", "嗜碱性细胞%", "0.20%", "参考值：0.00-1.00%", "RBC分布宽度-SD", "40", "参考值：35-44FL", "RBC分布宽度-CV", "12.3%", "参考值：11.6-14.4%", "中性淋巴比值", "3.24", "参考值：", "C反应蛋白", "个", "11.20mg/L", "参考值：&lt;10mg/L"], "rec_scores": [0.9998089075088501, 0.9970323443412781, 0.9990757703781128, 0.9923747181892395, 0.9465984106063843, 0.9961628317832947, 0.9638664722442627, 0.9992085695266724, 0.9998834133148193, 0.9949561357498169, 0.9990026354789734, 0.9938876628875732, 0.9703823328018188, 0.997710108757019, 0.9995215535163879, 0.996799111366272, 0.9995781183242798, 0.9996601343154907, 0.9928308129310608, 0.9991316795349121, 0.9992361068725586, 0.9912293553352356, 0.9987854957580566, 0.9903815984725952, 0.9553805589675903, 0.9998785853385925, 0.9710693359375, 0.952396810054779, 0.9997588992118835, 0.9353556632995605, 0.9805450439453125, 0.9673459529876709, 0.9995847940444946, 0.9775080680847168, 0.9522833824157715, 0.9996151328086853, 0.9619972109794617, 0.9807999730110168, 0.9998207092285156, 0.9990760684013367, 0.9871618151664734, 0.9996914863586426, 0.9668710231781006, 0.9710108637809753, 0.9996760487556458, 0.9994135499000549, 0.9845436811447144, 0.9988545179367065, 0.0, 0.9574736952781677, 0.9913600087165833, 0.9992296099662781, 0.9802621006965637, 0.9747381210327148, 0.9991654753684998, 0.5886089205741882, 0.9941795468330383, 0.981090247631073, 0.9996797442436218, 0.999230682849884, 0.9948692917823792, 0.9984222054481506, 0.20076553523540497, 0.9940159916877747, 0.9946249723434448, 0.9997161030769348, 0.9687976837158203, 0.9666581153869629, 0.9993624687194824, 0.998447060585022, 0.9954400062561035, 0.9995755553245544, 0.9654197096824646, 0.9763755798339844, 0.9990883469581604, 0.9988183975219727, 0.9905795454978943, 0.9990379214286804, 0.9997791051864624, 0.9799265265464783, 0.9973408579826355, 0.9993788003921509, 0.9942194819450378, 0.9885829091072083, 0.9997205138206482, 0.997609555721283, 0.998969554901123, 0.971600353717804, 0.9986013770103455, 0.9658942222595215], "rec_polys": [[[586, 97], [847, 111], [843, 198], [582, 185]], [[27, 130], [263, 229], [238, 290], [2, 190]], [[511, 183], [922, 208], [918, 267], [507, 242]], [[74, 484], [270, 536], [248, 621], [52, 569]], [[227, 537], [455, 589], [439, 657], [212, 605]], [[78, 674], [556, 760], [541, 844], [63, 757]], [[66, 773], [793, 858], [781, 961], [54, 876]], [[77, 1073], [340, 1124], [326, 1198], [63, 1146]], [[75, 1261], [296, 1304], [283, 1373], [62, 1329]], [[1116, 1323], [1354, 1305], [1359, 1366], [1121, 1384]], [[78, 1422], [346, 1475], [334, 1540], [66, 1487]], [[72, 1519], [565, 1577], [557, 1649], [63, 1591]], [[1075, 1514], [1357, 1499], [1360, 1566], [1079, 1581]], [[76, 1672], [336, 1711], [328, 1770], [67, 1731]], [[1199, 1750], [1357, 1740], [1361, 1804], [1203, 1814]], [[77, 1770], [489, 1813], [484, 1864], [71, 1821]], [[77, 1920], [334, 1948], [328, 2001], [71, 1973]], [[1219, 1991], [1356, 1981], [1361, 2046], [1224, 2056]], [[80, 2019], [416, 2048], [412, 2094], [76, 2065]], [[80, 2165], [378, 2190], [374, 2244], [76, 2218]], [[1203, 2230], [1357, 2223], [1360, 2285], [1206, 2293]], [[79, 2262], [413, 2285], [410, 2331], [76, 2308]], [[77, 2413], [333, 2426], [330, 2479], [74, 2466]], [[1094, 2473], [1356, 2462], [1358, 2521], [1097, 2532]], [[78, 2510], [529, 2526], [528, 2571], [76, 2555]], [[79, 2655], [333, 2671], [330, 2724], [76, 2709]], [[1077, 2712], [1356, 2699], [1358, 2758], [1080, 2771]], [[78, 2755], [539, 2769], [537, 2814], [76, 2800]], [[79, 2904], [383, 2919], [381, 2972], [76, 2957]], [[1075, 2952], [1355, 2936], [1358, 2995], [1078, 3010]], [[1009, 2966], [1052, 2966], [1052, 3010], [1009, 3010]], [[77, 3004], [542, 3012], [541, 3057], [77, 3049]], [[76, 3150], [332, 3156], [331, 3211], [75, 3205]], [[1040, 3195], [1355, 3178], [1358, 3235], [1043, 3252]], [[81, 3253], [551, 3253], [551, 3291], [81, 3291]], [[75, 3397], [279, 3393], [280, 3448], [76, 3452]], [[1205, 3417], [1365, 3411], [1368, 3489], [1207, 3494]], [[77, 3494], [448, 3490], [448, 3535], [77, 3539]], [[75, 3640], [331, 3636], [332, 3689], [76, 3693]], [[1195, 3677], [1356, 3665], [1361, 3728], [1199, 3740]], [[78, 3739], [461, 3729], [462, 3769], [79, 3779]], [[77, 3875], [381, 3875], [381, 3927], [77, 3927]], [[1210, 3922], [1360, 3910], [1365, 3971], [1214, 3983]], [[77, 3970], [450, 3966], [450, 4012], [77, 4016]], [[76, 4111], [385, 4117], [384, 4164], [75, 4158]], [[1192, 4158], [1367, 4163], [1366, 4231], [1190, 4226]], [[76, 4203], [488, 4214], [487, 4259], [75, 4249]], [[78, 4350], [383, 4360], [381, 4407], [76, 4397]], [[1080, 4403], [1136, 4403], [1136, 4456], [1080, 4456]], [[1157, 4399], [1363, 4399], [1363, 4460], [1157, 4460]], [[76, 4443], [465, 4453], [464, 4497], [75, 4486]], [[74, 4583], [334, 4592], [333, 4645], [73, 4636]], [[1063, 4639], [1360, 4633], [1362, 4691], [1064, 4698]], [[76, 4678], [569, 4696], [567, 4740], [74, 4722]], [[77, 4820], [368, 4835], [366, 4888], [74, 4873]], [[1148, 4887], [1200, 4887], [1200, 4942], [1148, 4942]], [[1223, 4881], [1363, 4873], [1366, 4938], [1227, 4945]], [[76, 4919], [434, 4932], [432, 4977], [75, 4965]], [[74, 5063], [334, 5072], [332, 5129], [73, 5120]], [[1197, 5129], [1361, 5120], [1364, 5183], [1201, 5192]], [[75, 5154], [475, 5173], [473, 5226], [72, 5208]], [[75, 5304], [368, 5321], [365, 5374], [72, 5357]], [[1178, 5381], [1226, 5381], [1226, 5436], [1178, 5436]], [[1248, 5373], [1362, 5366], [1366, 5432], [1252, 5439]], [[73, 5399], [394, 5412], [392, 5465], [71, 5453]], [[77, 5549], [383, 5564], [381, 5617], [74, 5602]], [[1069, 5620], [1359, 5620], [1359, 5673], [1069, 5673]], [[76, 5644], [595, 5664], [593, 5710], [74, 5690]], [[76, 5793], [377, 5803], [376, 5856], [74, 5846]], [[1203, 5868], [1360, 5858], [1364, 5921], [1206, 5931]], [[73, 5886], [436, 5900], [434, 5951], [71, 5937]], [[76, 6038], [381, 6046], [380, 6099], [75, 6091]], [[1051, 6109], [1359, 6102], [1360, 6161], [1053, 6168]], [[74, 6134], [584, 6150], [582, 6194], [73, 6178]], [[74, 6281], [377, 6291], [376, 6344], [73, 6334]], [[1193, 6350], [1361, 6341], [1364, 6404], [1197, 6413]], [[74, 6381], [480, 6392], [479, 6437], [73, 6427]], [[70, 6531], [467, 6538], [466, 6589], [69, 6583]], [[1282, 6583], [1365, 6583], [1365, 6648], [1282, 6648]], [[77, 6634], [429, 6634], [429, 6674], [77, 6674]], [[71, 6782], [471, 6782], [471, 6834], [71, 6834]], [[1206, 6829], [1360, 6819], [1364, 6884], [1210, 6894]], [[77, 6881], [459, 6881], [459, 6921], [77, 6921]], [[75, 7027], [379, 7027], [379, 7080], [75, 7080]], [[1237, 7064], [1369, 7059], [1371, 7129], [1239, 7134]], [[71, 7122], [262, 7122], [262, 7175], [71, 7175]], [[69, 7270], [317, 7270], [317, 7331], [69, 7331]], [[1031, 7314], [1091, 7314], [1091, 7373], [1031, 7373]], [[1102, 7308], [1363, 7308], [1363, 7382], [1102, 7382]], [[72, 7367], [467, 7373], [466, 7432], [71, 7426]]], "rec_boxes": [[582, 97, 847, 198], [2, 130, 263, 290], [507, 183, 922, 267], [52, 484, 270, 621], [212, 537, 455, 657], [63, 674, 556, 844], [54, 773, 793, 961], [63, 1073, 340, 1198], [62, 1261, 296, 1373], [1116, 1305, 1359, 1384], [66, 1422, 346, 1540], [63, 1519, 565, 1649], [1075, 1499, 1360, 1581], [67, 1672, 336, 1770], [1199, 1740, 1361, 1814], [71, 1770, 489, 1864], [71, 1920, 334, 2001], [1219, 1981, 1361, 2056], [76, 2019, 416, 2094], [76, 2165, 378, 2244], [1203, 2223, 1360, 2293], [76, 2262, 413, 2331], [74, 2413, 333, 2479], [1094, 2462, 1358, 2532], [76, 2510, 529, 2571], [76, 2655, 333, 2724], [1077, 2699, 1358, 2771], [76, 2755, 539, 2814], [76, 2904, 383, 2972], [1075, 2936, 1358, 3010], [1009, 2966, 1052, 3010], [77, 3004, 542, 3057], [75, 3150, 332, 3211], [1040, 3178, 1358, 3252], [81, 3253, 551, 3291], [75, 3393, 280, 3452], [1205, 3411, 1368, 3494], [77, 3490, 448, 3539], [75, 3636, 332, 3693], [1195, 3665, 1361, 3740], [78, 3729, 462, 3779], [77, 3875, 381, 3927], [1210, 3910, 1365, 3983], [77, 3966, 450, 4016], [75, 4111, 385, 4164], [1190, 4158, 1367, 4231], [75, 4203, 488, 4259], [76, 4350, 383, 4407], [1080, 4403, 1136, 4456], [1157, 4399, 1363, 4460], [75, 4443, 465, 4497], [73, 4583, 334, 4645], [1063, 4633, 1362, 4698], [74, 4678, 569, 4740], [74, 4820, 368, 4888], [1148, 4887, 1200, 4942], [1223, 4873, 1366, 4945], [75, 4919, 434, 4977], [73, 5063, 334, 5129], [1197, 5120, 1364, 5192], [72, 5154, 475, 5226], [72, 5304, 368, 5374], [1178, 5381, 1226, 5436], [1248, 5366, 1366, 5439], [71, 5399, 394, 5465], [74, 5549, 383, 5617], [1069, 5620, 1359, 5673], [74, 5644, 595, 5710], [74, 5793, 377, 5856], [1203, 5858, 1364, 5931], [71, 5886, 436, 5951], [75, 6038, 381, 6099], [1051, 6102, 1360, 6168], [73, 6134, 584, 6194], [73, 6281, 377, 6344], [1193, 6341, 1364, 6413], [73, 6381, 480, 6437], [69, 6531, 467, 6589], [1282, 6583, 1365, 6648], [77, 6634, 429, 6674], [71, 6782, 471, 6834], [1206, 6819, 1364, 6894], [77, 6881, 459, 6921], [75, 7027, 379, 7080], [1237, 7059, 1371, 7134], [71, 7122, 262, 7175], [69, 7270, 317, 7331], [1031, 7314, 1091, 7373], [1102, 7308, 1363, 7382], [71, 7367, 467, 7432]]}