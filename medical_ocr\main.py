import os
import shutil
from .config.config import DB_CONFIG, OCR_CONFIG
from .db.database_manager import DatabaseManager
from .ocr.report_processor import ReportProcessor
from .ocr.exam_report_processor import ExamReportProcessor
from .utils.logger import logger

def main():
    try:
        # 显示选择菜单
        print("=" * 50)
        print("医疗报告OCR识别系统")
        print("=" * 50)
        print("请选择处理类型：")
        print("1 - 检验报告（从source目录处理，保存到medical_check表）")
        print("2 - 检查报告（从source2目录处理，保存到medical_exam表）")
        print("=" * 50)

        while True:
            try:
                choice = input("请输入选择 (1 或 2): ").strip()
                if choice in ['1', '2']:
                    break
                else:
                    print("无效选择，请输入 1 或 2")
            except KeyboardInterrupt:
                print("\n程序已取消")
                return
            except Exception as e:
                print(f"输入错误: {e}")

        db_manager = DatabaseManager(DB_CONFIG)
        project_root = os.path.dirname(os.path.dirname(__file__))

        if choice == '1':
            # 处理检验报告（原逻辑）
            process_lab_reports(db_manager, project_root)
        else:
            # 处理检查报告（新逻辑）
            process_exam_reports(db_manager, project_root)

    except Exception as e:
        logger.error(f"Critical error in main function: {str(e)}", exc_info=True)

def process_lab_reports(db_manager, project_root):
    """处理检验报告（原逻辑）"""
    try:
        processor = ReportProcessor(db_manager, OCR_CONFIG)

        # 创建processed目录（如果不存在）
        processed_dir = os.path.join(project_root, 'processed')
        os.makedirs(processed_dir, exist_ok=True)

        # 处理source目录下所有jpg/png文件
        source_dir = os.path.join(project_root, 'source')
        supported_ext = ('.jpg', '.jpeg', '.png')

        if not os.path.exists(source_dir):
            logger.error(f"Source目录不存在: {source_dir}")
            print(f"错误：source目录不存在: {source_dir}")
            return

        files = [f for f in os.listdir(source_dir) if f.lower().endswith(supported_ext)]
        if not files:
            print("source目录中没有找到图片文件")
            return

        print(f"找到 {len(files)} 个检验报告文件，开始处理...")

        for filename in files:
            src_path = os.path.join(source_dir, filename)
            try:
                print(f"正在处理: {filename}")
                # 处理文件
                processor.process_report(src_path)

                # 移动已处理文件
                dst_path = os.path.join(processed_dir, filename)
                shutil.move(src_path, dst_path)
                logger.info(f"文件 {filename} 已移动到 processed 目录")
                print(f"完成处理: {filename}")
            except Exception as e:
                logger.error(f"Error processing file {filename}: {str(e)}", exc_info=True)
                print(f"处理文件 {filename} 时出错: {str(e)}")

        print("检验报告处理完成！")
    except Exception as e:
        logger.error(f"Error in process_lab_reports: {str(e)}", exc_info=True)
        print(f"处理检验报告时出错: {str(e)}")

def process_exam_reports(db_manager, project_root):
    """处理检查报告（新逻辑）"""
    try:
        processor = ExamReportProcessor(db_manager, OCR_CONFIG)

        # 创建processed2目录（如果不存在）
        processed_dir = os.path.join(project_root, 'processed2')
        os.makedirs(processed_dir, exist_ok=True)

        # 处理source2目录下所有jpg/png文件
        source_dir = os.path.join(project_root, 'source2')
        supported_ext = ('.jpg', '.jpeg', '.png')

        if not os.path.exists(source_dir):
            logger.error(f"Source2目录不存在: {source_dir}")
            print(f"错误：source2目录不存在: {source_dir}")
            return

        files = [f for f in os.listdir(source_dir) if f.lower().endswith(supported_ext)]
        if not files:
            print("source2目录中没有找到图片文件")
            return

        print(f"找到 {len(files)} 个检查报告文件，开始处理...")

        for filename in files:
            src_path = os.path.join(source_dir, filename)
            try:
                print(f"正在处理: {filename}")
                # 处理文件
                processor.process_exam_report(src_path)

                # 移动已处理文件
                dst_path = os.path.join(processed_dir, filename)
                shutil.move(src_path, dst_path)
                logger.info(f"文件 {filename} 已移动到 processed2 目录")
                print(f"完成处理: {filename}")
            except Exception as e:
                logger.error(f"Error processing file {filename}: {str(e)}", exc_info=True)
                print(f"处理文件 {filename} 时出错: {str(e)}")

        print("检查报告处理完成！")
    except Exception as e:
        logger.error(f"Error in process_exam_reports: {str(e)}", exc_info=True)
        print(f"处理检查报告时出错: {str(e)}")

if __name__ == "__main__":
    main()


