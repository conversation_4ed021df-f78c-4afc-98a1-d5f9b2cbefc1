import os
import shutil
from .config.config import DB_CONFIG, OCR_CONFIG
from .db.database_manager import DatabaseManager
from .ocr.report_processor import ReportProcessor
from .utils.logger import logger

def main():
    try:
        db_manager = DatabaseManager(DB_CONFIG)
        
        processor = ReportProcessor(db_manager, OCR_CONFIG)

        # 创建processed目录（如果不存在）
        project_root = os.path.dirname(os.path.dirname(__file__))
        processed_dir = os.path.join(project_root, 'processed')
        os.makedirs(processed_dir, exist_ok=True)

        # 处理source目录下所有jpg/png文件
        source_dir = os.path.join(project_root, 'source')
        supported_ext = ('.jpg', '.jpeg', '.png')
        
        for filename in os.listdir(source_dir):
            if filename.lower().endswith(supported_ext):
                src_path = os.path.join(source_dir, filename)
                try:
                    # 处理文件
                    processor.process_report(src_path)
                    
                    # 移动已处理文件
                    dst_path = os.path.join(processed_dir, filename)
                    shutil.move(src_path, dst_path)
                    logger.info(f"文件 {filename} 已移动到 processed 目录")
                except Exception as e:
                    logger.error(f"Error processing file {filename}: {str(e)}", exc_info=True)
    except Exception as e:
        logger.error(f"Critical error in main function: {str(e)}", exc_info=True)

if __name__ == "__main__":
    main()


