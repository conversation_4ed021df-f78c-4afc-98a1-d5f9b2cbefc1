# 医疗报告OCR识别系统

这是一个基于PaddleOCR的医疗报告自动识别和数据库存储系统。

## 项目结构

```
ocr/
├── medical_ocr/                # 主要代码模块
│   ├── config/                 # 配置文件
│   │   └── config.py          # 数据库和OCR配置
│   ├── db/                    # 数据库模块
│   │   ├── __init__.py
│   │   └── database_manager.py # 数据库管理器
│   ├── ocr/                   # OCR处理模块
│   │   ├── __init__.py
│   │   └── report_processor.py # 报告处理器
│   ├── utils/                 # 工具模块
│   │   ├── __init__.py
│   │   └── logger.py          # 日志工具
│   └── main.py                # 主程序入口
├── source/                    # 待处理图片目录
├── processed/                 # 已处理图片目录
├── output/                    # OCR结果输出目录
├── requirements.txt           # 项目依赖
├── database_init.sql          # 数据库初始化脚本
└── README.md                  # 项目说明文档
```

## 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 数据库配置

1. 确保MySQL服务已启动
2. 执行数据库初始化脚本：

```bash
mysql -u root -p < database_init.sql
```

3. 修改 `medical_ocr/config/config.py` 中的数据库配置：

```python
DB_CONFIG = {
    'host': 'localhost',
    'user': 'your_username',
    'password': 'your_password',
    'database': 'medical'
}
```

### 3. OCR配置

OCR配置已在 `medical_ocr/config/config.py` 中预设，使用PaddleOCR的服务器版模型：

```python
OCR_CONFIG = {
    'text_detection_model_name': "PP-OCRv5_server_det",
    'text_recognition_model_name': "PP-OCRv5_server_rec",
    'text_rec_score_thresh': 0.6,
    'use_doc_orientation_classify': False,
    'use_doc_unwarping': False
}
```

## 使用方法

### 1. 准备图片文件

将需要处理的医疗报告图片（支持jpg、jpeg、png格式）放入 `source/` 目录。

### 2. 运行程序

```bash
cd medical_ocr
python main.py
```

### 3. 查看结果

- 处理后的图片会移动到 `processed/` 目录
- OCR识别结果会保存到 `output/` 目录的JSON文件中
- 结构化数据会自动存储到MySQL数据库中

## 功能特性

### OCR识别功能
- 自动识别医疗报告中的检验日期
- 提取各项检验指标的名称、数值、单位和参考值
- 支持多种日期格式识别
- 智能处理指标名称的标准化

### 数据处理功能
- 自动判断检验指标是否异常（高/低/正常）
- 支持指标名称的智能映射和标准化
- 处理复杂的表格结构和数据格式

### 数据库存储
- 主表存储检查基本信息和整体状态
- 详情表存储每个指标的具体数据
- 参考值表用于异常判断

## 支持的医疗指标

系统预置了常见的血液检查指标参考值，包括：
- 血常规指标（白细胞、红细胞、血红蛋白、血小板等）
- 细胞分类计数和百分比
- 肿瘤标志物（糖类抗原等）

## 注意事项

1. 确保图片质量清晰，文字可读
2. 支持的图片格式：JPG、JPEG、PNG
3. 数据库连接配置需要正确设置
4. 首次运行时PaddleOCR会自动下载模型文件

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证数据库配置信息是否正确

2. **OCR识别效果不佳**
   - 确保图片质量清晰
   - 可以调整OCR配置中的识别阈值

3. **依赖安装失败**
   - 建议使用虚拟环境
   - 确保Python版本兼容（推荐3.8+）

## 开发说明

项目采用模块化设计，各模块职责清晰：
- `config`: 配置管理
- `db`: 数据库操作
- `ocr`: OCR处理逻辑
- `utils`: 通用工具

如需扩展功能，可以在对应模块中添加新的类和方法。
