#!/usr/bin/env python3
"""
演示如何使用新的检查报告功能
"""

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def show_usage_demo():
    """展示使用方法"""
    print("=" * 70)
    print("医疗报告OCR识别系统 - 使用演示")
    print("=" * 70)
    
    print("\n🎯 新功能说明:")
    print("现在系统支持两种类型的报告处理：")
    print("1. 检验报告 - 原有功能，处理血常规、生化等检验报告")
    print("2. 检查报告 - 新增功能，处理CT、MRI等影像检查报告")
    
    print("\n📁 目录结构:")
    print("source/      - 存放检验报告图片")
    print("processed/   - 检验报告处理完成后移动到此目录")
    print("source2/     - 存放检查报告图片")
    print("processed2/  - 检查报告处理完成后移动到此目录")
    
    print("\n💾 数据库表:")
    print("medical_check        - 存储检验报告数据")
    print("medical_check_detail - 存储检验报告详细指标")
    print("medical_exam         - 存储检查报告数据")
    
    print("\n🔧 使用步骤:")
    print("1. 准备图片文件:")
    print("   - 检验报告图片 → 放入 source/ 目录")
    print("   - 检查报告图片 → 放入 source2/ 目录")
    
    print("\n2. 运行程序:")
    print("   python run.py")
    
    print("\n3. 选择处理类型:")
    print("   选择 1 - 处理检验报告")
    print("   选择 2 - 处理检查报告")
    
    print("\n4. 系统自动处理:")
    print("   - OCR识别图片中的文字")
    print("   - 解析关键信息")
    print("   - 保存到数据库")
    print("   - 移动图片到processed目录")
    
    print("\n📊 检查报告解析内容:")
    print("- 检查日期：从'报告日期：2025/4/2216:24:00'格式中提取年月日，去掉时间")
    print("- 检查方法：从'检查方法：'开始，包括后面几行，直到'放射性诊断'")
    print("- 放射性诊断：从'放射性诊断'下一行开始，直到'查看影像'为止")
    print("- 检查类型：默认设置为1（影像检查）")
    print("- 医院：默认设置为'复旦肿瘤'")

    print("\n🗄️ 数据库字段说明 (medical_exam表):")
    print("exam_id      - 主键ID")
    print("user_id      - 用户ID（默认为1）")
    print("medical_date - 检查日期（YYYY-MM-DD格式）")
    print("exam_info    - 检查方法信息（多行文本）")
    print("exam_diag    - 放射性诊断信息（多行文本）")
    print("exam_type    - 检查类型（默认为1）")
    print("hospital     - 医院名称（默认为'复旦肿瘤'）")

    print("\n✅ 测试状态:")
    print("✓ 数据库连接正常")
    print("✓ OCR识别功能正常")
    print("✓ 文本解析功能正常")
    print("✓ 数据保存功能正常")
    print("✓ 文件移动功能正常")
    print("✓ 新解析逻辑测试通过")

    print("\n📝 示例检查报告格式:")
    print("报告日期：2025/4/2216:24:00")
    print("检查方法：CT平扫+增强扫描")
    print("胸部CT检查")
    print("造影剂使用情况：使用碘对比剂")
    print("放射性诊断：")
    print("肺部未见明显异常")
    print("心脏大小正常")
    print("纵隔结构清晰")
    print("查看影像")
    
    print("\n" + "=" * 70)
    print("系统已准备就绪，可以开始使用！")
    print("=" * 70)

def check_directories():
    """检查必要的目录是否存在"""
    print("\n🔍 检查目录状态:")
    
    directories = [
        ('source', '检验报告源目录'),
        ('processed', '检验报告处理完成目录'),
        ('source2', '检查报告源目录'),
        ('processed2', '检查报告处理完成目录'),
        ('output', 'OCR结果输出目录')
    ]
    
    for dir_name, description in directories:
        if os.path.exists(dir_name):
            files = os.listdir(dir_name)
            file_count = len([f for f in files if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
            print(f"✓ {description}: {dir_name}/ (包含 {file_count} 个图片文件)")
        else:
            print(f"✗ {description}: {dir_name}/ (不存在)")
            os.makedirs(dir_name, exist_ok=True)
            print(f"  → 已创建目录: {dir_name}/")

if __name__ == "__main__":
    show_usage_demo()
    check_directories()
    
    print("\n💡 提示:")
    print("- 如需测试功能，可以运行: python test_exam_processor.py")
    print("- 如需查看数据库数据，可以运行: python check_database.py")
    print("- 准备好图片文件后，运行: python run.py")
