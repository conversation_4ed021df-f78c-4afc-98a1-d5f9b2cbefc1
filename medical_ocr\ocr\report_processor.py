import logging
from paddleocr import PaddleOCR
import re
from datetime import datetime
import pymysql
from ..utils.logger import logger

# At the top of the file, add a test log to verify logger is working
logger.debug("Report processor module loaded")

# Try to handle matplotlib-related imports separately
try:
    import matplotlib
    # Set non-interactive backend to avoid display issues
    matplotlib.use('Agg')
    logger.debug(f"Matplotlib backend set to: {matplotlib.get_backend()}")
except Exception as e:
    logger.warning(f"Error setting matplotlib backend: {str(e)}")

class ReportProcessor:
    DATE_PATTERN = re.compile(r'检验日期：(\d{4}-\d{1,2}-\d{1,2})')
    VALUE_UNIT_PATTERN = re.compile(r'^(\d+\.?\d*)(.*)')

    def __init__(self, db_manager, ocr_config):
        self.db_manager = db_manager
        self.ocr_config = ocr_config

    def process_report(self, image_path):
        try:
            logger.info(f"当前路径开始OCR操作： {image_path}")
            
            print("OCR配置:", self.ocr_config)
            ocr = PaddleOCR(**self.ocr_config)
            result = ocr.predict(image_path)

            for i, res in enumerate(result):
                self._process_single_result(res)
                
            logger.info(f"完成处理 {image_path}")
        except Exception as e:
            logger.error(f"Error in process_report: {str(e)}", exc_info=True)
            raise  # Re-raise to be caught by the main function

    def _process_single_result(self, res):
        try:
            res.save_to_json("output")
            
            data = self._parse_ocr_result(res)

            if data:
                self._save_to_database(data)
                logger.debug("数据保存到数据库")
            else:
                logger.warning("No valid data extracted from OCR result")
        except Exception as e:
            logger.error(f"Error in _process_single_result: {str(e)}", exc_info=True)

    def _parse_ocr_result(self, res):
        try:
            if hasattr(res, 'rec_texts'):
                rec_texts = res.rec_texts
            elif isinstance(res, dict) and 'rec_texts' in res:
                rec_texts = res['rec_texts']
            else:
                # 如果res是OCR结果对象，尝试获取其内部数据
                rec_texts = getattr(res, 'rec_texts', None)
                if rec_texts is None:
                    logger.error(f"Cannot find rec_texts in OCR result: {type(res)}")
                    return None

            medical_date = self._extract_medical_date(rec_texts)
            lab_data = self._parse_lab_items(rec_texts)

            return {
                'medical_date': medical_date,
                'lab_items': lab_data
            }
        except (KeyError, TypeError, AttributeError) as e:
            logger.error(f"OCR result parsing error: {str(e)}")
            return None

    def _extract_medical_date(self, rec_texts):
        for text in rec_texts:
            if '报告日期' in text:
                # 清理字符串并分割日期时间
                clean_text = text.replace('报告日期：', '').split(' ')[0]
                clean_text = clean_text.replace('0:00:00', '').rstrip(':')

                # 尝试多种日期格式匹配
                for fmt in ('%Y-%m-%d', '%Y-%m-%d', '%Y/%m/%d'):
                    try:
                        return datetime.strptime(clean_text, fmt).strftime('%Y-%m-%d')
                    except ValueError:
                        continue

            match = self.DATE_PATTERN.search(text)
            if match:
                try:
                    return datetime.strptime(match.group(1), '%Y-%m-%d').date()
                except ValueError as e:
                    logger.warning(f"Invalid date format: {match.group(1)}")

            if '采样日期' in text:
                # 清理字符串并分割日期时间
                clean_text = text.replace('采样日期：', '').split(' ')[0]
                if len(clean_text) >= 10:
                    date_candidate = clean_text[:10]
                    try:
                        return datetime.strptime(date_candidate, '%Y-%m-%d').date()
                    except ValueError:
                        continue
        # 新增报告时间匹配逻辑
        for idx, text in enumerate(rec_texts):
            if '报告时间' in text and idx < len(rec_texts)-1:
                next_text = rec_texts[idx+1]
                if len(next_text) >= 10:
                    date_candidate = next_text[:10]
                    try:
                        return datetime.strptime(date_candidate, '%Y-%m-%d').date()
                    except ValueError:
                        logger.warning(f"备选日期格式无效: {date_candidate}")

        logger.error("未找到有效日期信息")
        return None

    def _parse_lab_items(self, rec_texts):
        start_index = 0
        try:
            start_index = rec_texts.index('结果/单位') + 1
        except ValueError:
            if start_index == 0:
                try:
                    start_index = rec_texts.index('结果') + 1
                except ValueError:
                    start_index = 0
            if start_index == 0:
                try:
                    start_index = rec_texts.index('参考值') + 1
                except ValueError:
                    start_index = 0
        if start_index == 0:
            return []

        items = []
        valid_group = []
        i = start_index
        while i < len(rec_texts):
            current_text = rec_texts[i].strip()
            current_text = current_text.replace('↓', '').replace('↑', '').replace('$', '').replace('!', '').replace('←', '').replace('个', '').replace('>', '').replace('(', '').replace(')', '').replace('（', '').replace('）', '')
            # 新增指标替换规则
            replacement_rules = {
                "D二聚体": "D-二聚体",
                "糖类抗原CA125": "糖类抗原125",
                "糖类抗原19-9": "糖类抗原199",
                "糖类抗原19-9高值": "糖类抗原199",
                "淋巴细胞比率": "淋巴细胞%",
                "淋巴细胞百分比": "淋巴细胞%",
                "单核细胞比率": "单核细胞%",
                "单核细胞百分比": "单核细胞%",
                "中性粒细胞比率": "中性粒细胞%",
                "中性粒细胞百分比": "中性粒细胞%",
                "淋巴细胞绝对值": "淋巴细胞数",
                "淋巴细胞": "淋巴细胞数",
                "淋巴细胞计数": "淋巴细胞数",
                "单核细胞绝对值": "单核细胞数",
                "单核细胞": "单核细胞数",
                "中性粒细胞绝对值": "中性粒细胞数",
                "中性粒细胞": "中性粒细胞数",
                "中性粒细胞计数": "中性粒细胞数",
                "平均RBC体积": "平均红细胞体积",
                "RBC平均HGB": "平均血红蛋白含量",
                "平均红细胞血红蛋白含量": "平均血红蛋白含量",
                "平均血红蛋白量": "平均血红蛋白含量",
                "平均HGB浓度": "平均血红蛋白浓度",
                "平均红细胞血红蛋白浓度": "平均血红蛋白浓度",
                "平均PLT容积": "平均血小板体积",
                "PLT分布宽度": "血小板分布宽度",
                "RBC分布宽度-CV": "红细胞分布宽度",
                "红细胞分布宽度变异系数": "红细胞分布宽度",
                "RBC分布宽度-SD": "红细胞分布宽度标准差",
                "嗜酸性细胞绝对值": "嗜酸性细胞数",
                "嗜酸性粒细胞": "嗜酸性细胞数",
                "嗜酸性细胞比率": "嗜酸性细胞%",
                "嗜酸性粒细胞%": "嗜酸性细胞%",
                "嗜碱性细胞绝对值": "嗜碱性细胞数",
                "嗜碱性粒细胞": "嗜碱性细胞数",
                "嗜碱性细胞比率": "嗜碱性细胞%",
                "嗜碱性粒细胞%": "嗜碱性细胞%"
            }
            current_text = replacement_rules.get(current_text, current_text)

            # 跳过无效条目
            if self._is_invalid_entry(current_text):
                i += 1
                continue

            if current_text.startswith('报告医师：'):
                break
            if current_text.startswith('收样日期：'):
                break
            if current_text.startswith('该报告仅供参考'):
                break
            try:
                valid_group.append(current_text)

                if len(valid_group) == 3:
                    # 新增数字与汉字顺序判断
                    if valid_group[0].replace('.', '', 1).isdigit() and re.search(r'[\u4e00-\u9fff]', valid_group[1]):
                        valid_group[0], valid_group[1] = valid_group[1], valid_group[0]

                    # 调整参考值顺序
                    if valid_group[1].startswith('参考值：') and not valid_group[2].startswith('参考值：'):
                        # 交换第二、第三位元素
                        valid_group[1], valid_group[2] = valid_group[2], valid_group[1]

                    # 检查下一个元素是否为'单位：'开头
                    if i + 1 < len(rec_texts):
                        next_text = rec_texts[i+1].strip()
                        if next_text.startswith('单位：'):
                            # 合并单位到第二个字段
                            valid_group[1] += f' {next_text[3:]}'
                            i += 1  # 跳过已处理的单位字段
                        if next_text.startswith('参考范围：'):
                            # original_reference = valid_group[2]
                            valid_group[1] += f' {valid_group[2]}'
                            valid_group[2] = next_text
                            i += 1

                    print(valid_group)
                    items.append(self._parse_lab_item(valid_group))
                    valid_group = []
            except Exception as e:
                logger.error(f"Exception in _parse_lab_items: {str(e)}", exc_info=True)

            i += 1

        return items

    def _is_invalid_entry(self, text):
        return not text

    def _get_valid_group(self, rec_texts, start):
        group = []
        for i in range(start, min(start+4, len(rec_texts))):
            if self._is_invalid_entry(rec_texts[i]):
                break
            group.append(rec_texts[i])
        return group

    def _parse_lab_item(self, group):
        raw_value = (group[1].replace('<', '').replace('>', '') if len(group) >=2 else '').strip()
        value_match = self.VALUE_UNIT_PATTERN.match(raw_value)

        item_data = {
            'index_name': group[0],
            'index_value': value_match.group(1) if value_match else '',
            'index_unit': value_match.group(2).strip() if value_match else '',
            'reference_value': group[2].replace('参考值：', '').replace('参考范围：', '') if len(group)>=3 else '',
            'index_status': 'normal'
        }

        # 数值判断逻辑
        if item_data['index_value']:
            try:
                numeric_value = float(item_data['index_value'])
                
                index_info = self.db_manager.index_cache.get(item_data['index_name'], {})

                if index_info.get('max') is not None and numeric_value > index_info['max']:
                    item_data['index_status'] = 'high'
                elif index_info.get('min') is not None and numeric_value < index_info['min']:
                    item_data['index_status'] = 'low'
                # 处理只有单边参考值的情况
                elif index_info.get('min') and not index_info.get('max') and numeric_value < index_info['min']:
                    item_data['index_status'] = 'low'
                elif index_info.get('max') and not index_info.get('min') and numeric_value > index_info['max']:
                    item_data['index_status'] = 'high'

            except ValueError as e:
                logger.warning(f"Value conversion error for '{item_data['index_value']}': {str(e)}")

        return item_data

    def _save_to_database(self, data):
        conn = None
        try:
            conn = self.db_manager.get_connection()
            with conn.cursor() as cursor:
                # 判断整体状态
                has_abnormal = any(item['index_status'] in ('low', 'high')
                                 for item in data['lab_items'])

                # 插入主表
                cursor.execute(
                    "INSERT INTO medical_check (user_id, medical_date, medical_type, status) VALUES (%s, %s, %s, %s)",
                    ('1', data['medical_date'], 6, 'abnormal' if has_abnormal else 'normal')
                )
                medical_id = cursor.lastrowid

                # 插入明细
                for item in data['lab_items']:
                    self._insert_detail(cursor, medical_id, item)

                conn.commit()
                logger.info(f"成功保存{len(data['lab_items'])}项检测指标，整体状态：{'异常' if has_abnormal else '正常'}")
        except pymysql.Error as e:
            logger.error(f"数据库错误: {str(e)}")
            if conn:
                conn.rollback()
        finally:
            if conn:
                conn.close()

    def _insert_detail(self, cursor, medical_id, item):
        try:
            cursor.execute(
                """INSERT INTO medical_check_detail
                (medical_id, index_name, index_value, index_unit, reference_value, index_status)
                VALUES (%s, %s, %s, %s, %s, %s)""",
                (
                    medical_id,
                    item['index_name'],
                    item['index_value'],
                    item['index_unit'],
                    item['reference_value'],
                    item['index_status']
                )
            )
        except pymysql.Error as e:
            logger.error(f"Failed to insert lab item: {str(e)}")













