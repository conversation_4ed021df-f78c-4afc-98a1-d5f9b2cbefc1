{"input_path": "D:\\dev\\medicalReport\\ocr\\source\\20250415161344.jpg", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[1156, 39], [1260, 45], [1256, 114], [1152, 108]], [[80, 58], [247, 58], [247, 100], [80, 100]], [[972, 49], [1059, 49], [1059, 85], [972, 85]], [[1061, 50], [1123, 50], [1123, 80], [1061, 80]], [[1269, 53], [1350, 56], [1349, 106], [1268, 103]], [[438, 67], [468, 67], [468, 94], [438, 94]], [[980, 77], [1048, 77], [1048, 107], [980, 107]], [[592, 184], [842, 181], [842, 253], [592, 255]], [[519, 269], [919, 267], [919, 311], [519, 313]], [[33, 355], [251, 353], [251, 402], [33, 404]], [[85, 658], [254, 658], [254, 735], [85, 735]], [[271, 662], [452, 670], [449, 735], [268, 728]], [[1279, 674], [1350, 674], [1350, 713], [1279, 713]], [[95, 844], [545, 844], [545, 899], [95, 899]], [[91, 946], [778, 946], [778, 1000], [91, 1000]], [[89, 1227], [339, 1227], [339, 1294], [89, 1294]], [[87, 1409], [297, 1406], [297, 1463], [87, 1465]], [[1121, 1407], [1350, 1407], [1350, 1466], [1121, 1466]], [[90, 1559], [296, 1559], [296, 1612], [90, 1612]], [[1010, 1605], [1049, 1605], [1049, 1653], [1010, 1653]], [[1079, 1600], [1350, 1604], [1350, 1659], [1079, 1655]], [[93, 1653], [520, 1653], [520, 1698], [93, 1698]], [[94, 1795], [292, 1795], [292, 1847], [94, 1847]], [[1125, 1835], [1351, 1835], [1351, 1891], [1125, 1891]], [[93, 1888], [538, 1886], [538, 1927], [93, 1929]], [[92, 2030], [440, 2030], [440, 2078], [92, 2078]], [[1010, 2073], [1047, 2073], [1047, 2120], [1010, 2120]], [[1083, 2070], [1350, 2070], [1350, 2118], [1083, 2118]], [[95, 2122], [474, 2122], [474, 2160], [95, 2160]], [[91, 2260], [395, 2260], [395, 2312], [91, 2312]], [[913, 2306], [950, 2306], [950, 2354], [913, 2354]], [[985, 2308], [1348, 2305], [1348, 2350], [985, 2353]], [[94, 2355], [473, 2355], [473, 2396], [94, 2396]], [[91, 2495], [403, 2495], [403, 2546], [91, 2546]], [[1070, 2540], [1108, 2540], [1108, 2588], [1070, 2588]], [[1123, 2535], [1350, 2535], [1350, 2589], [1123, 2589]], [[94, 2588], [499, 2588], [499, 2629], [94, 2629]], [[91, 2728], [384, 2728], [384, 2779], [91, 2779]], [[965, 2772], [1004, 2772], [1004, 2820], [965, 2820]], [[1040, 2773], [1347, 2770], [1347, 2815], [1040, 2818]], [[93, 2820], [464, 2820], [464, 2864], [93, 2864]], [[91, 2961], [357, 2961], [357, 3013], [91, 3013]], [[938, 3002], [981, 3006], [976, 3057], [933, 3053]], [[1009, 3005], [1348, 3003], [1348, 3051], [1009, 3053]], [[93, 3053], [472, 3053], [472, 3097], [93, 3097]], [[91, 3194], [532, 3194], [532, 3248], [91, 3248]], [[912, 3241], [951, 3241], [951, 3288], [912, 3288]], [[986, 3239], [1346, 3239], [1346, 3284], [986, 3284]], [[92, 3285], [575, 3285], [575, 3332], [92, 3332]]], "text_det_params": {"limit_side_len": 64, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.6, "rec_texts": ["5", "15:26", "0.37", "5G", "88", "中", "KB/S", "报告查询", "fwcs.linkingcloud.cn", "肿瘤标志物", "*春香", "***岁", "申请科室：胰腺外科", "报告日期：2025-4-150:00:00", "项目明细", "项目名称", "结果/单位", "癌胚抗原", "↑", "79.30ng/ml", "参考值：0-5.20ng/ml", "甲胎蛋白", "2.17ng/ml", "参考值：0-10.00ng/ml", "糖类抗原CA125", "↑", "103.00U/ml", "参考值：0-35U/mL", "糖类抗原19-9", "↑", ">10000.00U/ml", "参考值：0-27U/mL", "糖类抗原72-4", "↑", "7.52U/ml", "参考值：0-6.90U/ml", "糖类抗原242", "↑", ">200.00U/ml", "参考值：0-20U/ml", "糖类抗原50", ">500.00IU/mL", "参考值：0-25IU/ml", "糖类抗原19-9(高值）", "↑", ">10000.00U/ml", "参考值：0.00-27.00U/ml"], "rec_scores": [0.7204758524894714, 0.9709070324897766, 0.9983885884284973, 0.9995080232620239, 0.9996963739395142, 0.7009556293487549, 0.9894997477531433, 0.9998033046722412, 0.9995158910751343, 0.9980467557907104, 0.996144711971283, 0.9844738245010376, 0.9968207478523254, 0.9782660603523254, 0.9997930526733398, 0.9999434351921082, 0.9985097646713257, 0.999783456325531, 0.9097772240638733, 0.9969465136528015, 0.9962002635002136, 0.9998073577880859, 0.99898362159729, 0.9924954175949097, 0.99928218126297, 0.8714520335197449, 0.9913570284843445, 0.984139621257782, 0.9995545148849487, 0.9664190411567688, 0.9916587471961975, 0.9868751168251038, 0.9997242093086243, 0.872027575969696, 0.9864485263824463, 0.9929698705673218, 0.9997595548629761, 0.8148424029350281, 0.9932191967964172, 0.9804790019989014, 0.9997754096984863, 0.9869441390037537, 0.9696509838104248, 0.9423894882202148, 0.6875903010368347, 0.987510621547699, 0.9952250719070435], "rec_polys": [[[1156, 39], [1260, 45], [1256, 114], [1152, 108]], [[80, 58], [247, 58], [247, 100], [80, 100]], [[972, 49], [1059, 49], [1059, 85], [972, 85]], [[1061, 50], [1123, 50], [1123, 80], [1061, 80]], [[1269, 53], [1350, 56], [1349, 106], [1268, 103]], [[438, 67], [468, 67], [468, 94], [438, 94]], [[980, 77], [1048, 77], [1048, 107], [980, 107]], [[592, 184], [842, 181], [842, 253], [592, 255]], [[519, 269], [919, 267], [919, 311], [519, 313]], [[33, 355], [251, 353], [251, 402], [33, 404]], [[85, 658], [254, 658], [254, 735], [85, 735]], [[271, 662], [452, 670], [449, 735], [268, 728]], [[95, 844], [545, 844], [545, 899], [95, 899]], [[91, 946], [778, 946], [778, 1000], [91, 1000]], [[89, 1227], [339, 1227], [339, 1294], [89, 1294]], [[87, 1409], [297, 1406], [297, 1463], [87, 1465]], [[1121, 1407], [1350, 1407], [1350, 1466], [1121, 1466]], [[90, 1559], [296, 1559], [296, 1612], [90, 1612]], [[1010, 1605], [1049, 1605], [1049, 1653], [1010, 1653]], [[1079, 1600], [1350, 1604], [1350, 1659], [1079, 1655]], [[93, 1653], [520, 1653], [520, 1698], [93, 1698]], [[94, 1795], [292, 1795], [292, 1847], [94, 1847]], [[1125, 1835], [1351, 1835], [1351, 1891], [1125, 1891]], [[93, 1888], [538, 1886], [538, 1927], [93, 1929]], [[92, 2030], [440, 2030], [440, 2078], [92, 2078]], [[1010, 2073], [1047, 2073], [1047, 2120], [1010, 2120]], [[1083, 2070], [1350, 2070], [1350, 2118], [1083, 2118]], [[95, 2122], [474, 2122], [474, 2160], [95, 2160]], [[91, 2260], [395, 2260], [395, 2312], [91, 2312]], [[913, 2306], [950, 2306], [950, 2354], [913, 2354]], [[985, 2308], [1348, 2305], [1348, 2350], [985, 2353]], [[94, 2355], [473, 2355], [473, 2396], [94, 2396]], [[91, 2495], [403, 2495], [403, 2546], [91, 2546]], [[1070, 2540], [1108, 2540], [1108, 2588], [1070, 2588]], [[1123, 2535], [1350, 2535], [1350, 2589], [1123, 2589]], [[94, 2588], [499, 2588], [499, 2629], [94, 2629]], [[91, 2728], [384, 2728], [384, 2779], [91, 2779]], [[965, 2772], [1004, 2772], [1004, 2820], [965, 2820]], [[1040, 2773], [1347, 2770], [1347, 2815], [1040, 2818]], [[93, 2820], [464, 2820], [464, 2864], [93, 2864]], [[91, 2961], [357, 2961], [357, 3013], [91, 3013]], [[1009, 3005], [1348, 3003], [1348, 3051], [1009, 3053]], [[93, 3053], [472, 3053], [472, 3097], [93, 3097]], [[91, 3194], [532, 3194], [532, 3248], [91, 3248]], [[912, 3241], [951, 3241], [951, 3288], [912, 3288]], [[986, 3239], [1346, 3239], [1346, 3284], [986, 3284]], [[92, 3285], [575, 3285], [575, 3332], [92, 3332]]], "rec_boxes": [[1152, 39, 1260, 114], [80, 58, 247, 100], [972, 49, 1059, 85], [1061, 50, 1123, 80], [1268, 53, 1350, 106], [438, 67, 468, 94], [980, 77, 1048, 107], [592, 181, 842, 255], [519, 267, 919, 313], [33, 353, 251, 404], [85, 658, 254, 735], [268, 662, 452, 735], [95, 844, 545, 899], [91, 946, 778, 1000], [89, 1227, 339, 1294], [87, 1406, 297, 1465], [1121, 1407, 1350, 1466], [90, 1559, 296, 1612], [1010, 1605, 1049, 1653], [1079, 1600, 1350, 1659], [93, 1653, 520, 1698], [94, 1795, 292, 1847], [1125, 1835, 1351, 1891], [93, 1886, 538, 1929], [92, 2030, 440, 2078], [1010, 2073, 1047, 2120], [1083, 2070, 1350, 2118], [95, 2122, 474, 2160], [91, 2260, 395, 2312], [913, 2306, 950, 2354], [985, 2305, 1348, 2353], [94, 2355, 473, 2396], [91, 2495, 403, 2546], [1070, 2540, 1108, 2588], [1123, 2535, 1350, 2589], [94, 2588, 499, 2629], [91, 2728, 384, 2779], [965, 2772, 1004, 2820], [1040, 2770, 1347, 2818], [93, 2820, 464, 2864], [91, 2961, 357, 3013], [1009, 3003, 1348, 3053], [93, 3053, 472, 3097], [91, 3194, 532, 3248], [912, 3241, 951, 3288], [986, 3239, 1346, 3284], [92, 3285, 575, 3332]]}