{"input_path": "D:\\dev\\medicalReport\\ocr\\source\\0613.jpg", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[71, 44], [229, 48], [227, 107], [69, 103]], [[849, 39], [1256, 39], [1256, 116], [849, 116]], [[1262, 53], [1350, 53], [1350, 105], [1262, 105]], [[591, 180], [844, 180], [844, 252], [591, 252]], [[32, 208], [111, 208], [111, 283], [32, 283]], [[512, 259], [921, 263], [920, 313], [512, 309]], [[28, 348], [294, 348], [294, 405], [28, 405]], [[79, 649], [456, 662], [454, 744], [76, 732]], [[96, 845], [842, 845], [842, 894], [96, 894]], [[92, 946], [774, 946], [774, 996], [92, 996]], [[88, 1224], [338, 1224], [338, 1288], [88, 1288]], [[84, 1406], [297, 1402], [299, 1461], [85, 1465]], [[1121, 1404], [1350, 1404], [1350, 1463], [1121, 1463]], [[92, 1559], [345, 1559], [345, 1610], [92, 1610]], [[1012, 1603], [1058, 1603], [1058, 1649], [1012, 1649]], [[1078, 1596], [1352, 1596], [1352, 1655], [1078, 1655]], [[92, 1651], [561, 1651], [561, 1693], [92, 1693]], [[90, 1793], [341, 1793], [341, 1844], [90, 1844]], [[1123, 1837], [1166, 1837], [1166, 1883], [1123, 1883]], [[1192, 1833], [1348, 1833], [1348, 1887], [1192, 1887]], [[94, 1885], [491, 1885], [491, 1923], [94, 1923]], [[92, 2026], [341, 2026], [341, 2076], [92, 2076]], [[1200, 2065], [1352, 2065], [1352, 2124], [1200, 2124]], [[92, 2118], [422, 2118], [422, 2157], [92, 2157]], [[96, 2262], [386, 2262], [386, 2306], [96, 2306]], [[1124, 2307], [1171, 2301], [1177, 2348], [1130, 2354]], [[1200, 2299], [1348, 2299], [1348, 2352], [1200, 2352]], [[92, 2354], [422, 2354], [422, 2393], [92, 2393]], [[90, 2494], [343, 2494], [343, 2544], [90, 2544]], [[1095, 2533], [1350, 2533], [1350, 2584], [1095, 2584]], [[90, 2586], [540, 2582], [540, 2624], [90, 2628]], [[94, 2726], [343, 2726], [343, 2777], [94, 2777]], [[1084, 2763], [1352, 2763], [1352, 2820], [1084, 2820]], [[90, 2820], [553, 2816], [553, 2858], [90, 2862]], [[94, 2963], [390, 2963], [390, 3007], [94, 3007]], [[1078, 2996], [1352, 2996], [1352, 3055], [1078, 3055]], [[1011, 3006], [1056, 3006], [1056, 3048], [1011, 3048]], [[94, 3053], [548, 3053], [548, 3092], [94, 3092]], [[90, 3193], [341, 3193], [341, 3243], [90, 3243]], [[964, 3239], [1003, 3239], [1003, 3283], [964, 3283]], [[1033, 3230], [1350, 3230], [1350, 3287], [1033, 3287]], [[92, 3285], [564, 3285], [564, 3328], [92, 3328]], [[90, 3425], [291, 3425], [291, 3477], [90, 3477]], [[1144, 3469], [1196, 3469], [1196, 3521], [1144, 3521]], [[1213, 3460], [1358, 3460], [1358, 3530], [1213, 3530]], [[92, 3521], [459, 3521], [459, 3560], [92, 3560]], [[90, 3661], [345, 3661], [345, 3710], [90, 3710]], [[1121, 3703], [1174, 3703], [1174, 3755], [1121, 3755]], [[1192, 3698], [1352, 3698], [1352, 3756], [1192, 3756]], [[92, 3755], [472, 3755], [472, 3791], [92, 3791]], [[90, 3893], [392, 3893], [392, 3942], [90, 3942]], [[1207, 3929], [1356, 3929], [1356, 3990], [1207, 3990]], [[94, 3988], [461, 3988], [461, 4027], [94, 4027]], [[90, 4128], [396, 4128], [396, 4172], [90, 4172]], [[1179, 4156], [1360, 4170], [1354, 4236], [1174, 4223]], [[92, 4222], [499, 4222], [499, 4261], [92, 4261]], [[92, 4360], [394, 4364], [393, 4410], [92, 4406]], [[1147, 4395], [1357, 4395], [1357, 4461], [1147, 4461]], [[94, 4454], [474, 4454], [474, 4493], [94, 4493]], [[90, 4594], [343, 4594], [343, 4644], [90, 4644]], [[1007, 4640], [1048, 4640], [1048, 4682], [1007, 4682]], [[1076, 4631], [1352, 4631], [1352, 4688], [1076, 4688]], [[92, 4684], [576, 4684], [576, 4726], [92, 4726]], [[90, 4828], [377, 4828], [377, 4877], [90, 4877]], [[1239, 4863], [1358, 4863], [1358, 4925], [1239, 4925]], [[92, 4921], [441, 4921], [441, 4958], [92, 4958]], [[92, 5061], [343, 5061], [343, 5111], [92, 5111]], [[1132, 5106], [1178, 5106], [1178, 5152], [1132, 5152]], [[1206, 5100], [1350, 5100], [1350, 5153], [1206, 5153]], [[92, 5155], [484, 5155], [484, 5192], [92, 5192]], [[90, 5295], [377, 5295], [377, 5339], [90, 5339]], [[1178, 5336], [1230, 5336], [1230, 5387], [1178, 5387]], [[1249, 5330], [1356, 5330], [1356, 5393], [1249, 5393]], [[89, 5383], [403, 5387], [403, 5432], [88, 5427]], [[92, 5527], [394, 5527], [394, 5577], [92, 5577]], [[1065, 5566], [1350, 5566], [1350, 5617], [1065, 5617]], [[90, 5621], [609, 5617], [609, 5656], [90, 5660]], [[90, 5761], [390, 5761], [390, 5810], [90, 5810]], [[1189, 5799], [1352, 5799], [1352, 5858], [1189, 5858]], [[92, 5856], [444, 5856], [444, 5893], [92, 5893]], [[92, 5995], [394, 5995], [394, 6044], [92, 6044]], [[1061, 6031], [1350, 6031], [1350, 6088], [1061, 6088]], [[92, 6088], [615, 6088], [615, 6125], [92, 6125]], [[94, 6230], [388, 6230], [388, 6274], [94, 6274]], [[1189, 6265], [1354, 6265], [1354, 6324], [1189, 6324]], [[92, 6322], [489, 6322], [489, 6361], [92, 6361]], [[90, 6464], [476, 6464], [476, 6508], [90, 6508]], [[1280, 6494], [1359, 6500], [1354, 6563], [1275, 6557]], [[92, 6556], [442, 6556], [442, 6595], [92, 6595]], [[88, 6696], [482, 6696], [482, 6745], [88, 6745]], [[1198, 6728], [1352, 6733], [1351, 6794], [1196, 6789]], [[90, 6786], [472, 6786], [472, 6830], [90, 6830]], [[92, 6928], [394, 6928], [394, 6979], [92, 6979]], [[1247, 6963], [1356, 6963], [1356, 7027], [1247, 7027]], [[88, 7016], [276, 7016], [276, 7068], [88, 7068]]], "text_det_params": {"limit_side_len": 64, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.0, "rec_texts": ["16:44", "0", "80", "报告查询", "×", "fwcs.linkingcloud.cn", "东院XN9000", "*春香***岁", "申请科室：胰腺肿瘤临床研究门诊", "报告日期：2025-6-130:00:00", "项目明细", "项目名称", "结果/单位", "白细胞计数", "↑", "2.2*10^9/L", "参考值：3.5-9.5*10^9/L", "淋巴细胞%", "↑", "74.4%", "参考值：20.0-50.0%", "单核细胞%", "10.0%", "参考值：3.0-10%", "中性粒细胞%", "山", "10.5%", "参考值：40-75%", "淋巴细胞数", "1.6*10^9/L", "参考值：1.1-3.2*10^9/L", "单核细胞数", "0.2*10^9/L", "参考值：0.1-0.6*10^9/L", "中性粒细胞数", "0.2*10^9/L", "↓", "参考值：1.8-6.3*10^9/L", "红细胞计数", "↓", "2.86*10^12/L", "参考值：3.8-5.1*10^12/L", "血红蛋白", "", "80g/l", "参考值：115-150g/", "红细胞压积", "", "24.7%", "参考值：女35-45%", "平均RBC体积", "86.4fl", "参考值：82.0-100fl", "RBC平均HGB", "28.0pg", "参考值：27.0-34.0pg", "平均HGB浓度", "324.0g/l", "参考值：316-354g/", "血小板计数", "↓", "123*10^9/L", "参考值：125-350*10^9/L", "平均PLT容积", "9.9fl", "参考值：7.4-10.4f", "血小板压积", "↓", "0.12%", "参考值：0.15-0.30%", "PLT分布宽度", "", "11%", "参考值：13-21%", "嗜酸性细胞数", "0.10*10^9/L", "参考值：0.02-0.52*10^9/1", "嗜酸性细胞%", "4.60%", "参考值：0.4-8.0%", "嗜碱性细胞数", "0.01*10^9/L", "参考值：0.00-0.06*10^9/L", "嗜碱性细胞%", "0.50%", "参考值：0.00-1.00%", "RBC分布宽度-SD", "43", "参考值：35-44FL", "RBC分布宽度-CV", "14.0%", "参考值：11.6-14.4%", "中性淋巴比值", "0.13", "参考值：."], "rec_scores": [0.9881019592285156, 0.4232541620731354, 0.9967387318611145, 0.9998635053634644, 0.9047001600265503, 0.9904868006706238, 0.9990326166152954, 0.9667936563491821, 0.9857194423675537, 0.9845545887947083, 0.9996514916419983, 0.9998422861099243, 0.9984720945358276, 0.9994519948959351, 0.8849048614501953, 0.9635758399963379, 0.9835350513458252, 0.9968708157539368, 0.9823611974716187, 0.9986327290534973, 0.9843936562538147, 0.9996061325073242, 0.9989601969718933, 0.9828181862831116, 0.9963511824607849, 0.23645079135894775, 0.9992295503616333, 0.9888119697570801, 0.9993292689323425, 0.9527456164360046, 0.9630494713783264, 0.9998494982719421, 0.9749799966812134, 0.9681974053382874, 0.998725175857544, 0.9597139358520508, 0.980395495891571, 0.9700331091880798, 0.9993834495544434, 0.9911435842514038, 0.9802914261817932, 0.9846483469009399, 0.9997738599777222, 0.0, 0.949647068977356, 0.9591919779777527, 0.9998070597648621, 0.0, 0.9989057779312134, 0.9885500073432922, 0.9993917346000671, 0.9898414611816406, 0.9579970240592957, 0.9994915127754211, 0.9996230006217957, 0.9888149499893188, 0.9953432083129883, 0.9857590198516846, 0.9771153330802917, 0.9990634918212891, 0.9897757768630981, 0.9712088704109192, 0.9714396595954895, 0.9993643760681152, 0.9689451456069946, 0.9824005365371704, 0.9996593594551086, 0.8639326691627502, 0.9982024431228638, 0.9803313612937927, 0.9962826371192932, 0.0, 0.9992097020149231, 0.9895257949829102, 0.9997934699058533, 0.9750238060951233, 0.9694480895996094, 0.9965967535972595, 0.9985008239746094, 0.992175281047821, 0.9995152354240417, 0.9768400192260742, 0.9508711099624634, 0.9984527230262756, 0.9992329478263855, 0.9924078583717346, 0.9936615824699402, 0.9990346431732178, 0.9850314855575562, 0.9970919489860535, 0.9976151585578918, 0.9951320886611938, 0.9982301592826843, 0.9991364479064941, 0.9473331570625305], "rec_polys": [[[71, 44], [229, 48], [227, 107], [69, 103]], [[849, 39], [1256, 39], [1256, 116], [849, 116]], [[1262, 53], [1350, 53], [1350, 105], [1262, 105]], [[591, 180], [844, 180], [844, 252], [591, 252]], [[32, 208], [111, 208], [111, 283], [32, 283]], [[512, 259], [921, 263], [920, 313], [512, 309]], [[28, 348], [294, 348], [294, 405], [28, 405]], [[79, 649], [456, 662], [454, 744], [76, 732]], [[96, 845], [842, 845], [842, 894], [96, 894]], [[92, 946], [774, 946], [774, 996], [92, 996]], [[88, 1224], [338, 1224], [338, 1288], [88, 1288]], [[84, 1406], [297, 1402], [299, 1461], [85, 1465]], [[1121, 1404], [1350, 1404], [1350, 1463], [1121, 1463]], [[92, 1559], [345, 1559], [345, 1610], [92, 1610]], [[1012, 1603], [1058, 1603], [1058, 1649], [1012, 1649]], [[1078, 1596], [1352, 1596], [1352, 1655], [1078, 1655]], [[92, 1651], [561, 1651], [561, 1693], [92, 1693]], [[90, 1793], [341, 1793], [341, 1844], [90, 1844]], [[1123, 1837], [1166, 1837], [1166, 1883], [1123, 1883]], [[1192, 1833], [1348, 1833], [1348, 1887], [1192, 1887]], [[94, 1885], [491, 1885], [491, 1923], [94, 1923]], [[92, 2026], [341, 2026], [341, 2076], [92, 2076]], [[1200, 2065], [1352, 2065], [1352, 2124], [1200, 2124]], [[92, 2118], [422, 2118], [422, 2157], [92, 2157]], [[96, 2262], [386, 2262], [386, 2306], [96, 2306]], [[1124, 2307], [1171, 2301], [1177, 2348], [1130, 2354]], [[1200, 2299], [1348, 2299], [1348, 2352], [1200, 2352]], [[92, 2354], [422, 2354], [422, 2393], [92, 2393]], [[90, 2494], [343, 2494], [343, 2544], [90, 2544]], [[1095, 2533], [1350, 2533], [1350, 2584], [1095, 2584]], [[90, 2586], [540, 2582], [540, 2624], [90, 2628]], [[94, 2726], [343, 2726], [343, 2777], [94, 2777]], [[1084, 2763], [1352, 2763], [1352, 2820], [1084, 2820]], [[90, 2820], [553, 2816], [553, 2858], [90, 2862]], [[94, 2963], [390, 2963], [390, 3007], [94, 3007]], [[1078, 2996], [1352, 2996], [1352, 3055], [1078, 3055]], [[1011, 3006], [1056, 3006], [1056, 3048], [1011, 3048]], [[94, 3053], [548, 3053], [548, 3092], [94, 3092]], [[90, 3193], [341, 3193], [341, 3243], [90, 3243]], [[964, 3239], [1003, 3239], [1003, 3283], [964, 3283]], [[1033, 3230], [1350, 3230], [1350, 3287], [1033, 3287]], [[92, 3285], [564, 3285], [564, 3328], [92, 3328]], [[90, 3425], [291, 3425], [291, 3477], [90, 3477]], [[1144, 3469], [1196, 3469], [1196, 3521], [1144, 3521]], [[1213, 3460], [1358, 3460], [1358, 3530], [1213, 3530]], [[92, 3521], [459, 3521], [459, 3560], [92, 3560]], [[90, 3661], [345, 3661], [345, 3710], [90, 3710]], [[1121, 3703], [1174, 3703], [1174, 3755], [1121, 3755]], [[1192, 3698], [1352, 3698], [1352, 3756], [1192, 3756]], [[92, 3755], [472, 3755], [472, 3791], [92, 3791]], [[90, 3893], [392, 3893], [392, 3942], [90, 3942]], [[1207, 3929], [1356, 3929], [1356, 3990], [1207, 3990]], [[94, 3988], [461, 3988], [461, 4027], [94, 4027]], [[90, 4128], [396, 4128], [396, 4172], [90, 4172]], [[1179, 4156], [1360, 4170], [1354, 4236], [1174, 4223]], [[92, 4222], [499, 4222], [499, 4261], [92, 4261]], [[92, 4360], [394, 4364], [393, 4410], [92, 4406]], [[1147, 4395], [1357, 4395], [1357, 4461], [1147, 4461]], [[94, 4454], [474, 4454], [474, 4493], [94, 4493]], [[90, 4594], [343, 4594], [343, 4644], [90, 4644]], [[1007, 4640], [1048, 4640], [1048, 4682], [1007, 4682]], [[1076, 4631], [1352, 4631], [1352, 4688], [1076, 4688]], [[92, 4684], [576, 4684], [576, 4726], [92, 4726]], [[90, 4828], [377, 4828], [377, 4877], [90, 4877]], [[1239, 4863], [1358, 4863], [1358, 4925], [1239, 4925]], [[92, 4921], [441, 4921], [441, 4958], [92, 4958]], [[92, 5061], [343, 5061], [343, 5111], [92, 5111]], [[1132, 5106], [1178, 5106], [1178, 5152], [1132, 5152]], [[1206, 5100], [1350, 5100], [1350, 5153], [1206, 5153]], [[92, 5155], [484, 5155], [484, 5192], [92, 5192]], [[90, 5295], [377, 5295], [377, 5339], [90, 5339]], [[1178, 5336], [1230, 5336], [1230, 5387], [1178, 5387]], [[1249, 5330], [1356, 5330], [1356, 5393], [1249, 5393]], [[89, 5383], [403, 5387], [403, 5432], [88, 5427]], [[92, 5527], [394, 5527], [394, 5577], [92, 5577]], [[1065, 5566], [1350, 5566], [1350, 5617], [1065, 5617]], [[90, 5621], [609, 5617], [609, 5656], [90, 5660]], [[90, 5761], [390, 5761], [390, 5810], [90, 5810]], [[1189, 5799], [1352, 5799], [1352, 5858], [1189, 5858]], [[92, 5856], [444, 5856], [444, 5893], [92, 5893]], [[92, 5995], [394, 5995], [394, 6044], [92, 6044]], [[1061, 6031], [1350, 6031], [1350, 6088], [1061, 6088]], [[92, 6088], [615, 6088], [615, 6125], [92, 6125]], [[94, 6230], [388, 6230], [388, 6274], [94, 6274]], [[1189, 6265], [1354, 6265], [1354, 6324], [1189, 6324]], [[92, 6322], [489, 6322], [489, 6361], [92, 6361]], [[90, 6464], [476, 6464], [476, 6508], [90, 6508]], [[1280, 6494], [1359, 6500], [1354, 6563], [1275, 6557]], [[92, 6556], [442, 6556], [442, 6595], [92, 6595]], [[88, 6696], [482, 6696], [482, 6745], [88, 6745]], [[1198, 6728], [1352, 6733], [1351, 6794], [1196, 6789]], [[90, 6786], [472, 6786], [472, 6830], [90, 6830]], [[92, 6928], [394, 6928], [394, 6979], [92, 6979]], [[1247, 6963], [1356, 6963], [1356, 7027], [1247, 7027]], [[88, 7016], [276, 7016], [276, 7068], [88, 7068]]], "rec_boxes": [[69, 44, 229, 107], [849, 39, 1256, 116], [1262, 53, 1350, 105], [591, 180, 844, 252], [32, 208, 111, 283], [512, 259, 921, 313], [28, 348, 294, 405], [76, 649, 456, 744], [96, 845, 842, 894], [92, 946, 774, 996], [88, 1224, 338, 1288], [84, 1402, 299, 1465], [1121, 1404, 1350, 1463], [92, 1559, 345, 1610], [1012, 1603, 1058, 1649], [1078, 1596, 1352, 1655], [92, 1651, 561, 1693], [90, 1793, 341, 1844], [1123, 1837, 1166, 1883], [1192, 1833, 1348, 1887], [94, 1885, 491, 1923], [92, 2026, 341, 2076], [1200, 2065, 1352, 2124], [92, 2118, 422, 2157], [96, 2262, 386, 2306], [1124, 2301, 1177, 2354], [1200, 2299, 1348, 2352], [92, 2354, 422, 2393], [90, 2494, 343, 2544], [1095, 2533, 1350, 2584], [90, 2582, 540, 2628], [94, 2726, 343, 2777], [1084, 2763, 1352, 2820], [90, 2816, 553, 2862], [94, 2963, 390, 3007], [1078, 2996, 1352, 3055], [1011, 3006, 1056, 3048], [94, 3053, 548, 3092], [90, 3193, 341, 3243], [964, 3239, 1003, 3283], [1033, 3230, 1350, 3287], [92, 3285, 564, 3328], [90, 3425, 291, 3477], [1144, 3469, 1196, 3521], [1213, 3460, 1358, 3530], [92, 3521, 459, 3560], [90, 3661, 345, 3710], [1121, 3703, 1174, 3755], [1192, 3698, 1352, 3756], [92, 3755, 472, 3791], [90, 3893, 392, 3942], [1207, 3929, 1356, 3990], [94, 3988, 461, 4027], [90, 4128, 396, 4172], [1174, 4156, 1360, 4236], [92, 4222, 499, 4261], [92, 4360, 394, 4410], [1147, 4395, 1357, 4461], [94, 4454, 474, 4493], [90, 4594, 343, 4644], [1007, 4640, 1048, 4682], [1076, 4631, 1352, 4688], [92, 4684, 576, 4726], [90, 4828, 377, 4877], [1239, 4863, 1358, 4925], [92, 4921, 441, 4958], [92, 5061, 343, 5111], [1132, 5106, 1178, 5152], [1206, 5100, 1350, 5153], [92, 5155, 484, 5192], [90, 5295, 377, 5339], [1178, 5336, 1230, 5387], [1249, 5330, 1356, 5393], [88, 5383, 403, 5432], [92, 5527, 394, 5577], [1065, 5566, 1350, 5617], [90, 5617, 609, 5660], [90, 5761, 390, 5810], [1189, 5799, 1352, 5858], [92, 5856, 444, 5893], [92, 5995, 394, 6044], [1061, 6031, 1350, 6088], [92, 6088, 615, 6125], [94, 6230, 388, 6274], [1189, 6265, 1354, 6324], [92, 6322, 489, 6361], [90, 6464, 476, 6508], [1275, 6494, 1359, 6563], [92, 6556, 442, 6595], [88, 6696, 482, 6745], [1196, 6728, 1352, 6794], [90, 6786, 472, 6830], [92, 6928, 394, 6979], [1247, 6963, 1356, 7027], [88, 7016, 276, 7068]]}