{"input_path": "D:\\dev\\medicalReport\\ocr\\source\\20250521143205.jpg", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": true, "use_doc_unwarping": true}, "angle": 0}, "dt_polys": [[[87, 61], [304, 160], [279, 212], [62, 113]], [[515, 94], [928, 125], [923, 187], [510, 157]], [[69, 396], [469, 501], [442, 598], [43, 493]], [[66, 586], [869, 665], [857, 786], [53, 707]], [[64, 698], [796, 767], [786, 874], [53, 805]], [[76, 1011], [339, 1055], [327, 1127], [64, 1083]], [[72, 1203], [296, 1236], [286, 1301], [62, 1267]], [[1119, 1255], [1361, 1235], [1367, 1300], [1125, 1320]], [[78, 1367], [342, 1405], [333, 1464], [69, 1426]], [[1080, 1452], [1362, 1433], [1367, 1498], [1085, 1516]], [[73, 1466], [563, 1511], [557, 1568], [67, 1523]], [[74, 1616], [336, 1644], [330, 1701], [68, 1674]], [[1195, 1686], [1362, 1674], [1366, 1735], [1199, 1746]], [[74, 1713], [491, 1748], [486, 1798], [70, 1762]], [[74, 1862], [335, 1883], [331, 1940], [70, 1919]], [[1223, 1931], [1362, 1916], [1369, 1979], [1230, 1994]], [[75, 1961], [417, 1985], [414, 2030], [72, 2006]], [[76, 2109], [384, 2129], [380, 2185], [72, 2164]], [[1197, 2173], [1362, 2164], [1366, 2225], [1201, 2234]], [[73, 2207], [415, 2225], [413, 2274], [70, 2256]], [[73, 2361], [334, 2370], [332, 2425], [71, 2417]], [[1088, 2422], [1361, 2407], [1364, 2464], [1092, 2479]], [[74, 2461], [535, 2475], [533, 2519], [73, 2505]], [[75, 2608], [337, 2621], [334, 2678], [72, 2665]], [[1085, 2663], [1363, 2650], [1366, 2707], [1087, 2720]], [[76, 2709], [544, 2723], [543, 2773], [75, 2759]], [[77, 2862], [387, 2875], [385, 2932], [74, 2919]], [[1087, 2904], [1363, 2893], [1366, 2958], [1089, 2968]], [[75, 2968], [546, 2973], [545, 3018], [75, 3012]], [[70, 3117], [336, 3121], [335, 3178], [69, 3174]], [[1036, 3158], [1359, 3142], [1362, 3199], [1039, 3215]], [[75, 3220], [555, 3220], [555, 3264], [75, 3264]], [[73, 3369], [281, 3369], [281, 3426], [73, 3426]], [[1200, 3387], [1372, 3387], [1372, 3463], [1200, 3463]], [[1129, 3402], [1181, 3402], [1181, 3456], [1129, 3456]], [[71, 3471], [453, 3465], [454, 3514], [72, 3520]], [[71, 3621], [331, 3617], [332, 3671], [72, 3675]], [[1125, 3651], [1178, 3651], [1178, 3704], [1125, 3704]], [[1195, 3644], [1367, 3637], [1370, 3703], [1198, 3710]], [[73, 3719], [464, 3707], [465, 3752], [74, 3763]], [[73, 3865], [382, 3858], [383, 3910], [74, 3916]], [[1229, 3893], [1368, 3885], [1372, 3946], [1232, 3953]], [[73, 3960], [453, 3954], [454, 3998], [74, 4004]], [[71, 4110], [387, 4101], [389, 4151], [72, 4159]], [[1118, 4139], [1170, 4139], [1170, 4190], [1118, 4190]], [[1189, 4130], [1371, 4137], [1369, 4204], [1186, 4197]], [[73, 4201], [491, 4197], [491, 4243], [73, 4247]], [[75, 4350], [384, 4350], [384, 4400], [75, 4400]], [[1086, 4380], [1148, 4380], [1148, 4439], [1086, 4439]], [[1158, 4374], [1370, 4370], [1371, 4438], [1160, 4443]], [[77, 4448], [463, 4448], [463, 4487], [77, 4487]], [[70, 4584], [334, 4588], [333, 4645], [69, 4641]], [[1079, 4627], [1365, 4617], [1368, 4675], [1082, 4686]], [[73, 4685], [574, 4689], [574, 4733], [73, 4729]], [[72, 4834], [368, 4844], [366, 4894], [71, 4884]], [[1245, 4867], [1375, 4859], [1380, 4929], [1250, 4937]], [[74, 4930], [433, 4938], [432, 4982], [73, 4974]], [[73, 5077], [333, 5087], [330, 5139], [71, 5128]], [[1212, 5121], [1369, 5109], [1374, 5176], [1217, 5188]], [[74, 5173], [477, 5185], [475, 5231], [73, 5219]], [[73, 5322], [368, 5332], [366, 5384], [71, 5373]], [[1176, 5376], [1234, 5376], [1234, 5436], [1176, 5436]], [[1247, 5371], [1370, 5364], [1374, 5428], [1252, 5436]], [[74, 5414], [392, 5424], [391, 5474], [73, 5464]], [[73, 5561], [385, 5573], [383, 5630], [71, 5618]], [[1056, 5622], [1367, 5622], [1367, 5679], [1056, 5679]], [[74, 5663], [598, 5674], [597, 5718], [73, 5707]], [[72, 5810], [379, 5820], [378, 5871], [71, 5861]], [[1206, 5876], [1369, 5864], [1374, 5930], [1211, 5942]], [[72, 5907], [435, 5919], [434, 5963], [71, 5952]], [[73, 6053], [383, 6065], [381, 6120], [71, 6108]], [[1056, 6119], [1369, 6119], [1369, 6182], [1056, 6182]], [[74, 6154], [606, 6166], [605, 6208], [73, 6197]], [[74, 6301], [379, 6311], [378, 6363], [73, 6353]], [[1198, 6372], [1369, 6368], [1371, 6428], [1199, 6433]], [[74, 6403], [482, 6411], [481, 6455], [73, 6447]], [[71, 6556], [471, 6556], [471, 6606], [71, 6606]], [[1301, 6611], [1376, 6611], [1376, 6677], [1301, 6677]], [[75, 6653], [437, 6653], [437, 6698], [75, 6698]], [[73, 6802], [471, 6802], [471, 6854], [73, 6854]], [[1211, 6858], [1373, 6853], [1375, 6920], [1213, 6925]], [[74, 6894], [465, 6900], [465, 6946], [73, 6940]], [[75, 7044], [384, 7044], [384, 7101], [75, 7101]], [[1253, 7094], [1376, 7089], [1379, 7162], [1257, 7168]], [[68, 7139], [266, 7139], [266, 7198], [68, 7198]]], "text_det_params": {"limit_side_len": 64, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.0, "rec_texts": ["院XN9000", "fwcs.linkingcloud.cn", "*春香***岁", "申请科室：胰腺肿瘤临床研究门诊", "报告日期：2025-5-210:00:00", "项目明细", "项目名称", "结果/单位", "白细胞计数", "5.5*10^9/L", "参考值：3.5-9.5*10^9/L", "淋巴细胞%", "39.4%", "参考值：20.0-50.0%", "单核细胞%", "8.8%", "参考值：3.0-10%", "中性粒细胞%", "49.8%", "参考值：40-75%", "淋巴细胞数", "2.2*10^9/L", "参考值：1.1-3.2*10^9/L", "单核细胞数", "0.5*10^9/L", "参考值：0.1-0.6*10^9/", "中性粒细胞数", "2.7*10^9/L", "参考值：1.8-6.3*10^9/L", "红细胞计数", "3.75*10^12/L", "参考值：3.8-5.1*10^12/L", "血红蛋白", "100g/l", "↑", "参考值：115-150g/l", "红细胞压积", "", "32.3%", "参考值：女35-45%", "平均RBC体积", "86.1fl", "参考值：82.0-100fl", "RBC平均HGB", "↓", "26.7pg", "参考值：27.0-34.0pg", "平均HGB浓度", "", "310.0g/l", "参考值：316-354g/", "血小板计数", "182*10^9/L", "参考值：125-350*10^9/L", "平均PLT容积", "9.5fl", "参考值：7.4-10.4fl", "血小板压积", "0.17%", "参考值：0.15-0.30%", "PLT分布宽度", "", "10%", "参考值：13-21%", "嗜酸性细胞数", "0.09*10^9/L", "参考值：0.02-0.52*10^9/L", "嗜酸性细胞%", "1.60%", "参考值：0.4-8.0%", "嗜碱性细胞数", "0.02*10^9/L", "参考值：0.00-0.06*10^9/", "嗜碱性细胞%", "0.40%", "参考值：0.00-1.00%", "RBC分布宽度-SD", "41", "参考值：35-44FL", "RBC分布宽度-CV", "13.2%", "参考值：11.6-14.4%", "中性淋巴比值", "1.23", "参考值：."], "rec_scores": [0.9759184122085571, 0.999234676361084, 0.9235863089561462, 0.9981135129928589, 0.9821246862411499, 0.9995735883712769, 0.9997943639755249, 0.9939457774162292, 0.9992332458496094, 0.9782108068466187, 0.9790198802947998, 0.9779393076896667, 0.9991605877876282, 0.9979764819145203, 0.9992589950561523, 0.9992088079452515, 0.9959657788276672, 0.9987258315086365, 0.9995406866073608, 0.9924961924552917, 0.9988416433334351, 0.9798388481140137, 0.9522668123245239, 0.9998975992202759, 0.9634836912155151, 0.9479316473007202, 0.999598503112793, 0.9764624834060669, 0.9294888973236084, 0.9995965957641602, 0.9673598408699036, 0.9490944743156433, 0.9997410774230957, 0.9784762859344482, 0.6808992624282837, 0.9897055625915527, 0.9996280670166016, 0.0, 0.9993507266044617, 0.9900366067886353, 0.9995595812797546, 0.9574431777000427, 0.9853255152702332, 0.9997279047966003, 0.8707923889160156, 0.9990218281745911, 0.980480432510376, 0.9982585906982422, 0.0, 0.9753760099411011, 0.9727963805198669, 0.9991371035575867, 0.9798981547355652, 0.9722474217414856, 0.9994329810142517, 0.9749131202697754, 0.9757922291755676, 0.9995156526565552, 0.999516487121582, 0.991999089717865, 0.9990583062171936, 0.0, 0.9887240529060364, 0.9909513592720032, 0.9998195171356201, 0.9743719696998596, 0.9585771560668945, 0.9983479380607605, 0.9994939565658569, 0.9950529932975769, 0.9995553493499756, 0.9853467345237732, 0.9768797159194946, 0.9987781643867493, 0.9988963007926941, 0.995236873626709, 0.9990432858467102, 0.9996908903121948, 0.9935489296913147, 0.9937840700149536, 0.9995013475418091, 0.9944962859153748, 0.9982927441596985, 0.9996953010559082, 0.9749994277954102], "rec_polys": [[[87, 61], [304, 160], [279, 212], [62, 113]], [[515, 94], [928, 125], [923, 187], [510, 157]], [[69, 396], [469, 501], [442, 598], [43, 493]], [[66, 586], [869, 665], [857, 786], [53, 707]], [[64, 698], [796, 767], [786, 874], [53, 805]], [[76, 1011], [339, 1055], [327, 1127], [64, 1083]], [[72, 1203], [296, 1236], [286, 1301], [62, 1267]], [[1119, 1255], [1361, 1235], [1367, 1300], [1125, 1320]], [[78, 1367], [342, 1405], [333, 1464], [69, 1426]], [[1080, 1452], [1362, 1433], [1367, 1498], [1085, 1516]], [[73, 1466], [563, 1511], [557, 1568], [67, 1523]], [[74, 1616], [336, 1644], [330, 1701], [68, 1674]], [[1195, 1686], [1362, 1674], [1366, 1735], [1199, 1746]], [[74, 1713], [491, 1748], [486, 1798], [70, 1762]], [[74, 1862], [335, 1883], [331, 1940], [70, 1919]], [[1223, 1931], [1362, 1916], [1369, 1979], [1230, 1994]], [[75, 1961], [417, 1985], [414, 2030], [72, 2006]], [[76, 2109], [384, 2129], [380, 2185], [72, 2164]], [[1197, 2173], [1362, 2164], [1366, 2225], [1201, 2234]], [[73, 2207], [415, 2225], [413, 2274], [70, 2256]], [[73, 2361], [334, 2370], [332, 2425], [71, 2417]], [[1088, 2422], [1361, 2407], [1364, 2464], [1092, 2479]], [[74, 2461], [535, 2475], [533, 2519], [73, 2505]], [[75, 2608], [337, 2621], [334, 2678], [72, 2665]], [[1085, 2663], [1363, 2650], [1366, 2707], [1087, 2720]], [[76, 2709], [544, 2723], [543, 2773], [75, 2759]], [[77, 2862], [387, 2875], [385, 2932], [74, 2919]], [[1087, 2904], [1363, 2893], [1366, 2958], [1089, 2968]], [[75, 2968], [546, 2973], [545, 3018], [75, 3012]], [[70, 3117], [336, 3121], [335, 3178], [69, 3174]], [[1036, 3158], [1359, 3142], [1362, 3199], [1039, 3215]], [[75, 3220], [555, 3220], [555, 3264], [75, 3264]], [[73, 3369], [281, 3369], [281, 3426], [73, 3426]], [[1200, 3387], [1372, 3387], [1372, 3463], [1200, 3463]], [[1129, 3402], [1181, 3402], [1181, 3456], [1129, 3456]], [[71, 3471], [453, 3465], [454, 3514], [72, 3520]], [[71, 3621], [331, 3617], [332, 3671], [72, 3675]], [[1125, 3651], [1178, 3651], [1178, 3704], [1125, 3704]], [[1195, 3644], [1367, 3637], [1370, 3703], [1198, 3710]], [[73, 3719], [464, 3707], [465, 3752], [74, 3763]], [[73, 3865], [382, 3858], [383, 3910], [74, 3916]], [[1229, 3893], [1368, 3885], [1372, 3946], [1232, 3953]], [[73, 3960], [453, 3954], [454, 3998], [74, 4004]], [[71, 4110], [387, 4101], [389, 4151], [72, 4159]], [[1118, 4139], [1170, 4139], [1170, 4190], [1118, 4190]], [[1189, 4130], [1371, 4137], [1369, 4204], [1186, 4197]], [[73, 4201], [491, 4197], [491, 4243], [73, 4247]], [[75, 4350], [384, 4350], [384, 4400], [75, 4400]], [[1086, 4380], [1148, 4380], [1148, 4439], [1086, 4439]], [[1158, 4374], [1370, 4370], [1371, 4438], [1160, 4443]], [[77, 4448], [463, 4448], [463, 4487], [77, 4487]], [[70, 4584], [334, 4588], [333, 4645], [69, 4641]], [[1079, 4627], [1365, 4617], [1368, 4675], [1082, 4686]], [[73, 4685], [574, 4689], [574, 4733], [73, 4729]], [[72, 4834], [368, 4844], [366, 4894], [71, 4884]], [[1245, 4867], [1375, 4859], [1380, 4929], [1250, 4937]], [[74, 4930], [433, 4938], [432, 4982], [73, 4974]], [[73, 5077], [333, 5087], [330, 5139], [71, 5128]], [[1212, 5121], [1369, 5109], [1374, 5176], [1217, 5188]], [[74, 5173], [477, 5185], [475, 5231], [73, 5219]], [[73, 5322], [368, 5332], [366, 5384], [71, 5373]], [[1176, 5376], [1234, 5376], [1234, 5436], [1176, 5436]], [[1247, 5371], [1370, 5364], [1374, 5428], [1252, 5436]], [[74, 5414], [392, 5424], [391, 5474], [73, 5464]], [[73, 5561], [385, 5573], [383, 5630], [71, 5618]], [[1056, 5622], [1367, 5622], [1367, 5679], [1056, 5679]], [[74, 5663], [598, 5674], [597, 5718], [73, 5707]], [[72, 5810], [379, 5820], [378, 5871], [71, 5861]], [[1206, 5876], [1369, 5864], [1374, 5930], [1211, 5942]], [[72, 5907], [435, 5919], [434, 5963], [71, 5952]], [[73, 6053], [383, 6065], [381, 6120], [71, 6108]], [[1056, 6119], [1369, 6119], [1369, 6182], [1056, 6182]], [[74, 6154], [606, 6166], [605, 6208], [73, 6197]], [[74, 6301], [379, 6311], [378, 6363], [73, 6353]], [[1198, 6372], [1369, 6368], [1371, 6428], [1199, 6433]], [[74, 6403], [482, 6411], [481, 6455], [73, 6447]], [[71, 6556], [471, 6556], [471, 6606], [71, 6606]], [[1301, 6611], [1376, 6611], [1376, 6677], [1301, 6677]], [[75, 6653], [437, 6653], [437, 6698], [75, 6698]], [[73, 6802], [471, 6802], [471, 6854], [73, 6854]], [[1211, 6858], [1373, 6853], [1375, 6920], [1213, 6925]], [[74, 6894], [465, 6900], [465, 6946], [73, 6940]], [[75, 7044], [384, 7044], [384, 7101], [75, 7101]], [[1253, 7094], [1376, 7089], [1379, 7162], [1257, 7168]], [[68, 7139], [266, 7139], [266, 7198], [68, 7198]]], "rec_boxes": [[62, 61, 304, 212], [510, 94, 928, 187], [43, 396, 469, 598], [53, 586, 869, 786], [53, 698, 796, 874], [64, 1011, 339, 1127], [62, 1203, 296, 1301], [1119, 1235, 1367, 1320], [69, 1367, 342, 1464], [1080, 1433, 1367, 1516], [67, 1466, 563, 1568], [68, 1616, 336, 1701], [1195, 1674, 1366, 1746], [70, 1713, 491, 1798], [70, 1862, 335, 1940], [1223, 1916, 1369, 1994], [72, 1961, 417, 2030], [72, 2109, 384, 2185], [1197, 2164, 1366, 2234], [70, 2207, 415, 2274], [71, 2361, 334, 2425], [1088, 2407, 1364, 2479], [73, 2461, 535, 2519], [72, 2608, 337, 2678], [1085, 2650, 1366, 2720], [75, 2709, 544, 2773], [74, 2862, 387, 2932], [1087, 2893, 1366, 2968], [75, 2968, 546, 3018], [69, 3117, 336, 3178], [1036, 3142, 1362, 3215], [75, 3220, 555, 3264], [73, 3369, 281, 3426], [1200, 3387, 1372, 3463], [1129, 3402, 1181, 3456], [71, 3465, 454, 3520], [71, 3617, 332, 3675], [1125, 3651, 1178, 3704], [1195, 3637, 1370, 3710], [73, 3707, 465, 3763], [73, 3858, 383, 3916], [1229, 3885, 1372, 3953], [73, 3954, 454, 4004], [71, 4101, 389, 4159], [1118, 4139, 1170, 4190], [1186, 4130, 1371, 4204], [73, 4197, 491, 4247], [75, 4350, 384, 4400], [1086, 4380, 1148, 4439], [1158, 4370, 1371, 4443], [77, 4448, 463, 4487], [69, 4584, 334, 4645], [1079, 4617, 1368, 4686], [73, 4685, 574, 4733], [71, 4834, 368, 4894], [1245, 4859, 1380, 4937], [73, 4930, 433, 4982], [71, 5077, 333, 5139], [1212, 5109, 1374, 5188], [73, 5173, 477, 5231], [71, 5322, 368, 5384], [1176, 5376, 1234, 5436], [1247, 5364, 1374, 5436], [73, 5414, 392, 5474], [71, 5561, 385, 5630], [1056, 5622, 1367, 5679], [73, 5663, 598, 5718], [71, 5810, 379, 5871], [1206, 5864, 1374, 5942], [71, 5907, 435, 5963], [71, 6053, 383, 6120], [1056, 6119, 1369, 6182], [73, 6154, 606, 6208], [73, 6301, 379, 6363], [1198, 6368, 1371, 6433], [73, 6403, 482, 6455], [71, 6556, 471, 6606], [1301, 6611, 1376, 6677], [75, 6653, 437, 6698], [73, 6802, 471, 6854], [1211, 6853, 1375, 6925], [73, 6894, 465, 6946], [75, 7044, 384, 7101], [1253, 7089, 1379, 7168], [68, 7139, 266, 7198]]}