import re
import pymysql
from paddleocr import PaddleOCR
from ..utils.logger import logger
from datetime import datetime

class ExamReportProcessor:
    """检查报告处理器，用于处理影像检查报告的OCR识别和数据解析"""

    # 报告日期匹配模式，匹配"报告日期：2025/4/2216:24:00"格式
    DATE_PATTERN = re.compile(r'报告日期[：:](\d{4})/(\d{1,2})/(\d{1,2})\d{2}:\d{2}:\d{2}')

    # 检查方法匹配模式 - 从"检查方法："开始到"放射性诊断"结束
    EXAM_METHOD_PATTERN = re.compile(r'检查方法[：:](.+?)(?=放射性诊断)', re.DOTALL | re.IGNORECASE)

    # 放射性诊断匹配模式 - 从"放射性诊断"下一行开始到"查看影像"结束
    EXAM_DIAG_PATTERN = re.compile(r'放射性诊断[：:]\s*\n(.+?)(?=查看影像)', re.DOTALL | re.IGNORECASE)

    def __init__(self, db_manager, ocr_config):
        self.db_manager = db_manager
        self.ocr_config = ocr_config

    def process_exam_report(self, image_path):
        """处理检查报告"""
        try:
            logger.info(f"开始处理检查报告： {image_path}")

            # 初始化OCR
            ocr = PaddleOCR(**self.ocr_config)
            result = ocr.predict(image_path)

            for i, res in enumerate(result):
                self._process_single_exam_result(res)

            logger.info(f"完成处理检查报告 {image_path}")
        except Exception as e:
            logger.error(f"Error in process_exam_report: {str(e)}", exc_info=True)
            raise

    def _process_single_exam_result(self, res):
        """处理单个OCR结果"""
        try:
            # 保存OCR结果到JSON文件
            res.save_to_json("output")

            # 解析OCR结果
            data = self._parse_exam_ocr_result(res)

            if data:
                self._save_exam_to_database(data)
                logger.debug("检查报告数据保存到数据库")
            else:
                logger.warning("No valid exam data extracted from OCR result")
        except Exception as e:
            logger.error(f"Error in _process_single_exam_result: {str(e)}", exc_info=True)

    def _parse_exam_ocr_result(self, res):
        """解析检查报告OCR结果"""
        try:
            # 获取OCR识别的文本
            if hasattr(res, 'rec_texts'):
                rec_texts = res.rec_texts
            elif isinstance(res, dict) and 'rec_texts' in res:
                rec_texts = res['rec_texts']
            else:
                rec_texts = getattr(res, 'rec_texts', None)
                if rec_texts is None:
                    logger.error(f"Cannot find rec_texts in OCR result: {type(res)}")
                    return None

            # 将所有文本合并为一个字符串，便于正则匹配
            full_text = '\n'.join(rec_texts)
            logger.debug(f"OCR识别的完整文本: {full_text}")

            # 提取检查日期
            medical_date = self._extract_medical_date(rec_texts)

            # 提取检查方法信息
            exam_info = self._extract_exam_info(full_text)

            # 提取放射性诊断信息
            exam_diag = self._extract_exam_diag(full_text)

            return {
                'medical_date': medical_date,
                'exam_info': exam_info,
                'exam_diag': exam_diag,
                'exam_type': 1,  # 默认为1
                'hospital': '复旦肿瘤'  # 默认医院
            }
        except Exception as e:
            logger.error(f"OCR result parsing error: {str(e)}")
            return None

    def _extract_medical_date(self, rec_texts):
        """提取检查日期，从"报告日期：2025/4/2216:24:00"格式中提取年月日"""
        for text in rec_texts:
            match = self.DATE_PATTERN.search(text)
            if match:
                year = match.group(1)
                month = match.group(2).zfill(2)  # 补零到两位数
                day = match.group(3).zfill(2)    # 补零到两位数
                date_str = f"{year}-{month}-{day}"
                logger.debug(f"提取到检查日期: {date_str} (原文: {text})")
                return date_str

        logger.warning("未找到检查日期")
        return None

    def _extract_exam_info(self, full_text):
        """提取检查方法信息，从"检查方法："开始，包括后面几行，直到出现"放射性诊断"""
        # 使用正则表达式匹配从"检查方法："到"放射性诊断"之间的所有内容
        match = re.search(r'检查方法[：:](.+?)(?=放射性诊断)', full_text, re.DOTALL | re.IGNORECASE)
        if match:
            exam_info = match.group(1).strip()
            # 清理多余的空白字符，但保留换行
            exam_info = re.sub(r'\n\s*\n', '\n', exam_info)  # 去除多余空行
            exam_info = re.sub(r'[ \t]+', ' ', exam_info)     # 合并多余空格
            print(f"提取到检查方法: {exam_info}")
            return exam_info

        logger.warning("未找到检查方法信息")
        return None

    def _extract_exam_diag(self, full_text):
        """提取放射性诊断信息，从"放射性诊断"下一行开始，直到出现"查看影像"为止"""
        # 使用正则表达式匹配从"放射性诊断"下一行到"查看影像"之间的所有内容
        match = re.search(r'放射性诊断[：:]\s*\n(.+?)(?=查看影像)', full_text, re.DOTALL | re.IGNORECASE)
        if match:
            exam_diag = match.group(1).strip()
            # 清理多余的空白字符，但保留换行
            exam_diag = re.sub(r'\n\s*\n', '\n', exam_diag)  # 去除多余空行
            exam_diag = re.sub(r'[ \t]+', ' ', exam_diag)     # 合并多余空格
            logger.debug(f"提取到放射性诊断: {exam_diag}")
            return exam_diag

        # 如果没有找到"查看影像"，尝试匹配到文本结尾
        match = re.search(r'放射性诊断[：:]\s*\n(.+?)$', full_text, re.DOTALL | re.IGNORECASE)
        if match:
            exam_diag = match.group(1).strip()
            exam_diag = re.sub(r'\n\s*\n', '\n', exam_diag)
            exam_diag = re.sub(r'[ \t]+', ' ', exam_diag)
            logger.debug(f"提取到放射性诊断（到结尾）: {exam_diag}")
            return exam_diag

        logger.warning("未找到放射性诊断信息")
        return None

    def _save_exam_to_database(self, data):
        """保存检查报告数据到数据库"""
        conn = None
        try:
            conn = self.db_manager.get_connection()
            with conn.cursor() as cursor:
                # 插入medical_exam表
                cursor.execute(
                    """INSERT INTO medical_exam
                    (user_id, medical_date, exam_info, exam_diag, exam_type, hospital)
                    VALUES (%s, %s, %s, %s, %s, %s)""",
                    (
                        1,  # 默认用户ID（整数）
                        data['medical_date'],
                        data['exam_info'],
                        data['exam_diag'],
                        data['exam_type'],
                        data['hospital']
                    )
                )

                exam_id = cursor.lastrowid
                conn.commit()
                logger.info(f"成功保存检查报告数据，ID: {exam_id}")

        except pymysql.Error as e:
            logger.error(f"数据库错误: {str(e)}")
            if conn:
                conn.rollback()
        finally:
            if conn:
                conn.close()
