{"input_path": "D:\\dev\\medicalReport\\ocr\\source\\20250415161347.jpg", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[74, 48], [423, 48], [423, 105], [74, 105]], [[912, 54], [959, 54], [959, 101], [912, 101]], [[970, 48], [1125, 48], [1125, 85], [970, 85]], [[432, 58], [472, 58], [472, 97], [432, 97]], [[1270, 54], [1346, 54], [1346, 103], [1270, 103]], [[972, 71], [1053, 71], [1053, 107], [972, 107]], [[591, 179], [845, 179], [845, 252], [591, 252]], [[33, 208], [110, 208], [110, 282], [33, 282]], [[514, 260], [919, 264], [918, 313], [513, 308]], [[33, 353], [391, 353], [391, 401], [33, 401]], [[80, 652], [263, 660], [259, 737], [77, 729]], [[235, 656], [453, 664], [450, 730], [233, 723]], [[94, 842], [542, 842], [542, 897], [94, 897]], [[92, 947], [775, 947], [775, 994], [92, 994]], [[86, 1226], [335, 1221], [336, 1288], [87, 1292]], [[83, 1407], [296, 1402], [297, 1461], [85, 1466]], [[1121, 1405], [1350, 1405], [1350, 1461], [1121, 1461]], [[88, 1556], [297, 1556], [297, 1612], [88, 1612]], [[1107, 1594], [1356, 1594], [1356, 1657], [1107, 1657]], [[92, 1651], [515, 1651], [515, 1693], [92, 1693]], [[90, 1792], [342, 1792], [342, 1842], [90, 1842]], [[1139, 1830], [1354, 1830], [1354, 1887], [1139, 1887]], [[92, 1887], [448, 1887], [448, 1927], [92, 1927]], [[90, 2028], [344, 2028], [344, 2076], [90, 2076]], [[1166, 2062], [1352, 2062], [1352, 2120], [1166, 2120]], [[92, 2118], [477, 2118], [477, 2159], [92, 2159]], [[92, 2259], [342, 2259], [342, 2308], [92, 2308]], [[1186, 2298], [1354, 2298], [1354, 2354], [1186, 2354]], [[92, 2352], [423, 2352], [423, 2394], [92, 2394]], [[92, 2493], [344, 2493], [344, 2544], [92, 2544]], [[1168, 2529], [1352, 2529], [1352, 2588], [1168, 2588]], [[92, 2586], [452, 2586], [452, 2626], [92, 2626]], [[90, 2727], [344, 2727], [344, 2775], [90, 2775]], [[1172, 2761], [1352, 2761], [1352, 2820], [1172, 2820]], [[92, 2820], [503, 2820], [503, 2860], [92, 2860]], [[92, 2959], [391, 2959], [391, 3007], [92, 3007]], [[1209, 2995], [1354, 2995], [1354, 3055], [1209, 3055]], [[92, 3053], [425, 3053], [425, 3094], [92, 3094]], [[88, 3189], [245, 3189], [245, 3247], [88, 3247]], [[1180, 3225], [1358, 3225], [1358, 3297], [1180, 3297]], [[92, 3285], [436, 3285], [436, 3328], [92, 3328]], [[87, 3418], [246, 3423], [244, 3483], [85, 3478]], [[1180, 3463], [1358, 3463], [1358, 3529], [1180, 3529]], [[90, 3515], [475, 3515], [475, 3563], [90, 3563]], [[82, 3652], [202, 3652], [202, 3721], [82, 3721]], [[1082, 3698], [1352, 3698], [1352, 3753], [1082, 3753]], [[92, 3751], [522, 3751], [522, 3793], [92, 3793]], [[84, 3888], [200, 3888], [200, 3950], [84, 3950]], [[1141, 3930], [1354, 3930], [1354, 3987], [1141, 3987]], [[92, 3987], [487, 3987], [487, 4027], [92, 4027]], [[84, 4120], [200, 4120], [200, 4182], [84, 4182]], [[1127, 4168], [1352, 4168], [1352, 4218], [1127, 4218]], [[92, 4220], [542, 4220], [542, 4261], [92, 4261]], [[84, 4353], [151, 4353], [151, 4418], [84, 4418]], [[1098, 4400], [1354, 4400], [1354, 4452], [1098, 4452]], [[92, 4452], [565, 4452], [565, 4493], [92, 4493]], [[84, 4583], [151, 4583], [151, 4650], [84, 4650]], [[1080, 4634], [1352, 4634], [1352, 4688], [1080, 4688]], [[92, 4684], [583, 4684], [583, 4726], [92, 4726]], [[84, 4821], [149, 4821], [149, 4886], [84, 4886]], [[1066, 4869], [1350, 4869], [1350, 4918], [1066, 4918]], [[92, 4918], [548, 4918], [548, 4960], [92, 4960]], [[84, 5055], [149, 5055], [149, 5117], [84, 5117]], [[1047, 5101], [1348, 5101], [1348, 5150], [1047, 5150]], [[92, 5154], [556, 5154], [556, 5194], [92, 5194]], [[86, 5287], [151, 5287], [151, 5349], [86, 5349]], [[1043, 5335], [1349, 5331], [1350, 5385], [1044, 5390]], [[92, 5387], [548, 5387], [548, 5428], [92, 5428]], [[90, 5527], [346, 5527], [346, 5579], [90, 5579]], [[1076, 5567], [1350, 5567], [1350, 5621], [1076, 5621]], [[92, 5619], [562, 5619], [562, 5660], [92, 5660]], [[82, 5752], [151, 5752], [151, 5819], [82, 5819]], [[1064, 5799], [1350, 5799], [1350, 5855], [1064, 5855]], [[92, 5853], [589, 5853], [589, 5893], [92, 5893]], [[86, 5990], [248, 5990], [248, 6049], [86, 6049]], [[1207, 6026], [1356, 6026], [1356, 6093], [1207, 6093]], [[92, 6087], [466, 6087], [466, 6127], [92, 6127]], [[88, 6224], [295, 6224], [295, 6280], [88, 6280]], [[1127, 6264], [1352, 6264], [1352, 6327], [1127, 6327]], [[1064, 6274], [1098, 6274], [1098, 6315], [1064, 6315]], [[88, 6314], [540, 6319], [540, 6367], [88, 6363]], [[86, 6456], [245, 6456], [245, 6514], [86, 6514]], [[1182, 6496], [1358, 6496], [1358, 6562], [1182, 6562]], [[92, 6552], [507, 6552], [507, 6595], [92, 6595]], [[92, 6693], [290, 6693], [290, 6744], [92, 6744]], [[1246, 6730], [1356, 6730], [1356, 6790], [1246, 6790]], [[92, 6786], [393, 6786], [393, 6829], [92, 6829]], [[90, 6930], [343, 6925], [344, 6975], [91, 6980]], [[1090, 6968], [1349, 6961], [1350, 7018], [1091, 7024]], [[92, 7020], [481, 7020], [481, 7062], [92, 7062]], [[90, 7159], [346, 7159], [346, 7211], [90, 7211]], [[1180, 7196], [1353, 7191], [1355, 7255], [1182, 7260]], [[92, 7254], [489, 7254], [489, 7294], [92, 7294]], [[90, 7395], [342, 7395], [342, 7445], [90, 7445]], [[1069, 7433], [1347, 7429], [1348, 7485], [1070, 7490]], [[1004, 7443], [1047, 7443], [1047, 7486], [1004, 7486]], [[92, 7486], [567, 7486], [567, 7528], [92, 7528]], [[88, 7625], [395, 7625], [395, 7679], [88, 7679]], [[1199, 7661], [1354, 7661], [1354, 7727], [1199, 7727]], [[90, 7717], [456, 7717], [456, 7766], [90, 7766]]], "text_det_params": {"limit_side_len": 64, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.6, "rec_texts": ["15:26", "2.00÷5G", "中", "88", "KB/S", "报告查询", "×", "fwcs.linkingcloud.cn", "罗氏Cobas8000-1", "*春香", "***岁", "申请科室：胰腺外科", "报告日期：2025-4-150:00:00", "项目明细", "项目名称", "结果/单位", "总胆红素", "16.3umol/l", "参考值：女≤21umol/l", "直接胆红素", "6.1umol/l", "参考值：≤8umol/l", "碱性磷酸酶", "81.6U/L", "参考值：50-135U/L", "谷丙转氨酶", "14.7U/I", "参考值：7-40U/", "谷草转氨酶", "16.6U/L", "参考值：13-35U/L", "乳酸脱氢酶", "207U/L", "参考值：120-250U/L", "谷氨酰转肽酶", "15U/L", "参考值：7-45U/L", "总蛋白", "70.7g/l", "参考值：65-85g/l", "白蛋白", "43.3g/l", "参考值：40-55.0g/l", "尿素", "3.36mmol/l", "参考值：3.1-8.8mmol/", "肌酐", "65umol/l", "参考值：41-81umol/l", "尿酸", "176umol/I", "参考值：155-357umol/l", "钙", "2.31mmol/l", "参考值：2.11-2.52mmol/l", "磷", "1.22mmol/L", "参考值：0.85-1.51mmol/L", "钾", "4.45mmol/L", "参考值：3.5-5.3mmol/L", "钠", "140.0mmol/L", "参考值：137-147mmol/L", "氯", "102.0mmol/L", "参考值：96-108mmol/L", "总二氧化碳", "27.2mmol/L", "参考值：23-29.0mmol/L", "镁", "0.90mmol/L", "参考值：0.75-1.02mmol/L", "淀粉酶", "41U/L", "参考值：35-135U/L", "前白蛋白", "209mg/L", "↓", "参考值：250-400mg/L", "球蛋白", "27.4g/l", "参考值：20.0-40.0g/l", "白球比例", "1.58", "参考值：1.2-2.4", "间接胆红素", "10.2umol/L", "参考值：0-12umol/L", "谷草同工酶", "5.4U/L", "参考值：0.0-15.0U/L", "游离脂肪酸", "0.91mmol/L", "↑", "参考值：0.1-0.45mmol/L", "谷氨酸脱氢酶", "<2U/L", "参考值：女0-5U/L"], "rec_scores": [0.9857252240180969, 0.9341891407966614, 0.9638956189155579, 0.9996203184127808, 0.8902320265769958, 0.999855637550354, 0.8395116925239563, 0.9980409741401672, 0.9985097646713257, 0.99559086561203, 0.8870144486427307, 0.9879066944122314, 0.9777029156684875, 0.9997234344482422, 0.9999011754989624, 0.9976078271865845, 0.999290406703949, 0.984973132610321, 0.9691294431686401, 0.999308705329895, 0.9557940363883972, 0.957204282283783, 0.9990788698196411, 0.9966115951538086, 0.9801318645477295, 0.9976957440376282, 0.9861313700675964, 0.9778518676757812, 0.9985310435295105, 0.9985139966011047, 0.9865218997001648, 0.9956920742988586, 0.9986665844917297, 0.9850420355796814, 0.9971739649772644, 0.9989906549453735, 0.9817661046981812, 0.9995097517967224, 0.9761384129524231, 0.988032341003418, 0.9997428059577942, 0.9735553860664368, 0.9550842046737671, 0.9998656511306763, 0.9637002944946289, 0.9803791046142578, 0.9591206908226013, 0.9677397012710571, 0.9813057780265808, 0.9997768402099609, 0.9453296661376953, 0.9842280149459839, 0.9995208978652954, 0.9698501825332642, 0.9692918062210083, 0.9997838139533997, 0.9978018999099731, 0.9800707101821899, 0.9999167919158936, 0.9954420328140259, 0.9859577417373657, 0.9769439101219177, 0.9979059100151062, 0.9813023805618286, 0.9998134970664978, 0.9974685907363892, 0.9879893064498901, 0.9977236986160278, 0.9972013235092163, 0.9893051385879517, 0.9993232488632202, 0.9973807334899902, 0.9821316599845886, 0.9994745850563049, 0.9965337514877319, 0.9766238331794739, 0.9996293187141418, 0.9976152181625366, 0.9887646436691284, 0.9893861413002014, 0.9992868900299072, 0.9754951596260071, 0.9760412573814392, 0.9981068968772888, 0.9998451471328735, 0.9919167160987854, 0.9988547563552856, 0.9963699579238892, 0.984285295009613, 0.9974514245986938, 0.995089590549469, 0.992397665977478, 0.9957583546638489, 0.9947410821914673, 0.9268119931221008, 0.9923800230026245, 0.9900631904602051, 0.98777836561203, 0.9931308627128601], "rec_polys": [[[74, 48], [423, 48], [423, 105], [74, 105]], [[970, 48], [1125, 48], [1125, 85], [970, 85]], [[432, 58], [472, 58], [472, 97], [432, 97]], [[1270, 54], [1346, 54], [1346, 103], [1270, 103]], [[972, 71], [1053, 71], [1053, 107], [972, 107]], [[591, 179], [845, 179], [845, 252], [591, 252]], [[33, 208], [110, 208], [110, 282], [33, 282]], [[514, 260], [919, 264], [918, 313], [513, 308]], [[33, 353], [391, 353], [391, 401], [33, 401]], [[80, 652], [263, 660], [259, 737], [77, 729]], [[235, 656], [453, 664], [450, 730], [233, 723]], [[94, 842], [542, 842], [542, 897], [94, 897]], [[92, 947], [775, 947], [775, 994], [92, 994]], [[86, 1226], [335, 1221], [336, 1288], [87, 1292]], [[83, 1407], [296, 1402], [297, 1461], [85, 1466]], [[1121, 1405], [1350, 1405], [1350, 1461], [1121, 1461]], [[88, 1556], [297, 1556], [297, 1612], [88, 1612]], [[1107, 1594], [1356, 1594], [1356, 1657], [1107, 1657]], [[92, 1651], [515, 1651], [515, 1693], [92, 1693]], [[90, 1792], [342, 1792], [342, 1842], [90, 1842]], [[1139, 1830], [1354, 1830], [1354, 1887], [1139, 1887]], [[92, 1887], [448, 1887], [448, 1927], [92, 1927]], [[90, 2028], [344, 2028], [344, 2076], [90, 2076]], [[1166, 2062], [1352, 2062], [1352, 2120], [1166, 2120]], [[92, 2118], [477, 2118], [477, 2159], [92, 2159]], [[92, 2259], [342, 2259], [342, 2308], [92, 2308]], [[1186, 2298], [1354, 2298], [1354, 2354], [1186, 2354]], [[92, 2352], [423, 2352], [423, 2394], [92, 2394]], [[92, 2493], [344, 2493], [344, 2544], [92, 2544]], [[1168, 2529], [1352, 2529], [1352, 2588], [1168, 2588]], [[92, 2586], [452, 2586], [452, 2626], [92, 2626]], [[90, 2727], [344, 2727], [344, 2775], [90, 2775]], [[1172, 2761], [1352, 2761], [1352, 2820], [1172, 2820]], [[92, 2820], [503, 2820], [503, 2860], [92, 2860]], [[92, 2959], [391, 2959], [391, 3007], [92, 3007]], [[1209, 2995], [1354, 2995], [1354, 3055], [1209, 3055]], [[92, 3053], [425, 3053], [425, 3094], [92, 3094]], [[88, 3189], [245, 3189], [245, 3247], [88, 3247]], [[1180, 3225], [1358, 3225], [1358, 3297], [1180, 3297]], [[92, 3285], [436, 3285], [436, 3328], [92, 3328]], [[87, 3418], [246, 3423], [244, 3483], [85, 3478]], [[1180, 3463], [1358, 3463], [1358, 3529], [1180, 3529]], [[90, 3515], [475, 3515], [475, 3563], [90, 3563]], [[82, 3652], [202, 3652], [202, 3721], [82, 3721]], [[1082, 3698], [1352, 3698], [1352, 3753], [1082, 3753]], [[92, 3751], [522, 3751], [522, 3793], [92, 3793]], [[84, 3888], [200, 3888], [200, 3950], [84, 3950]], [[1141, 3930], [1354, 3930], [1354, 3987], [1141, 3987]], [[92, 3987], [487, 3987], [487, 4027], [92, 4027]], [[84, 4120], [200, 4120], [200, 4182], [84, 4182]], [[1127, 4168], [1352, 4168], [1352, 4218], [1127, 4218]], [[92, 4220], [542, 4220], [542, 4261], [92, 4261]], [[84, 4353], [151, 4353], [151, 4418], [84, 4418]], [[1098, 4400], [1354, 4400], [1354, 4452], [1098, 4452]], [[92, 4452], [565, 4452], [565, 4493], [92, 4493]], [[84, 4583], [151, 4583], [151, 4650], [84, 4650]], [[1080, 4634], [1352, 4634], [1352, 4688], [1080, 4688]], [[92, 4684], [583, 4684], [583, 4726], [92, 4726]], [[84, 4821], [149, 4821], [149, 4886], [84, 4886]], [[1066, 4869], [1350, 4869], [1350, 4918], [1066, 4918]], [[92, 4918], [548, 4918], [548, 4960], [92, 4960]], [[84, 5055], [149, 5055], [149, 5117], [84, 5117]], [[1047, 5101], [1348, 5101], [1348, 5150], [1047, 5150]], [[92, 5154], [556, 5154], [556, 5194], [92, 5194]], [[86, 5287], [151, 5287], [151, 5349], [86, 5349]], [[1043, 5335], [1349, 5331], [1350, 5385], [1044, 5390]], [[92, 5387], [548, 5387], [548, 5428], [92, 5428]], [[90, 5527], [346, 5527], [346, 5579], [90, 5579]], [[1076, 5567], [1350, 5567], [1350, 5621], [1076, 5621]], [[92, 5619], [562, 5619], [562, 5660], [92, 5660]], [[82, 5752], [151, 5752], [151, 5819], [82, 5819]], [[1064, 5799], [1350, 5799], [1350, 5855], [1064, 5855]], [[92, 5853], [589, 5853], [589, 5893], [92, 5893]], [[86, 5990], [248, 5990], [248, 6049], [86, 6049]], [[1207, 6026], [1356, 6026], [1356, 6093], [1207, 6093]], [[92, 6087], [466, 6087], [466, 6127], [92, 6127]], [[88, 6224], [295, 6224], [295, 6280], [88, 6280]], [[1127, 6264], [1352, 6264], [1352, 6327], [1127, 6327]], [[1064, 6274], [1098, 6274], [1098, 6315], [1064, 6315]], [[88, 6314], [540, 6319], [540, 6367], [88, 6363]], [[86, 6456], [245, 6456], [245, 6514], [86, 6514]], [[1182, 6496], [1358, 6496], [1358, 6562], [1182, 6562]], [[92, 6552], [507, 6552], [507, 6595], [92, 6595]], [[92, 6693], [290, 6693], [290, 6744], [92, 6744]], [[1246, 6730], [1356, 6730], [1356, 6790], [1246, 6790]], [[92, 6786], [393, 6786], [393, 6829], [92, 6829]], [[90, 6930], [343, 6925], [344, 6975], [91, 6980]], [[1090, 6968], [1349, 6961], [1350, 7018], [1091, 7024]], [[92, 7020], [481, 7020], [481, 7062], [92, 7062]], [[90, 7159], [346, 7159], [346, 7211], [90, 7211]], [[1180, 7196], [1353, 7191], [1355, 7255], [1182, 7260]], [[92, 7254], [489, 7254], [489, 7294], [92, 7294]], [[90, 7395], [342, 7395], [342, 7445], [90, 7445]], [[1069, 7433], [1347, 7429], [1348, 7485], [1070, 7490]], [[1004, 7443], [1047, 7443], [1047, 7486], [1004, 7486]], [[92, 7486], [567, 7486], [567, 7528], [92, 7528]], [[88, 7625], [395, 7625], [395, 7679], [88, 7679]], [[1199, 7661], [1354, 7661], [1354, 7727], [1199, 7727]], [[90, 7717], [456, 7717], [456, 7766], [90, 7766]]], "rec_boxes": [[74, 48, 423, 105], [970, 48, 1125, 85], [432, 58, 472, 97], [1270, 54, 1346, 103], [972, 71, 1053, 107], [591, 179, 845, 252], [33, 208, 110, 282], [513, 260, 919, 313], [33, 353, 391, 401], [77, 652, 263, 737], [233, 656, 453, 730], [94, 842, 542, 897], [92, 947, 775, 994], [86, 1221, 336, 1292], [83, 1402, 297, 1466], [1121, 1405, 1350, 1461], [88, 1556, 297, 1612], [1107, 1594, 1356, 1657], [92, 1651, 515, 1693], [90, 1792, 342, 1842], [1139, 1830, 1354, 1887], [92, 1887, 448, 1927], [90, 2028, 344, 2076], [1166, 2062, 1352, 2120], [92, 2118, 477, 2159], [92, 2259, 342, 2308], [1186, 2298, 1354, 2354], [92, 2352, 423, 2394], [92, 2493, 344, 2544], [1168, 2529, 1352, 2588], [92, 2586, 452, 2626], [90, 2727, 344, 2775], [1172, 2761, 1352, 2820], [92, 2820, 503, 2860], [92, 2959, 391, 3007], [1209, 2995, 1354, 3055], [92, 3053, 425, 3094], [88, 3189, 245, 3247], [1180, 3225, 1358, 3297], [92, 3285, 436, 3328], [85, 3418, 246, 3483], [1180, 3463, 1358, 3529], [90, 3515, 475, 3563], [82, 3652, 202, 3721], [1082, 3698, 1352, 3753], [92, 3751, 522, 3793], [84, 3888, 200, 3950], [1141, 3930, 1354, 3987], [92, 3987, 487, 4027], [84, 4120, 200, 4182], [1127, 4168, 1352, 4218], [92, 4220, 542, 4261], [84, 4353, 151, 4418], [1098, 4400, 1354, 4452], [92, 4452, 565, 4493], [84, 4583, 151, 4650], [1080, 4634, 1352, 4688], [92, 4684, 583, 4726], [84, 4821, 149, 4886], [1066, 4869, 1350, 4918], [92, 4918, 548, 4960], [84, 5055, 149, 5117], [1047, 5101, 1348, 5150], [92, 5154, 556, 5194], [86, 5287, 151, 5349], [1043, 5331, 1350, 5390], [92, 5387, 548, 5428], [90, 5527, 346, 5579], [1076, 5567, 1350, 5621], [92, 5619, 562, 5660], [82, 5752, 151, 5819], [1064, 5799, 1350, 5855], [92, 5853, 589, 5893], [86, 5990, 248, 6049], [1207, 6026, 1356, 6093], [92, 6087, 466, 6127], [88, 6224, 295, 6280], [1127, 6264, 1352, 6327], [1064, 6274, 1098, 6315], [88, 6314, 540, 6367], [86, 6456, 245, 6514], [1182, 6496, 1358, 6562], [92, 6552, 507, 6595], [92, 6693, 290, 6744], [1246, 6730, 1356, 6790], [92, 6786, 393, 6829], [90, 6925, 344, 6980], [1090, 6961, 1350, 7024], [92, 7020, 481, 7062], [90, 7159, 346, 7211], [1180, 7191, 1355, 7260], [92, 7254, 489, 7294], [90, 7395, 342, 7445], [1069, 7429, 1348, 7490], [1004, 7443, 1047, 7486], [92, 7486, 567, 7528], [88, 7625, 395, 7679], [1199, 7661, 1354, 7727], [90, 7717, 456, 7766]]}