{"input_path": "D:\\dev\\medicalReport\\ocr\\source\\20250521143154.jpg", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[65, 50], [205, 50], [205, 103], [65, 103]], [[356, 52], [550, 52], [550, 101], [356, 101]], [[839, 52], [882, 52], [882, 103], [839, 103]], [[890, 44], [984, 44], [984, 89], [890, 89]], [[1145, 48], [1242, 48], [1242, 103], [1145, 103]], [[286, 58], [356, 58], [356, 99], [286, 99]], [[1260, 54], [1323, 54], [1323, 103], [1260, 103]], [[898, 73], [974, 73], [974, 109], [898, 109]], [[589, 179], [845, 179], [845, 252], [589, 252]], [[33, 208], [110, 208], [110, 282], [33, 282]], [[515, 266], [916, 266], [916, 308], [515, 308]], [[29, 351], [297, 351], [297, 407], [29, 407]], [[79, 655], [457, 664], [455, 743], [77, 733]], [[96, 849], [841, 849], [841, 897], [96, 897]], [[94, 950], [773, 950], [773, 998], [94, 998]], [[83, 1228], [339, 1223], [340, 1294], [85, 1299]], [[81, 1408], [298, 1403], [299, 1467], [83, 1472]], [[1121, 1409], [1350, 1409], [1350, 1466], [1121, 1466]], [[82, 1553], [151, 1553], [151, 1619], [82, 1619]], [[1084, 1601], [1354, 1601], [1354, 1657], [1084, 1657]], [[88, 1649], [569, 1651], [568, 1700], [88, 1698]], [[82, 1788], [202, 1788], [202, 1857], [82, 1857]], [[1139, 1835], [1353, 1830], [1355, 1889], [1140, 1894]], [[92, 1889], [487, 1889], [487, 1930], [92, 1930]], [[84, 2024], [149, 2024], [149, 2087], [84, 2087]], [[1066, 2065], [1350, 2065], [1350, 2119], [1066, 2119]], [[92, 2121], [546, 2121], [546, 2161], [92, 2161]], [[86, 2256], [151, 2256], [151, 2319], [86, 2319]], [[1051, 2301], [1352, 2301], [1352, 2355], [1051, 2355]], [[92, 2355], [548, 2355], [548, 2395], [92, 2395]], [[82, 2488], [151, 2488], [151, 2555], [82, 2555]], [[1062, 2532], [1348, 2532], [1348, 2587], [1062, 2587]], [[92, 2587], [589, 2587], [589, 2629], [92, 2629]], [[84, 2722], [149, 2722], [149, 2786], [84, 2786]], [[1060, 2770], [1350, 2770], [1350, 2819], [1060, 2819]], [[92, 2821], [558, 2821], [558, 2863], [92, 2863]], [[84, 2958], [200, 2958], [200, 3018], [84, 3018]], [[1084, 3004], [1352, 3004], [1352, 3053], [1084, 3053]], [[92, 3057], [524, 3057], [524, 3097], [92, 3097]], [[86, 3192], [248, 3192], [248, 3250], [86, 3250]], [[1094, 3238], [1350, 3238], [1350, 3286], [1094, 3286]], [[1021, 3250], [1058, 3242], [1066, 3279], [1028, 3286]], [[92, 3289], [524, 3289], [524, 3329], [92, 3329]], [[90, 3430], [344, 3430], [344, 3478], [90, 3478]], [[1176, 3466], [1352, 3466], [1352, 3524], [1176, 3524]], [[92, 3522], [503, 3522], [503, 3563], [92, 3563]], [[84, 3655], [151, 3655], [151, 3722], [84, 3722]], [[1074, 3702], [1350, 3702], [1350, 3756], [1074, 3756]], [[92, 3756], [579, 3756], [579, 3797], [92, 3797]], [[92, 3897], [393, 3897], [393, 3946], [92, 3946]], [[1207, 3928], [1356, 3928], [1356, 3994], [1207, 3994]], [[92, 3990], [425, 3990], [425, 4030], [92, 4030]], [[92, 4127], [245, 4127], [245, 4182], [92, 4182]], [[1192, 4166], [1356, 4166], [1356, 4232], [1192, 4232]], [[92, 4222], [472, 4222], [472, 4264], [92, 4264]], [[90, 4363], [346, 4363], [346, 4416], [90, 4416]], [[1182, 4399], [1354, 4399], [1354, 4458], [1182, 4458]], [[90, 4452], [425, 4452], [425, 4494], [90, 4494]], [[92, 4597], [393, 4597], [393, 4645], [92, 4645]], [[1199, 4629], [1356, 4629], [1356, 4696], [1199, 4696]], [[90, 4690], [454, 4690], [454, 4730], [90, 4730]], [[88, 4831], [344, 4831], [344, 4879], [88, 4879]], [[1158, 4867], [1352, 4867], [1352, 4924], [1158, 4924]], [[92, 4922], [479, 4922], [479, 4964], [92, 4964]], [[84, 5057], [200, 5057], [200, 5119], [84, 5119]], [[1120, 5104], [1353, 5097], [1355, 5155], [1122, 5162]], [[92, 5154], [540, 5154], [540, 5196], [92, 5196]], [[88, 5293], [295, 5293], [295, 5349], [88, 5349]], [[1062, 5339], [1111, 5339], [1111, 5389], [1062, 5389]], [[1131, 5333], [1354, 5333], [1354, 5395], [1131, 5395]], [[92, 5389], [538, 5389], [538, 5432], [92, 5432]], [[87, 5524], [346, 5529], [345, 5585], [86, 5581]], [[1074, 5569], [1352, 5569], [1352, 5623], [1074, 5623]], [[92, 5623], [562, 5623], [562, 5664], [92, 5664]], [[89, 5760], [344, 5765], [343, 5815], [88, 5811]], [[1156, 5797], [1354, 5797], [1354, 5861], [1156, 5861]], [[92, 5857], [450, 5857], [450, 5898], [92, 5898]], [[90, 5994], [342, 5994], [342, 6045], [90, 6045]], [[1184, 6031], [1354, 6031], [1354, 6095], [1184, 6095]], [[92, 6091], [491, 6091], [491, 6131], [92, 6131]], [[90, 6230], [344, 6230], [344, 6279], [90, 6279]], [[1133, 6267], [1354, 6267], [1354, 6323], [1133, 6323]], [[92, 6323], [448, 6323], [448, 6365], [92, 6365]], [[88, 6460], [297, 6460], [297, 6517], [88, 6517]], [[1125, 6498], [1356, 6498], [1356, 6561], [1125, 6561]], [[92, 6555], [513, 6555], [513, 6597], [92, 6597]], [[85, 6689], [246, 6694], [244, 6755], [83, 6750]], [[1178, 6732], [1356, 6732], [1356, 6799], [1178, 6799]], [[92, 6789], [436, 6789], [436, 6831], [92, 6831]], [[92, 6932], [344, 6932], [344, 6980], [92, 6980]], [[1058, 6968], [1350, 6968], [1350, 7023], [1058, 7023]], [[996, 6980], [1037, 6980], [1037, 7017], [996, 7017]], [[1041, 6990], [1072, 6990], [1072, 7011], [1041, 7011]], [[92, 7025], [567, 7025], [567, 7065], [92, 7065]], [[92, 7164], [344, 7164], [344, 7212], [92, 7212]], [[1107, 7198], [1352, 7198], [1352, 7261], [1107, 7261]], [[92, 7256], [481, 7256], [481, 7299], [92, 7299]], [[82, 7390], [248, 7390], [248, 7456], [82, 7456]], [[1188, 7428], [1360, 7428], [1360, 7506], [1188, 7506]], [[88, 7486], [509, 7486], [509, 7535], [88, 7535]], [[90, 7625], [292, 7625], [292, 7684], [90, 7684]], [[1246, 7666], [1356, 7666], [1356, 7730], [1246, 7730]], [[90, 7720], [399, 7720], [399, 7769], [90, 7769]]], "text_det_params": {"limit_side_len": 64, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.6, "rec_texts": ["14:29", "0.65", "5G", "67", "KB/S", "报告查询", "×", "fwcs.linkingcloud.cn", "东院生化检测", "*春香***岁", "申请科室：胰腺肿瘤临床研究门诊", "报告日期：2025-5-210:00:00", "项目明细", "项目名称", "结果/单位", "钙", "2.35mmol/l", "参考值：2.11-2.52mmol/l", "肌酐", "55umol/l", "参考值：41-81umol/", "钾", "4.32mmol/L", "参考值：3.5-5.3mmol/L", "氯", "107.0mmol/L", "参考值：96-108mmol/L", "镁", "0.88mmol/L", "参考值：0.75-1.02mmol/L", "钠", "141.0mmol/L", "参考值：137-147mmol/L", "尿素", "5.00mmol/l", "参考值：3.1-8.8mmol/l", "葡萄糖", "6.51mmol/l", "↑", "参考值：3.9-6.1mmol/l", "乳酸脱氢酶", "168U/L", "参考值：120-250U/L", "磷", "1.06mmol/L", "参考值：0.85-1.51mmol/L", "谷氨酰转肽酶", "19U/L", "参考值：7-45U/L", "白蛋白", "43.1g/l", "参考值：40-55.0g/", "谷丙转氨酶", "18.4U/I", "参考值：7-40U/I", "谷氨酸脱氢酶", "<2U/L", "参考值：女0-5U/L", "碱性磷酸酶", "62.9U/L", "参考值：50-135U/L", "尿酸", "218umol/I", "参考值：155-357umol/", "前白蛋白", "232mg/L", "参考值：250-400mg/L", "总二氧化碳", "27.6mmol/L", "参考值：23-29.0mmol/L", "谷草转氨酶", "20.8U/L", "参考值：13-35U/L", "谷草同工酶", "8.9U/L", "参考值：0.0-15.0U/L", "直接胆红素", "3.7umol/l", "参考值：≤8umol/l", "总胆红素", "9.3umol/l", "参考值：女≤21umol//", "总蛋白", "69.2g/l", "参考值：65-85g/l", "游离脂肪酸", "0.55mmol/L", "↑", "参考值：0.1-0.45mmol/L", "间接胆红素", "5.6umol/L", "参考值：0-12umol/L", "球蛋白", "26.1g/l", "参考值：20.0-40.0g/l", "白球比例", "1.65", "参考值：1.2-2.4"], "rec_scores": [0.9923356175422668, 0.9993331432342529, 0.7751380205154419, 0.9979028701782227, 0.9522445797920227, 0.9998095035552979, 0.8427753448486328, 0.9912495613098145, 0.9990605711936951, 0.9728195071220398, 0.9872652888298035, 0.9752963781356812, 0.9994392395019531, 0.9999428987503052, 0.9952508807182312, 0.9995112419128418, 0.9824736714363098, 0.9553098082542419, 0.9796848297119141, 0.9414166212081909, 0.9826298356056213, 0.999929666519165, 0.9925511479377747, 0.9853156805038452, 0.9998013377189636, 0.9970264434814453, 0.9872426986694336, 0.9993115663528442, 0.9953095316886902, 0.975684404373169, 0.9860407114028931, 0.9965953826904297, 0.9835124015808105, 0.9999121427536011, 0.9755629301071167, 0.9819527864456177, 0.9998793601989746, 0.9603812098503113, 0.9650686383247375, 0.9682273864746094, 0.996153712272644, 0.9983170032501221, 0.9839827418327332, 0.9997287392616272, 0.9973962903022766, 0.9841772317886353, 0.9979438185691833, 0.9981898069381714, 0.9869627356529236, 0.9998331069946289, 0.9764555096626282, 0.9916508197784424, 0.9984003901481628, 0.9731971621513367, 0.9220762252807617, 0.9941909313201904, 0.9914292097091675, 0.9856859445571899, 0.9988616108894348, 0.997063934803009, 0.9818447828292847, 0.9997734427452087, 0.9446777701377869, 0.9950498938560486, 0.9995474815368652, 0.9977788329124451, 0.9945635199546814, 0.9965527653694153, 0.995489239692688, 0.9875914454460144, 0.997938334941864, 0.9973784685134888, 0.9840999245643616, 0.9951184988021851, 0.9933944344520569, 0.9891998767852783, 0.9995080828666687, 0.9586607813835144, 0.9717469811439514, 0.9993269443511963, 0.9595175981521606, 0.9288128614425659, 0.9993808269500732, 0.9920817613601685, 0.9896281361579895, 0.994399905204773, 0.9963113069534302, 0.9854594469070435, 0.9943564534187317, 0.9988638162612915, 0.9960619807243347, 0.985287070274353, 0.9990528225898743, 0.9777430295944214, 0.9731333255767822, 0.9992220401763916, 0.9998406171798706, 0.996899425983429], "rec_polys": [[[65, 50], [205, 50], [205, 103], [65, 103]], [[890, 44], [984, 44], [984, 89], [890, 89]], [[1145, 48], [1242, 48], [1242, 103], [1145, 103]], [[1260, 54], [1323, 54], [1323, 103], [1260, 103]], [[898, 73], [974, 73], [974, 109], [898, 109]], [[589, 179], [845, 179], [845, 252], [589, 252]], [[33, 208], [110, 208], [110, 282], [33, 282]], [[515, 266], [916, 266], [916, 308], [515, 308]], [[29, 351], [297, 351], [297, 407], [29, 407]], [[79, 655], [457, 664], [455, 743], [77, 733]], [[96, 849], [841, 849], [841, 897], [96, 897]], [[94, 950], [773, 950], [773, 998], [94, 998]], [[83, 1228], [339, 1223], [340, 1294], [85, 1299]], [[81, 1408], [298, 1403], [299, 1467], [83, 1472]], [[1121, 1409], [1350, 1409], [1350, 1466], [1121, 1466]], [[82, 1553], [151, 1553], [151, 1619], [82, 1619]], [[1084, 1601], [1354, 1601], [1354, 1657], [1084, 1657]], [[88, 1649], [569, 1651], [568, 1700], [88, 1698]], [[82, 1788], [202, 1788], [202, 1857], [82, 1857]], [[1139, 1835], [1353, 1830], [1355, 1889], [1140, 1894]], [[92, 1889], [487, 1889], [487, 1930], [92, 1930]], [[84, 2024], [149, 2024], [149, 2087], [84, 2087]], [[1066, 2065], [1350, 2065], [1350, 2119], [1066, 2119]], [[92, 2121], [546, 2121], [546, 2161], [92, 2161]], [[86, 2256], [151, 2256], [151, 2319], [86, 2319]], [[1051, 2301], [1352, 2301], [1352, 2355], [1051, 2355]], [[92, 2355], [548, 2355], [548, 2395], [92, 2395]], [[82, 2488], [151, 2488], [151, 2555], [82, 2555]], [[1062, 2532], [1348, 2532], [1348, 2587], [1062, 2587]], [[92, 2587], [589, 2587], [589, 2629], [92, 2629]], [[84, 2722], [149, 2722], [149, 2786], [84, 2786]], [[1060, 2770], [1350, 2770], [1350, 2819], [1060, 2819]], [[92, 2821], [558, 2821], [558, 2863], [92, 2863]], [[84, 2958], [200, 2958], [200, 3018], [84, 3018]], [[1084, 3004], [1352, 3004], [1352, 3053], [1084, 3053]], [[92, 3057], [524, 3057], [524, 3097], [92, 3097]], [[86, 3192], [248, 3192], [248, 3250], [86, 3250]], [[1094, 3238], [1350, 3238], [1350, 3286], [1094, 3286]], [[1021, 3250], [1058, 3242], [1066, 3279], [1028, 3286]], [[92, 3289], [524, 3289], [524, 3329], [92, 3329]], [[90, 3430], [344, 3430], [344, 3478], [90, 3478]], [[1176, 3466], [1352, 3466], [1352, 3524], [1176, 3524]], [[92, 3522], [503, 3522], [503, 3563], [92, 3563]], [[84, 3655], [151, 3655], [151, 3722], [84, 3722]], [[1074, 3702], [1350, 3702], [1350, 3756], [1074, 3756]], [[92, 3756], [579, 3756], [579, 3797], [92, 3797]], [[92, 3897], [393, 3897], [393, 3946], [92, 3946]], [[1207, 3928], [1356, 3928], [1356, 3994], [1207, 3994]], [[92, 3990], [425, 3990], [425, 4030], [92, 4030]], [[92, 4127], [245, 4127], [245, 4182], [92, 4182]], [[1192, 4166], [1356, 4166], [1356, 4232], [1192, 4232]], [[92, 4222], [472, 4222], [472, 4264], [92, 4264]], [[90, 4363], [346, 4363], [346, 4416], [90, 4416]], [[1182, 4399], [1354, 4399], [1354, 4458], [1182, 4458]], [[90, 4452], [425, 4452], [425, 4494], [90, 4494]], [[92, 4597], [393, 4597], [393, 4645], [92, 4645]], [[1199, 4629], [1356, 4629], [1356, 4696], [1199, 4696]], [[90, 4690], [454, 4690], [454, 4730], [90, 4730]], [[88, 4831], [344, 4831], [344, 4879], [88, 4879]], [[1158, 4867], [1352, 4867], [1352, 4924], [1158, 4924]], [[92, 4922], [479, 4922], [479, 4964], [92, 4964]], [[84, 5057], [200, 5057], [200, 5119], [84, 5119]], [[1120, 5104], [1353, 5097], [1355, 5155], [1122, 5162]], [[92, 5154], [540, 5154], [540, 5196], [92, 5196]], [[88, 5293], [295, 5293], [295, 5349], [88, 5349]], [[1131, 5333], [1354, 5333], [1354, 5395], [1131, 5395]], [[92, 5389], [538, 5389], [538, 5432], [92, 5432]], [[87, 5524], [346, 5529], [345, 5585], [86, 5581]], [[1074, 5569], [1352, 5569], [1352, 5623], [1074, 5623]], [[92, 5623], [562, 5623], [562, 5664], [92, 5664]], [[89, 5760], [344, 5765], [343, 5815], [88, 5811]], [[1156, 5797], [1354, 5797], [1354, 5861], [1156, 5861]], [[92, 5857], [450, 5857], [450, 5898], [92, 5898]], [[90, 5994], [342, 5994], [342, 6045], [90, 6045]], [[1184, 6031], [1354, 6031], [1354, 6095], [1184, 6095]], [[92, 6091], [491, 6091], [491, 6131], [92, 6131]], [[90, 6230], [344, 6230], [344, 6279], [90, 6279]], [[1133, 6267], [1354, 6267], [1354, 6323], [1133, 6323]], [[92, 6323], [448, 6323], [448, 6365], [92, 6365]], [[88, 6460], [297, 6460], [297, 6517], [88, 6517]], [[1125, 6498], [1356, 6498], [1356, 6561], [1125, 6561]], [[92, 6555], [513, 6555], [513, 6597], [92, 6597]], [[85, 6689], [246, 6694], [244, 6755], [83, 6750]], [[1178, 6732], [1356, 6732], [1356, 6799], [1178, 6799]], [[92, 6789], [436, 6789], [436, 6831], [92, 6831]], [[92, 6932], [344, 6932], [344, 6980], [92, 6980]], [[1058, 6968], [1350, 6968], [1350, 7023], [1058, 7023]], [[996, 6980], [1037, 6980], [1037, 7017], [996, 7017]], [[92, 7025], [567, 7025], [567, 7065], [92, 7065]], [[92, 7164], [344, 7164], [344, 7212], [92, 7212]], [[1107, 7198], [1352, 7198], [1352, 7261], [1107, 7261]], [[92, 7256], [481, 7256], [481, 7299], [92, 7299]], [[82, 7390], [248, 7390], [248, 7456], [82, 7456]], [[1188, 7428], [1360, 7428], [1360, 7506], [1188, 7506]], [[88, 7486], [509, 7486], [509, 7535], [88, 7535]], [[90, 7625], [292, 7625], [292, 7684], [90, 7684]], [[1246, 7666], [1356, 7666], [1356, 7730], [1246, 7730]], [[90, 7720], [399, 7720], [399, 7769], [90, 7769]]], "rec_boxes": [[65, 50, 205, 103], [890, 44, 984, 89], [1145, 48, 1242, 103], [1260, 54, 1323, 103], [898, 73, 974, 109], [589, 179, 845, 252], [33, 208, 110, 282], [515, 266, 916, 308], [29, 351, 297, 407], [77, 655, 457, 743], [96, 849, 841, 897], [94, 950, 773, 998], [83, 1223, 340, 1299], [81, 1403, 299, 1472], [1121, 1409, 1350, 1466], [82, 1553, 151, 1619], [1084, 1601, 1354, 1657], [88, 1649, 569, 1700], [82, 1788, 202, 1857], [1139, 1830, 1355, 1894], [92, 1889, 487, 1930], [84, 2024, 149, 2087], [1066, 2065, 1350, 2119], [92, 2121, 546, 2161], [86, 2256, 151, 2319], [1051, 2301, 1352, 2355], [92, 2355, 548, 2395], [82, 2488, 151, 2555], [1062, 2532, 1348, 2587], [92, 2587, 589, 2629], [84, 2722, 149, 2786], [1060, 2770, 1350, 2819], [92, 2821, 558, 2863], [84, 2958, 200, 3018], [1084, 3004, 1352, 3053], [92, 3057, 524, 3097], [86, 3192, 248, 3250], [1094, 3238, 1350, 3286], [1021, 3242, 1066, 3286], [92, 3289, 524, 3329], [90, 3430, 344, 3478], [1176, 3466, 1352, 3524], [92, 3522, 503, 3563], [84, 3655, 151, 3722], [1074, 3702, 1350, 3756], [92, 3756, 579, 3797], [92, 3897, 393, 3946], [1207, 3928, 1356, 3994], [92, 3990, 425, 4030], [92, 4127, 245, 4182], [1192, 4166, 1356, 4232], [92, 4222, 472, 4264], [90, 4363, 346, 4416], [1182, 4399, 1354, 4458], [90, 4452, 425, 4494], [92, 4597, 393, 4645], [1199, 4629, 1356, 4696], [90, 4690, 454, 4730], [88, 4831, 344, 4879], [1158, 4867, 1352, 4924], [92, 4922, 479, 4964], [84, 5057, 200, 5119], [1120, 5097, 1355, 5162], [92, 5154, 540, 5196], [88, 5293, 295, 5349], [1131, 5333, 1354, 5395], [92, 5389, 538, 5432], [86, 5524, 346, 5585], [1074, 5569, 1352, 5623], [92, 5623, 562, 5664], [88, 5760, 344, 5815], [1156, 5797, 1354, 5861], [92, 5857, 450, 5898], [90, 5994, 342, 6045], [1184, 6031, 1354, 6095], [92, 6091, 491, 6131], [90, 6230, 344, 6279], [1133, 6267, 1354, 6323], [92, 6323, 448, 6365], [88, 6460, 297, 6517], [1125, 6498, 1356, 6561], [92, 6555, 513, 6597], [83, 6689, 246, 6755], [1178, 6732, 1356, 6799], [92, 6789, 436, 6831], [92, 6932, 344, 6980], [1058, 6968, 1350, 7023], [996, 6980, 1037, 7017], [92, 7025, 567, 7065], [92, 7164, 344, 7212], [1107, 7198, 1352, 7261], [92, 7256, 481, 7299], [82, 7390, 248, 7456], [1188, 7428, 1360, 7506], [88, 7486, 509, 7535], [90, 7625, 292, 7684], [1246, 7666, 1356, 7730], [90, 7720, 399, 7769]]}