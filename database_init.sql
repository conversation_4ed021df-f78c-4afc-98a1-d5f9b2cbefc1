-- 创建医疗数据库
CREATE DATABASE IF NOT EXISTS medical CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE medical;

-- 创建医疗检查主表
CREATE TABLE IF NOT EXISTS medical_check (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    medical_date DATE NOT NULL COMMENT '检查日期',
    medical_type INT NOT NULL COMMENT '检查类型：1-血常规，2-尿常规，3-生化，4-其他',
    status ENUM('normal', 'abnormal') NOT NULL DEFAULT 'normal' COMMENT '整体状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_date (user_id, medical_date),
    INDEX idx_medical_type (medical_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='医疗检查主表';

-- 创建医疗检查详情表
CREATE TABLE IF NOT EXISTS medical_check_detail (
    id INT AUTO_INCREMENT PRIMARY KEY,
    medical_id INT NOT NULL COMMENT '医疗检查主表ID',
    index_name VARCHAR(100) NOT NULL COMMENT '指标名称',
    index_value VARCHAR(50) COMMENT '指标值',
    index_unit VARCHAR(20) COMMENT '单位',
    reference_value VARCHAR(100) COMMENT '参考值',
    index_status ENUM('normal', 'high', 'low') NOT NULL DEFAULT 'normal' COMMENT '指标状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (medical_id) REFERENCES medical_check(id) ON DELETE CASCADE,
    INDEX idx_medical_id (medical_id),
    INDEX idx_index_name (index_name),
    INDEX idx_index_status (index_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='医疗检查详情表';

-- 创建医疗指标参考值表
CREATE TABLE IF NOT EXISTS medical_index (
    id INT AUTO_INCREMENT PRIMARY KEY,
    index_name VARCHAR(100) NOT NULL UNIQUE COMMENT '指标名称',
    reference_min DECIMAL(10,3) COMMENT '参考值最小值',
    reference_max DECIMAL(10,3) COMMENT '参考值最大值',
    unit VARCHAR(20) COMMENT '单位',
    description TEXT COMMENT '指标描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_index_name (index_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='医疗指标参考值表';

-- 插入常见医疗指标参考值
INSERT INTO medical_index (index_name, reference_min, reference_max, unit, description) VALUES
('白细胞计数', 3.5, 9.5, '10^9/L', '白细胞计数'),
('红细胞计数', 4.3, 5.8, '10^12/L', '红细胞计数'),
('血红蛋白', 130, 175, 'g/L', '血红蛋白浓度'),
('血小板计数', 125, 350, '10^9/L', '血小板计数'),
('中性粒细胞%', 40, 75, '%', '中性粒细胞百分比'),
('淋巴细胞%', 20, 50, '%', '淋巴细胞百分比'),
('单核细胞%', 3, 10, '%', '单核细胞百分比'),
('嗜酸性细胞%', 0.4, 8, '%', '嗜酸性细胞百分比'),
('嗜碱性细胞%', 0, 1, '%', '嗜碱性细胞百分比'),
('中性粒细胞数', 1.8, 6.3, '10^9/L', '中性粒细胞绝对值'),
('淋巴细胞数', 1.1, 3.2, '10^9/L', '淋巴细胞绝对值'),
('单核细胞数', 0.1, 0.6, '10^9/L', '单核细胞绝对值'),
('嗜酸性细胞数', 0.02, 0.52, '10^9/L', '嗜酸性细胞绝对值'),
('嗜碱性细胞数', 0, 0.06, '10^9/L', '嗜碱性细胞绝对值'),
('平均红细胞体积', 82, 100, 'fL', '平均红细胞体积'),
('平均血红蛋白含量', 27, 34, 'pg', '平均血红蛋白含量'),
('平均血红蛋白浓度', 316, 354, 'g/L', '平均血红蛋白浓度'),
('红细胞分布宽度', 11.5, 14.5, '%', '红细胞分布宽度'),
('血小板分布宽度', 15.5, 18.1, '%', '血小板分布宽度'),
('平均血小板体积', 7.4, 12.5, 'fL', '平均血小板体积'),
('糖类抗原125', 0, 35, 'U/mL', '糖类抗原125'),
('糖类抗原199', 0, 37, 'U/mL', '糖类抗原199');

-- 创建医疗检查报告表
CREATE TABLE IF NOT EXISTS medical_exam (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL DEFAULT '1' COMMENT '用户ID',
    medical_date DATE NOT NULL COMMENT '检查日期',
    exam_info TEXT COMMENT '检查方法信息',
    exam_diag TEXT COMMENT '放射性诊断信息',
    exam_type INT NOT NULL DEFAULT 1 COMMENT '检查类型：1-影像检查',
    hospital VARCHAR(100) NOT NULL DEFAULT '复旦肿瘤' COMMENT '医院名称',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_date (user_id, medical_date),
    INDEX idx_exam_type (exam_type),
    INDEX idx_hospital (hospital)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='医疗检查报告表';
