{"input_path": "D:\\dev\\medicalReport\\ocr\\source\\05152.jpg", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[76, 46], [229, 46], [229, 106], [76, 106]], [[970, 44], [1129, 44], [1129, 86], [970, 86]], [[245, 54], [297, 54], [297, 100], [245, 100]], [[914, 54], [959, 54], [959, 102], [914, 102]], [[1166, 46], [1248, 46], [1248, 106], [1166, 106]], [[1270, 52], [1354, 52], [1354, 106], [1270, 106]], [[978, 72], [1053, 72], [1053, 108], [978, 108]], [[575, 206], [859, 206], [859, 284], [575, 284]], [[567, 414], [869, 414], [869, 500], [567, 500]], [[70, 638], [297, 638], [297, 718], [70, 718]], [[78, 788], [869, 788], [869, 842], [78, 842]], [[76, 906], [501, 906], [501, 962], [76, 962]], [[78, 1028], [581, 1028], [581, 1082], [78, 1082]], [[78, 1146], [871, 1146], [871, 1200], [78, 1200]], [[68, 1354], [196, 1354], [196, 1422], [68, 1422]], [[1238, 1350], [1372, 1350], [1372, 1428], [1238, 1428]], [[78, 1546], [358, 1546], [358, 1602], [78, 1602]], [[1274, 1540], [1372, 1540], [1372, 1606], [1274, 1606]], [[78, 1632], [499, 1632], [499, 1680], [78, 1680]], [[1036, 1626], [1369, 1630], [1368, 1684], [1035, 1680]], [[78, 1812], [434, 1812], [434, 1868], [78, 1868]], [[1242, 1808], [1368, 1808], [1368, 1870], [1242, 1870]], [[78, 1898], [554, 1898], [554, 1946], [78, 1946]], [[1164, 1892], [1370, 1892], [1370, 1950], [1164, 1950]], [[78, 2078], [378, 2078], [378, 2134], [78, 2134]], [[1244, 2076], [1366, 2076], [1366, 2136], [1244, 2136]], [[78, 2164], [552, 2164], [552, 2212], [78, 2212]], [[1164, 2160], [1368, 2160], [1368, 2216], [1164, 2216]], [[78, 2346], [376, 2346], [376, 2400], [78, 2400]], [[1274, 2340], [1368, 2340], [1368, 2404], [1274, 2404]], [[80, 2434], [524, 2434], [524, 2474], [80, 2474]], [[1164, 2426], [1368, 2426], [1368, 2484], [1164, 2484]], [[78, 2612], [487, 2612], [487, 2666], [78, 2666]], [[1274, 2606], [1372, 2606], [1372, 2672], [1274, 2672]], [[1165, 2687], [1369, 2692], [1367, 2750], [1163, 2745]], [[80, 2698], [452, 2698], [452, 2740], [80, 2740]], [[78, 2878], [485, 2878], [485, 2932], [78, 2932]], [[1272, 2872], [1368, 2872], [1368, 2936], [1272, 2936]], [[80, 2964], [491, 2964], [491, 3006], [80, 3006]], [[1166, 2958], [1368, 2958], [1368, 3016], [1166, 3016]], [[78, 3144], [356, 3144], [356, 3198], [78, 3198]], [[1272, 3138], [1372, 3138], [1372, 3204], [1272, 3204]], [[80, 3232], [491, 3232], [491, 3274], [80, 3274]], [[1039, 3230], [1364, 3230], [1364, 3278], [1039, 3278]], [[76, 3408], [301, 3408], [301, 3464], [76, 3464]], [[1275, 3401], [1372, 3406], [1368, 3472], [1271, 3467]], [[80, 3498], [493, 3498], [493, 3540], [80, 3540]], [[1040, 3491], [1365, 3496], [1364, 3546], [1039, 3541]], [[76, 3676], [301, 3676], [301, 3732], [76, 3732]], [[1272, 3672], [1370, 3672], [1370, 3736], [1272, 3736]], [[78, 3762], [495, 3762], [495, 3810], [78, 3810]], [[1041, 3762], [1364, 3762], [1364, 3810], [1041, 3810]], [[80, 3946], [407, 3946], [407, 3994], [80, 3994]], [[1244, 3940], [1368, 3940], [1368, 4000], [1244, 4000]], [[80, 4029], [550, 4029], [550, 4071], [80, 4071]], [[1043, 4027], [1364, 4027], [1364, 4075], [1043, 4075]], [[80, 4211], [409, 4211], [409, 4259], [80, 4259]], [[1244, 4205], [1368, 4205], [1368, 4265], [1244, 4265]], [[80, 4297], [550, 4297], [550, 4337], [80, 4337]], [[1043, 4293], [1364, 4293], [1364, 4341], [1043, 4341]], [[74, 4473], [356, 4473], [356, 4527], [74, 4527]], [[1170, 4471], [1358, 4471], [1358, 4527], [1170, 4527]], [[80, 4563], [550, 4563], [550, 4603], [80, 4603]], [[1017, 4563], [1360, 4563], [1360, 4605], [1017, 4605]], [[76, 4739], [295, 4739], [295, 4789], [76, 4789]], [[1184, 4737], [1356, 4737], [1356, 4793], [1184, 4793]], [[80, 4827], [520, 4827], [520, 4869], [80, 4869]], [[1124, 4819], [1365, 4824], [1364, 4882], [1123, 4877]], [[78, 5009], [356, 5009], [356, 5057], [78, 5057]], [[1170, 5005], [1356, 5005], [1356, 5061], [1170, 5061]], [[82, 5093], [550, 5093], [550, 5135], [82, 5135]], [[1164, 5087], [1368, 5087], [1368, 5143], [1164, 5143]], [[78, 5278], [464, 5273], [465, 5321], [78, 5326]], [[1244, 5269], [1370, 5269], [1370, 5329], [1244, 5329]], [[1172, 5351], [1368, 5351], [1368, 5409], [1172, 5409]], [[80, 5361], [581, 5361], [581, 5401], [80, 5401]], [[82, 5545], [464, 5545], [464, 5587], [82, 5587]], [[1170, 5537], [1356, 5537], [1356, 5593], [1170, 5593]], [[1144, 5610], [1370, 5624], [1365, 5689], [1140, 5674]], [[82, 5627], [550, 5627], [550, 5667], [82, 5667]], [[1256, 5801], [1368, 5801], [1368, 5863], [1256, 5863]], [[82, 5811], [518, 5811], [518, 5853], [82, 5853]], [[1122, 5881], [1369, 5888], [1367, 5952], [1120, 5945]], [[80, 5893], [526, 5893], [526, 5935], [80, 5935]], [[78, 6073], [464, 6073], [464, 6121], [78, 6121]], [[1246, 6067], [1370, 6067], [1370, 6129], [1246, 6129]], [[1162, 6149], [1370, 6149], [1370, 6213], [1162, 6213]], [[80, 6161], [552, 6161], [552, 6201], [80, 6201]], [[76, 6337], [356, 6337], [356, 6393], [76, 6393]], [[1260, 6330], [1369, 6336], [1366, 6398], [1257, 6392]], [[80, 6427], [524, 6427], [524, 6467], [80, 6467]], [[1039, 6421], [1366, 6421], [1366, 6475], [1039, 6475]], [[1272, 6597], [1372, 6597], [1372, 6663], [1272, 6663]], [[80, 6607], [466, 6607], [466, 6655], [80, 6655]], [[80, 6691], [524, 6691], [524, 6733], [80, 6733]], [[1172, 6683], [1368, 6683], [1368, 6741], [1172, 6741]], [[74, 6871], [354, 6871], [354, 6925], [74, 6925]], [[1240, 6865], [1372, 6865], [1372, 6931], [1240, 6931]], [[78, 6955], [583, 6955], [583, 7003], [78, 7003]], [[1164, 6951], [1368, 6951], [1368, 7009], [1164, 7009]], [[76, 7133], [469, 7137], [468, 7193], [75, 7189]], [[1287, 7129], [1370, 7129], [1370, 7199], [1287, 7199]], [[78, 7223], [468, 7223], [468, 7271], [78, 7271]], [[1162, 7217], [1368, 7217], [1368, 7275], [1162, 7275]], [[76, 7427], [524, 7427], [524, 7483], [76, 7483]], [[80, 7549], [522, 7549], [522, 7605], [80, 7605]], [[78, 7667], [873, 7667], [873, 7727], [78, 7727]], [[243, 7841], [1195, 7841], [1195, 7889], [243, 7889]]], "text_det_params": {"limit_side_len": 64, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.0, "rec_texts": ["16:42", "1.00÷5G", "→", "8", "5", "80", "KB/S", "报告详情", "检验报告", "李春香", "证件号：332623195201264044", "单号：36780885", "申请科室：1002044", "检验日期：2025-05-1509:53:12", "项目", "结果", "白细胞计数", "5.0", "参考范围：3.5-9.5", "单位：10E9/L", "中性粒细胞(%)", "63.4", "参考范围：40.0-75.0", "单位：%", "淋巴细胞(%)", "30.1", "参考范围：20.0-50.0", "单位：%", "单核细胞(%)", "5.1", "参考范围：3.0-10.0", "单位：%", "嗜酸性粒细胞(%)", "1.3", "单位：%", "参考范围：0.4-8", "嗜碱性粒细胞(%)", "0.1", "参考范围：0.0-1.0", "单位：%", "中性粒细胞", "3.2", "参考范围：1.8-6.3", "单位：10E9/L", "淋巴细胞", "1.5", "参考范围：1.1-3.2", "单位：10E9/L", "单核细胞", "0.3", "参考范围：0.1-0.6", "单位：10E9/L", "嗜酸性粒细胞", "0.06", "参考范围：0.02-0.52", "单位：10E9/L", "嗜碱性粒细胞", "0.00", "参考范围：0.00-0.06", "单位：10E9/L", "红细胞计数", "3.70↓", "参考范围：3.80-5.10", "单位：10E12/L", "血红蛋白", "100↓", "参考范围：115-150", "单位：g/L", "红细胞压积", "31.2↓", "参考范围：35.0-45.0", "单位：%", "平均红细胞体积", "84.4", "单位：fl", "参考范围：82.0-100.0", "平均血红蛋白量", "26.9 ↓", "单位：pg", "参考范围：27.0-34.0", "319", "平均血红蛋白浓度", "单位：g/L", "参考范围：316-354", "红细胞分布宽度", "13.0", "单位：%", "参考范围：11.5-14.5", "血小板计数", "160", "参考范围：125-350", "单位：10E9/L", "9.4", "平均血小板体积", "参考范围：7.4-12.5", "单位：f", "血小板压积", "0.15", "参考范围：0.05-0.282", "单位：%", "血小板分布宽度", "16", "参考范围：10-20", "单位：%", "报告医师：黄玲燕", "审核医师：叶海南", "审核时间：2025-05-1510:05:42", "此报告单仅作参考，以医院实际纸质报告为准"], "rec_scores": [0.9927816390991211, 0.9449572563171387, 0.47129055857658386, 0.2802971303462982, 0.7125231623649597, 0.9981768727302551, 0.9616656303405762, 0.9994255304336548, 0.9997677803039551, 0.9990271925926208, 0.996727705001831, 0.9938113689422607, 0.9914782047271729, 0.9851962327957153, 0.9999260902404785, 0.9995149970054626, 0.999424159526825, 0.9998695850372314, 0.995793342590332, 0.9939112067222595, 0.9507838487625122, 0.9997292757034302, 0.9847596287727356, 0.9863608479499817, 0.9725314378738403, 0.9999110698699951, 0.9954560995101929, 0.9885716438293457, 0.968889594078064, 0.9998314380645752, 0.9926413297653198, 0.9850659370422363, 0.9500603079795837, 0.9998831748962402, 0.9853894710540771, 0.9896343946456909, 0.9663790464401245, 0.9997157454490662, 0.9794597625732422, 0.9791021943092346, 0.9993187785148621, 0.9997127056121826, 0.9798511862754822, 0.9928581714630127, 0.9987783432006836, 0.9997666478157043, 0.9627369046211243, 0.9813538789749146, 0.9998809695243835, 0.9996832013130188, 0.9943955540657043, 0.9693565368652344, 0.998852550983429, 0.999838650226593, 0.9771440625190735, 0.9754304885864258, 0.9985143542289734, 0.9998060464859009, 0.992439329624176, 0.9659328460693359, 0.9995932579040527, 0.99119633436203, 0.9891132116317749, 0.985740065574646, 0.9989697933197021, 0.994109034538269, 0.9373273253440857, 0.9919312000274658, 0.9981842041015625, 0.9902281761169434, 0.973078727722168, 0.9867057800292969, 0.9984186887741089, 0.9997652769088745, 0.9145914912223816, 0.9873628616333008, 0.9861312508583069, 0.9227406978607178, 0.9851465225219727, 0.9885913729667664, 0.9997947812080383, 0.9938061833381653, 0.9925806522369385, 0.992323100566864, 0.9922032952308655, 0.9999590516090393, 0.9846001863479614, 0.977088987827301, 0.9990943074226379, 0.9998591542243958, 0.9484214782714844, 0.9973697662353516, 0.9998181462287903, 0.9964903593063354, 0.9609476327896118, 0.9920110702514648, 0.9994882345199585, 0.999708354473114, 0.9916051626205444, 0.9837304949760437, 0.996749758720398, 0.9998403787612915, 0.9961403608322144, 0.9897593855857849, 0.9955928921699524, 0.9913921356201172, 0.9826900362968445, 0.999269962310791], "rec_polys": [[[76, 46], [229, 46], [229, 106], [76, 106]], [[970, 44], [1129, 44], [1129, 86], [970, 86]], [[245, 54], [297, 54], [297, 100], [245, 100]], [[914, 54], [959, 54], [959, 102], [914, 102]], [[1166, 46], [1248, 46], [1248, 106], [1166, 106]], [[1270, 52], [1354, 52], [1354, 106], [1270, 106]], [[978, 72], [1053, 72], [1053, 108], [978, 108]], [[575, 206], [859, 206], [859, 284], [575, 284]], [[567, 414], [869, 414], [869, 500], [567, 500]], [[70, 638], [297, 638], [297, 718], [70, 718]], [[78, 788], [869, 788], [869, 842], [78, 842]], [[76, 906], [501, 906], [501, 962], [76, 962]], [[78, 1028], [581, 1028], [581, 1082], [78, 1082]], [[78, 1146], [871, 1146], [871, 1200], [78, 1200]], [[68, 1354], [196, 1354], [196, 1422], [68, 1422]], [[1238, 1350], [1372, 1350], [1372, 1428], [1238, 1428]], [[78, 1546], [358, 1546], [358, 1602], [78, 1602]], [[1274, 1540], [1372, 1540], [1372, 1606], [1274, 1606]], [[78, 1632], [499, 1632], [499, 1680], [78, 1680]], [[1036, 1626], [1369, 1630], [1368, 1684], [1035, 1680]], [[78, 1812], [434, 1812], [434, 1868], [78, 1868]], [[1242, 1808], [1368, 1808], [1368, 1870], [1242, 1870]], [[78, 1898], [554, 1898], [554, 1946], [78, 1946]], [[1164, 1892], [1370, 1892], [1370, 1950], [1164, 1950]], [[78, 2078], [378, 2078], [378, 2134], [78, 2134]], [[1244, 2076], [1366, 2076], [1366, 2136], [1244, 2136]], [[78, 2164], [552, 2164], [552, 2212], [78, 2212]], [[1164, 2160], [1368, 2160], [1368, 2216], [1164, 2216]], [[78, 2346], [376, 2346], [376, 2400], [78, 2400]], [[1274, 2340], [1368, 2340], [1368, 2404], [1274, 2404]], [[80, 2434], [524, 2434], [524, 2474], [80, 2474]], [[1164, 2426], [1368, 2426], [1368, 2484], [1164, 2484]], [[78, 2612], [487, 2612], [487, 2666], [78, 2666]], [[1274, 2606], [1372, 2606], [1372, 2672], [1274, 2672]], [[1165, 2687], [1369, 2692], [1367, 2750], [1163, 2745]], [[80, 2698], [452, 2698], [452, 2740], [80, 2740]], [[78, 2878], [485, 2878], [485, 2932], [78, 2932]], [[1272, 2872], [1368, 2872], [1368, 2936], [1272, 2936]], [[80, 2964], [491, 2964], [491, 3006], [80, 3006]], [[1166, 2958], [1368, 2958], [1368, 3016], [1166, 3016]], [[78, 3144], [356, 3144], [356, 3198], [78, 3198]], [[1272, 3138], [1372, 3138], [1372, 3204], [1272, 3204]], [[80, 3232], [491, 3232], [491, 3274], [80, 3274]], [[1039, 3230], [1364, 3230], [1364, 3278], [1039, 3278]], [[76, 3408], [301, 3408], [301, 3464], [76, 3464]], [[1275, 3401], [1372, 3406], [1368, 3472], [1271, 3467]], [[80, 3498], [493, 3498], [493, 3540], [80, 3540]], [[1040, 3491], [1365, 3496], [1364, 3546], [1039, 3541]], [[76, 3676], [301, 3676], [301, 3732], [76, 3732]], [[1272, 3672], [1370, 3672], [1370, 3736], [1272, 3736]], [[78, 3762], [495, 3762], [495, 3810], [78, 3810]], [[1041, 3762], [1364, 3762], [1364, 3810], [1041, 3810]], [[80, 3946], [407, 3946], [407, 3994], [80, 3994]], [[1244, 3940], [1368, 3940], [1368, 4000], [1244, 4000]], [[80, 4029], [550, 4029], [550, 4071], [80, 4071]], [[1043, 4027], [1364, 4027], [1364, 4075], [1043, 4075]], [[80, 4211], [409, 4211], [409, 4259], [80, 4259]], [[1244, 4205], [1368, 4205], [1368, 4265], [1244, 4265]], [[80, 4297], [550, 4297], [550, 4337], [80, 4337]], [[1043, 4293], [1364, 4293], [1364, 4341], [1043, 4341]], [[74, 4473], [356, 4473], [356, 4527], [74, 4527]], [[1170, 4471], [1358, 4471], [1358, 4527], [1170, 4527]], [[80, 4563], [550, 4563], [550, 4603], [80, 4603]], [[1017, 4563], [1360, 4563], [1360, 4605], [1017, 4605]], [[76, 4739], [295, 4739], [295, 4789], [76, 4789]], [[1184, 4737], [1356, 4737], [1356, 4793], [1184, 4793]], [[80, 4827], [520, 4827], [520, 4869], [80, 4869]], [[1124, 4819], [1365, 4824], [1364, 4882], [1123, 4877]], [[78, 5009], [356, 5009], [356, 5057], [78, 5057]], [[1170, 5005], [1356, 5005], [1356, 5061], [1170, 5061]], [[82, 5093], [550, 5093], [550, 5135], [82, 5135]], [[1164, 5087], [1368, 5087], [1368, 5143], [1164, 5143]], [[78, 5278], [464, 5273], [465, 5321], [78, 5326]], [[1244, 5269], [1370, 5269], [1370, 5329], [1244, 5329]], [[1172, 5351], [1368, 5351], [1368, 5409], [1172, 5409]], [[80, 5361], [581, 5361], [581, 5401], [80, 5401]], [[82, 5545], [464, 5545], [464, 5587], [82, 5587]], [[1170, 5537], [1356, 5537], [1356, 5593], [1170, 5593]], [[1144, 5610], [1370, 5624], [1365, 5689], [1140, 5674]], [[82, 5627], [550, 5627], [550, 5667], [82, 5667]], [[1256, 5801], [1368, 5801], [1368, 5863], [1256, 5863]], [[82, 5811], [518, 5811], [518, 5853], [82, 5853]], [[1122, 5881], [1369, 5888], [1367, 5952], [1120, 5945]], [[80, 5893], [526, 5893], [526, 5935], [80, 5935]], [[78, 6073], [464, 6073], [464, 6121], [78, 6121]], [[1246, 6067], [1370, 6067], [1370, 6129], [1246, 6129]], [[1162, 6149], [1370, 6149], [1370, 6213], [1162, 6213]], [[80, 6161], [552, 6161], [552, 6201], [80, 6201]], [[76, 6337], [356, 6337], [356, 6393], [76, 6393]], [[1260, 6330], [1369, 6336], [1366, 6398], [1257, 6392]], [[80, 6427], [524, 6427], [524, 6467], [80, 6467]], [[1039, 6421], [1366, 6421], [1366, 6475], [1039, 6475]], [[1272, 6597], [1372, 6597], [1372, 6663], [1272, 6663]], [[80, 6607], [466, 6607], [466, 6655], [80, 6655]], [[80, 6691], [524, 6691], [524, 6733], [80, 6733]], [[1172, 6683], [1368, 6683], [1368, 6741], [1172, 6741]], [[74, 6871], [354, 6871], [354, 6925], [74, 6925]], [[1240, 6865], [1372, 6865], [1372, 6931], [1240, 6931]], [[78, 6955], [583, 6955], [583, 7003], [78, 7003]], [[1164, 6951], [1368, 6951], [1368, 7009], [1164, 7009]], [[76, 7133], [469, 7137], [468, 7193], [75, 7189]], [[1287, 7129], [1370, 7129], [1370, 7199], [1287, 7199]], [[78, 7223], [468, 7223], [468, 7271], [78, 7271]], [[1162, 7217], [1368, 7217], [1368, 7275], [1162, 7275]], [[76, 7427], [524, 7427], [524, 7483], [76, 7483]], [[80, 7549], [522, 7549], [522, 7605], [80, 7605]], [[78, 7667], [873, 7667], [873, 7727], [78, 7727]], [[243, 7841], [1195, 7841], [1195, 7889], [243, 7889]]], "rec_boxes": [[76, 46, 229, 106], [970, 44, 1129, 86], [245, 54, 297, 100], [914, 54, 959, 102], [1166, 46, 1248, 106], [1270, 52, 1354, 106], [978, 72, 1053, 108], [575, 206, 859, 284], [567, 414, 869, 500], [70, 638, 297, 718], [78, 788, 869, 842], [76, 906, 501, 962], [78, 1028, 581, 1082], [78, 1146, 871, 1200], [68, 1354, 196, 1422], [1238, 1350, 1372, 1428], [78, 1546, 358, 1602], [1274, 1540, 1372, 1606], [78, 1632, 499, 1680], [1035, 1626, 1369, 1684], [78, 1812, 434, 1868], [1242, 1808, 1368, 1870], [78, 1898, 554, 1946], [1164, 1892, 1370, 1950], [78, 2078, 378, 2134], [1244, 2076, 1366, 2136], [78, 2164, 552, 2212], [1164, 2160, 1368, 2216], [78, 2346, 376, 2400], [1274, 2340, 1368, 2404], [80, 2434, 524, 2474], [1164, 2426, 1368, 2484], [78, 2612, 487, 2666], [1274, 2606, 1372, 2672], [1163, 2687, 1369, 2750], [80, 2698, 452, 2740], [78, 2878, 485, 2932], [1272, 2872, 1368, 2936], [80, 2964, 491, 3006], [1166, 2958, 1368, 3016], [78, 3144, 356, 3198], [1272, 3138, 1372, 3204], [80, 3232, 491, 3274], [1039, 3230, 1364, 3278], [76, 3408, 301, 3464], [1271, 3401, 1372, 3472], [80, 3498, 493, 3540], [1039, 3491, 1365, 3546], [76, 3676, 301, 3732], [1272, 3672, 1370, 3736], [78, 3762, 495, 3810], [1041, 3762, 1364, 3810], [80, 3946, 407, 3994], [1244, 3940, 1368, 4000], [80, 4029, 550, 4071], [1043, 4027, 1364, 4075], [80, 4211, 409, 4259], [1244, 4205, 1368, 4265], [80, 4297, 550, 4337], [1043, 4293, 1364, 4341], [74, 4473, 356, 4527], [1170, 4471, 1358, 4527], [80, 4563, 550, 4603], [1017, 4563, 1360, 4605], [76, 4739, 295, 4789], [1184, 4737, 1356, 4793], [80, 4827, 520, 4869], [1123, 4819, 1365, 4882], [78, 5009, 356, 5057], [1170, 5005, 1356, 5061], [82, 5093, 550, 5135], [1164, 5087, 1368, 5143], [78, 5273, 465, 5326], [1244, 5269, 1370, 5329], [1172, 5351, 1368, 5409], [80, 5361, 581, 5401], [82, 5545, 464, 5587], [1170, 5537, 1356, 5593], [1140, 5610, 1370, 5689], [82, 5627, 550, 5667], [1256, 5801, 1368, 5863], [82, 5811, 518, 5853], [1120, 5881, 1369, 5952], [80, 5893, 526, 5935], [78, 6073, 464, 6121], [1246, 6067, 1370, 6129], [1162, 6149, 1370, 6213], [80, 6161, 552, 6201], [76, 6337, 356, 6393], [1257, 6330, 1369, 6398], [80, 6427, 524, 6467], [1039, 6421, 1366, 6475], [1272, 6597, 1372, 6663], [80, 6607, 466, 6655], [80, 6691, 524, 6733], [1172, 6683, 1368, 6741], [74, 6871, 354, 6925], [1240, 6865, 1372, 6931], [78, 6955, 583, 7003], [1164, 6951, 1368, 7009], [75, 7133, 469, 7193], [1287, 7129, 1370, 7199], [78, 7223, 468, 7271], [1162, 7217, 1368, 7275], [76, 7427, 524, 7483], [80, 7549, 522, 7605], [78, 7667, 873, 7727], [243, 7841, 1195, 7889]]}