from paddleocr import PaddleOCR
import pymysql
from datetime import datetime
import re
import logging
import os
import shutil

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class DatabaseManager:
    def __init__(self, config):
        self.config = config
        self._validate_config()

    def _validate_config(self):
        required_keys = ['host', 'user', 'password', 'database']
        if not all(k in self.config for k in required_keys):
            raise ValueError("Missing required database configuration keys")

    def get_connection(self):
        try:
            return pymysql.connect(
                host=self.config['host'],
                user=self.config['user'],
                password=self.config['password'],
                database=self.config['database'],
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
        except pymysql.Error as e:
            logging.error(f"Database connection failed: {str(e)}")
            raise

class ReportProcessor:
    DATE_PATTERN = re.compile(r'报告日期：(\d{4}-\d{1,2}-\d{1,2})')
    VALUE_UNIT_PATTERN = re.compile(r'^(\d+\.?\d*)(.*)')

    def __init__(self, db_manager):
        self.db_manager = db_manager

    def process_report(self, image_path):
        ocr = PaddleOCR(
            text_detection_model_name="PP-OCRv5_server_det",
            text_recognition_model_name="PP-OCRv5_server_rec",
            use_doc_unwarping=True
        )
        result = ocr.predict(image_path)

        for res in result:
            self._process_single_result(res)

    def _process_single_result(self, res):
        res.save_to_json("output")
        data = self._parse_ocr_result(res)
        
        if data:
            self._save_to_database(data)

    def _parse_ocr_result(self, res):
        try:
            # 修复：直接访问字典，而不是调用to_dict()方法
            if hasattr(res, 'rec_texts'):
                rec_texts = res.rec_texts
            elif isinstance(res, dict) and 'rec_texts' in res:
                rec_texts = res['rec_texts']
            else:
                # 如果res是OCR结果对象，尝试获取其内部数据
                rec_texts = getattr(res, 'rec_texts', None)
                if rec_texts is None:
                    logging.error(f"Cannot find rec_texts in OCR result: {type(res)}")
                    return None
            
            medical_date = self._extract_medical_date(rec_texts)
            lab_data = self._parse_lab_items(rec_texts)
            
            return {
                'medical_date': medical_date,
                'lab_items': lab_data
            }
        except (KeyError, TypeError, AttributeError) as e:
            logging.error(f"OCR result parsing error: {str(e)}")
            return None

    def _extract_medical_date(self, rec_texts):
        # 原有日期匹配逻辑
        for text in rec_texts:
            match = self.DATE_PATTERN.search(text)
            if match:
                try:
                    return datetime.strptime(match.group(1), '%Y-%m-%d').date()
                except ValueError as e:
                    logging.warning(f"无效日期格式: {match.group(1)}")
        
        # 新增报告时间匹配逻辑
        for idx, text in enumerate(rec_texts):
            if '报告时间' in text and idx < len(rec_texts)-1:
                next_text = rec_texts[idx+1]
                if len(next_text) >= 10:
                    date_candidate = next_text[:10]
                    try:
                        return datetime.strptime(date_candidate, '%Y-%m-%d').date()
                    except ValueError:
                        logging.warning(f"备选日期格式无效: {date_candidate}")
        
        logging.error("未找到有效日期信息")
        return None

    def _parse_lab_items(self, rec_texts):
        try:
            start_index = rec_texts.index('结果/单位') + 1 
        except ValueError:
            try:
                start_index = rec_texts.index('结果') + 1
            except ValueError:
                start_index = 0
    
        items = []
        valid_group = []
        i = start_index
        while i < len(rec_texts):
            # 跳过无效条目
            if self._is_invalid_entry(rec_texts[i]):
                i += 1
                continue
            
            try:
                valid_group.append(rec_texts[i])

                if len(valid_group) == 3:
                    print(valid_group)
                    items.append(self._parse_lab_item(valid_group))
                    valid_group = []
            except IndexError:
                pass
            
            i += 1
        
        return items
    
    # 新增验证方法
    def _is_invalid_entry(self, text):
        return not text or any(c in text for c in ['↓', '↑'])

    def _get_valid_group(self, rec_texts, start):
        group = []
        for i in range(start, min(start+4, len(rec_texts))):
            if self._is_invalid_entry(rec_texts[i]):
                break
            group.append(rec_texts[i])
        return group

    def _parse_lab_item(self, group):
        # 清理特殊符号后进行匹配
        raw_value = (group[1] if len(group) >=2 else '').replace('↓','').replace('↑','').strip()
        value_match = self.VALUE_UNIT_PATTERN.match(raw_value)
        
        return {
            'index_name': group[0],
            'index_value': value_match.group(1) if value_match else '',
            'index_unit': value_match.group(2).strip() if value_match else '',
            'reference_value': group[2].replace('参考值：', '').replace('参考范围：', '') if len(group)>=3 else ''
        }

    def _save_to_database(self, data):
        conn = None
        try:
            conn = self.db_manager.get_connection()
            with conn.cursor() as cursor:
                # 插入主表
                cursor.execute(
                    "INSERT INTO medical_check (user_id, medical_date) VALUES (%s, %s)",
                    ('1', data['medical_date'])
                )
                medical_id = cursor.lastrowid

                # 插入明细
                for item in data['lab_items']:
                    self._insert_detail(cursor, medical_id, item)
                
                conn.commit()
                logging.info(f"Successfully saved {len(data['lab_items'])} lab items")
        except pymysql.Error as e:
            logging.error(f"Database error: {str(e)}")
            if conn:
                conn.rollback()
        finally:
            if conn:
                conn.close()

    def _insert_detail(self, cursor, medical_id, item):
        try:
            cursor.execute(
                """INSERT INTO medical_check_detail 
                (medical_id, index_name, index_value, index_unit, reference_value)
                VALUES (%s, %s, %s, %s, %s)""",
                (
                    medical_id,
                    item['index_name'],
                    item['index_value'],
                    item['index_unit'],
                    item['reference_value']
                )
            )
        except pymysql.Error as e:
            logging.error(f"Failed to insert lab item: {str(e)}")

# 初始化配置和使用
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'database': 'medical'
}

if __name__ == "__main__":
    try:
        db_manager = DatabaseManager(DB_CONFIG)
        processor = ReportProcessor(db_manager)

        # 创建processed目录（如果不存在）
        processed_dir = os.path.join(os.path.dirname(__file__), 'processed')
        os.makedirs(processed_dir, exist_ok=True)

        # 处理source目录下所有jpg/png文件
        source_dir = os.path.join(os.path.dirname(__file__), 'source')
        supported_ext = ('.jpg', '.jpeg', '.png')
        
        for filename in os.listdir(source_dir):
            if filename.lower().endswith(supported_ext):
                src_path = os.path.join(source_dir, filename)
                # 处理文件
                processor.process_report(src_path)
                logging.info(f"Processing file: {src_path}")
            
                # 移动已处理文件
                dst_path = os.path.join(processed_dir, filename)
                shutil.move(src_path, dst_path)
                logging.info(f"文件 {filename} 已移动到processed目录")

    except Exception as e:
        logging.error(f"Critical error: {str(e)}", exc_info=True)