2025-06-29 16:55:17,139 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-06-29 16:55:17,145 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-06-29 16:55:17,147 - DEBUG - interactive is False
2025-06-29 16:55:17,147 - DEBUG - platform is win32
2025-06-29 16:55:17,200 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-06-29 16:55:17,203 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-06-29 16:55:17,576 - INFO - NumExpr defaulting to 12 threads.
2025-06-29 17:09:47,819 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-06-29 17:09:47,824 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-06-29 17:09:47,826 - DEBUG - interactive is False
2025-06-29 17:09:47,826 - DEBUG - platform is win32
2025-06-29 17:09:47,873 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-06-29 17:09:47,875 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-06-29 17:09:48,250 - INFO - NumExpr defaulting to 12 threads.
2025-06-29 17:12:41,473 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-06-29 17:12:41,478 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-06-29 17:12:41,480 - DEBUG - interactive is False
2025-06-29 17:12:41,480 - DEBUG - platform is win32
2025-06-29 17:12:41,527 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-06-29 17:12:41,530 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-06-29 17:12:41,906 - INFO - NumExpr defaulting to 12 threads.
2025-06-29 17:18:22,261 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-06-29 17:18:22,266 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-06-29 17:18:22,267 - DEBUG - interactive is False
2025-06-29 17:18:22,267 - DEBUG - platform is win32
2025-06-29 17:18:22,316 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-06-29 17:18:22,318 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-06-29 17:18:22,706 - INFO - NumExpr defaulting to 12 threads.
2025-06-29 17:18:24,406 - DEBUG - Loaded backend Agg version v2.2.
2025-06-29 17:18:24,407 - DEBUG - Matplotlib backend set to: Agg
2025-06-29 17:18:24,407 - INFO - Starting medical OCR processing
2025-06-29 17:18:24,413 - INFO - Processing file: D:\dev\medicalReport\ocr\source\20250521143154.jpg
2025-06-29 17:18:24,413 - INFO - Starting OCR processing for D:\dev\medicalReport\ocr\source\20250521143154.jpg
2025-06-29 17:18:24,413 - DEBUG - Using OCR config: {'text_detection_model_name': 'PP-OCRv5_server_det', 'text_recognition_model_name': 'PP-OCRv5_server_rec', 'text_rec_score_thresh': 0.6, 'use_doc_orientation_classify': False, 'use_doc_unwarping': False}
2025-06-29 17:22:08,613 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-06-29 17:22:08,618 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-06-29 17:22:08,619 - DEBUG - interactive is False
2025-06-29 17:22:08,620 - DEBUG - platform is win32
2025-06-29 17:22:08,668 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-06-29 17:22:08,670 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-06-29 17:22:09,059 - INFO - NumExpr defaulting to 12 threads.
2025-06-29 17:22:10,742 - DEBUG - Loaded backend Agg version v2.2.
2025-06-29 17:22:10,742 - DEBUG - Matplotlib backend set to: Agg
2025-06-29 17:22:10,743 - INFO - Starting medical OCR processing
2025-06-29 17:22:10,748 - INFO - Processing file: D:\dev\medicalReport\ocr\source\20250521143154.jpg
2025-06-29 17:22:10,748 - INFO - Starting OCR processing for D:\dev\medicalReport\ocr\source\20250521143154.jpg
2025-06-29 17:22:10,748 - DEBUG - Using OCR config: {'text_detection_model_name': 'PP-OCRv5_server_det', 'text_recognition_model_name': 'PP-OCRv5_server_rec', 'text_rec_score_thresh': 0.6, 'use_doc_orientation_classify': False, 'use_doc_unwarping': False}
2025-06-29 17:25:49,143 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-06-29 17:25:49,148 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-06-29 17:25:49,149 - DEBUG - interactive is False
2025-06-29 17:25:49,149 - DEBUG - platform is win32
2025-06-29 17:25:49,197 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-06-29 17:25:49,199 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-06-29 17:25:49,568 - INFO - NumExpr defaulting to 12 threads.
2025-06-29 17:25:51,253 - DEBUG - Report processor module loaded
2025-06-29 17:25:51,253 - DEBUG - Loaded backend Agg version v2.2.
2025-06-29 17:25:51,254 - DEBUG - Matplotlib backend set to: Agg
2025-06-29 17:25:51,254 - INFO - Starting medical OCR processing
2025-06-29 17:25:51,259 - INFO - Processing file: D:\dev\medicalReport\ocr\source\20250521143154.jpg
2025-06-29 17:25:51,259 - INFO - Starting OCR processing for D:\dev\medicalReport\ocr\source\20250521143154.jpg
2025-06-29 17:25:51,260 - DEBUG - Using OCR config: {'text_detection_model_name': 'PP-OCRv5_server_det', 'text_recognition_model_name': 'PP-OCRv5_server_rec', 'text_rec_score_thresh': 0.6, 'use_doc_orientation_classify': False, 'use_doc_unwarping': False}
2025-06-29 17:27:48,931 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-06-29 17:27:48,936 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-06-29 17:27:48,937 - DEBUG - interactive is False
2025-06-29 17:27:48,937 - DEBUG - platform is win32
2025-06-29 17:27:48,988 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-06-29 17:27:48,991 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-06-29 17:27:49,383 - INFO - NumExpr defaulting to 12 threads.
2025-06-29 17:27:51,131 - DEBUG - Report processor module loaded
2025-06-29 17:27:51,132 - DEBUG - Loaded backend Agg version v2.2.
2025-06-29 17:27:51,132 - DEBUG - Matplotlib backend set to: Agg
2025-06-29 17:27:51,133 - INFO - Starting medical OCR processing
2025-06-29 17:27:51,138 - INFO - Processing file: D:\dev\medicalReport\ocr\source\20250521143154.jpg
2025-06-29 17:27:51,138 - INFO - Starting OCR processing for D:\dev\medicalReport\ocr\source\20250521143154.jpg
2025-06-29 17:27:51,138 - DEBUG - Using OCR config: {'text_detection_model_name': 'PP-OCRv5_server_det', 'text_recognition_model_name': 'PP-OCRv5_server_rec', 'text_rec_score_thresh': 0.6, 'use_doc_orientation_classify': False, 'use_doc_unwarping': False}
2025-06-29 17:33:33,927 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-06-29 17:33:33,931 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-06-29 17:33:33,933 - DEBUG - interactive is False
2025-06-29 17:33:33,933 - DEBUG - platform is win32
2025-06-29 17:33:33,981 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-06-29 17:33:33,983 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-06-29 17:33:34,392 - INFO - NumExpr defaulting to 12 threads.
2025-06-29 17:33:36,167 - DEBUG - Report processor module loaded
2025-06-29 17:33:36,168 - DEBUG - Loaded backend Agg version v2.2.
2025-06-29 17:33:36,168 - DEBUG - Matplotlib backend set to: Agg
2025-06-29 17:33:36,168 - INFO - Starting medical OCR processing
2025-06-29 17:33:36,174 - INFO - Processing file: D:\dev\medicalReport\ocr\source\20250521143154.jpg
2025-06-29 17:33:36,174 - INFO - Starting OCR processing for D:\dev\medicalReport\ocr\source\20250521143154.jpg
2025-06-29 17:33:36,174 - DEBUG - Using OCR config: {'text_detection_model_name': 'PP-OCRv5_server_det', 'text_recognition_model_name': 'PP-OCRv5_server_rec', 'text_rec_score_thresh': 0.6, 'use_doc_orientation_classify': False, 'use_doc_unwarping': False, 'use_gpu': False}
2025-06-29 17:33:36,175 - ERROR - Error in process_report: Unknown argument: use_gpu
Traceback (most recent call last):
  File "D:\dev\medicalReport\ocr\medical_ocr\ocr\report_processor.py", line 33, in process_report
    ocr = PaddleOCR(**self.ocr_config)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\anaconda3\Lib\site-packages\paddleocr\_pipelines\ocr.py", line 161, in __init__
    super().__init__(**base_params)
  File "D:\anaconda3\Lib\site-packages\paddleocr\_pipelines\base.py", line 59, in __init__
    self._common_args = parse_common_args(
                        ^^^^^^^^^^^^^^^^^^
  File "D:\anaconda3\Lib\site-packages\paddleocr\_common_args.py", line 43, in parse_common_args
    raise ValueError(f"Unknown argument: {name}")
ValueError: Unknown argument: use_gpu
2025-06-29 17:33:36,177 - ERROR - Error processing file 20250521143154.jpg: Unknown argument: use_gpu
Traceback (most recent call last):
  File "D:\dev\medicalReport\ocr\medical_ocr\main.py", line 29, in main
    processor.process_report(src_path)
  File "D:\dev\medicalReport\ocr\medical_ocr\ocr\report_processor.py", line 33, in process_report
    ocr = PaddleOCR(**self.ocr_config)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\anaconda3\Lib\site-packages\paddleocr\_pipelines\ocr.py", line 161, in __init__
    super().__init__(**base_params)
  File "D:\anaconda3\Lib\site-packages\paddleocr\_pipelines\base.py", line 59, in __init__
    self._common_args = parse_common_args(
                        ^^^^^^^^^^^^^^^^^^
  File "D:\anaconda3\Lib\site-packages\paddleocr\_common_args.py", line 43, in parse_common_args
    raise ValueError(f"Unknown argument: {name}")
ValueError: Unknown argument: use_gpu
2025-06-30 10:01:47,992 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-06-30 10:01:47,998 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-06-30 10:01:48,000 - DEBUG - interactive is False
2025-06-30 10:01:48,000 - DEBUG - platform is win32
2025-06-30 10:01:48,073 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-06-30 10:01:48,076 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-06-30 10:01:48,816 - INFO - NumExpr defaulting to 12 threads.
2025-06-30 10:01:52,707 - DEBUG - Report processor module loaded
2025-06-30 10:01:52,708 - DEBUG - Loaded backend Agg version v2.2.
2025-06-30 10:01:52,708 - DEBUG - Matplotlib backend set to: Agg
2025-06-30 10:01:52,708 - INFO - Starting medical OCR processing
2025-06-30 10:01:52,715 - INFO - Processing file: D:\dev\medicalReport\ocr\source\20250521143154.jpg
2025-06-30 10:01:52,715 - INFO - Starting OCR processing for D:\dev\medicalReport\ocr\source\20250521143154.jpg
2025-06-30 10:11:03,321 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-06-30 10:11:03,327 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-06-30 10:11:03,329 - DEBUG - interactive is False
2025-06-30 10:11:03,329 - DEBUG - platform is win32
2025-06-30 10:11:03,378 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-06-30 10:11:03,380 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-06-30 10:11:03,746 - INFO - NumExpr defaulting to 12 threads.
2025-06-30 10:11:05,508 - DEBUG - Report processor module loaded
2025-06-30 10:11:05,509 - DEBUG - Loaded backend Agg version v2.2.
2025-06-30 10:11:05,509 - DEBUG - Matplotlib backend set to: Agg
2025-06-30 10:11:05,509 - INFO - Starting medical OCR processing
2025-06-30 10:11:05,515 - INFO - Processing file: D:\dev\medicalReport\ocr\source\20250521143154.jpg
2025-06-30 10:11:05,515 - INFO - Starting OCR processing for D:\dev\medicalReport\ocr\source\20250521143154.jpg
2025-06-30 10:15:44,693 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-06-30 10:15:44,698 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-06-30 10:15:44,700 - DEBUG - interactive is False
2025-06-30 10:15:44,700 - DEBUG - platform is win32
2025-06-30 10:15:44,746 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-06-30 10:15:44,748 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-06-30 10:15:45,087 - INFO - NumExpr defaulting to 12 threads.
2025-06-30 10:15:46,722 - DEBUG - Report processor module loaded
2025-06-30 10:15:46,722 - DEBUG - Loaded backend Agg version v2.2.
2025-06-30 10:15:46,722 - DEBUG - Matplotlib backend set to: Agg
2025-06-30 10:15:46,723 - INFO - Starting medical OCR processing
2025-06-30 10:15:46,728 - INFO - Processing file: D:\dev\medicalReport\ocr\source\20250521143154.jpg
2025-06-30 10:15:46,728 - INFO - Starting OCR processing for D:\dev\medicalReport\ocr\source\20250521143154.jpg
2025-06-30 10:21:56,048 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-06-30 10:21:56,054 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-06-30 10:21:56,055 - DEBUG - interactive is False
2025-06-30 10:21:56,056 - DEBUG - platform is win32
2025-06-30 10:21:56,099 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-06-30 10:21:56,101 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-06-30 10:21:56,437 - INFO - NumExpr defaulting to 12 threads.
2025-06-30 10:21:58,019 - DEBUG - Report processor module loaded
2025-06-30 10:21:58,019 - DEBUG - Loaded backend Agg version v2.2.
2025-06-30 10:21:58,020 - DEBUG - Matplotlib backend set to: Agg
2025-06-30 10:21:58,020 - INFO - Starting medical OCR processing
2025-06-30 10:21:58,025 - INFO - Processing file: D:\dev\medicalReport\ocr\source\20250521143154.jpg
2025-06-30 10:21:58,026 - INFO - Starting OCR processing for D:\dev\medicalReport\ocr\source\20250521143154.jpg
2025-06-30 10:30:25,654 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-06-30 10:30:25,660 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-06-30 10:30:25,661 - DEBUG - interactive is False
2025-06-30 10:30:25,661 - DEBUG - platform is win32
2025-06-30 10:30:25,705 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-06-30 10:30:25,707 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-06-30 10:30:26,044 - INFO - NumExpr defaulting to 12 threads.
2025-06-30 10:30:27,683 - DEBUG - Report processor module loaded
2025-06-30 10:30:27,684 - DEBUG - Loaded backend Agg version v2.2.
2025-06-30 10:30:27,684 - DEBUG - Matplotlib backend set to: Agg
2025-06-30 10:30:27,684 - INFO - Starting medical OCR processing
2025-06-30 10:30:27,691 - INFO - Processing file: D:\dev\medicalReport\ocr\source\20250521143154.jpg
2025-06-30 10:30:27,691 - INFO - Starting OCR processing for D:\dev\medicalReport\ocr\source\20250521143154.jpg
2025-06-30 10:35:58,483 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-06-30 10:35:58,494 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-06-30 10:35:58,497 - DEBUG - interactive is False
2025-06-30 10:35:58,497 - DEBUG - platform is win32
2025-06-30 10:35:58,572 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-06-30 10:35:58,578 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-06-30 10:35:59,259 - INFO - NumExpr defaulting to 12 threads.
2025-06-30 10:36:02,775 - DEBUG - Report processor module loaded
2025-06-30 10:36:02,775 - DEBUG - Loaded backend Agg version v2.2.
2025-06-30 10:36:02,775 - DEBUG - Matplotlib backend set to: Agg
2025-06-30 10:36:02,776 - INFO - Starting medical OCR processing
2025-06-30 10:36:02,785 - INFO - Processing file: D:\dev\medicalReport\ocr\source\20250521143154.jpg
2025-06-30 10:36:02,785 - INFO - Starting OCR processing for D:\dev\medicalReport\ocr\source\20250521143154.jpg
2025-06-30 11:06:07,044 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-06-30 11:06:07,050 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-06-30 11:06:07,051 - DEBUG - interactive is False
2025-06-30 11:06:07,051 - DEBUG - platform is win32
2025-06-30 11:06:07,097 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-06-30 11:06:07,099 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-06-30 11:06:07,463 - INFO - NumExpr defaulting to 12 threads.
2025-06-30 11:06:09,191 - DEBUG - Report processor module loaded
2025-06-30 11:06:09,191 - DEBUG - Loaded backend Agg version v2.2.
2025-06-30 11:06:09,191 - DEBUG - Matplotlib backend set to: Agg
2025-06-30 11:06:09,197 - INFO - Processing file: D:\dev\medicalReport\ocr\source\20250521143154.jpg
2025-06-30 11:06:09,198 - INFO - 当前路径开始OCR操作： D:\dev\medicalReport\ocr\source\20250521143154.jpg
2025-06-30 11:12:16,590 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-06-30 11:12:16,595 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-06-30 11:12:16,597 - DEBUG - interactive is False
2025-06-30 11:12:16,597 - DEBUG - platform is win32
2025-06-30 11:12:16,641 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-06-30 11:12:16,643 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-06-30 11:12:16,980 - INFO - NumExpr defaulting to 12 threads.
2025-06-30 11:12:18,615 - DEBUG - Report processor module loaded
2025-06-30 11:12:18,615 - DEBUG - Loaded backend Agg version v2.2.
2025-06-30 11:12:18,616 - DEBUG - Matplotlib backend set to: Agg
2025-06-30 11:12:18,622 - INFO - Processing file: D:\dev\medicalReport\ocr\source\20250415161347.jpg
2025-06-30 11:12:18,622 - INFO - 当前路径开始OCR操作： D:\dev\medicalReport\ocr\source\20250415161347.jpg
2025-06-30 11:39:35,782 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-06-30 11:39:35,787 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-06-30 11:39:35,788 - DEBUG - interactive is False
2025-06-30 11:39:35,789 - DEBUG - platform is win32
2025-06-30 11:39:35,833 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-06-30 11:39:35,835 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-06-30 11:39:36,182 - INFO - NumExpr defaulting to 12 threads.
2025-06-30 11:39:37,848 - DEBUG - Report processor module loaded
2025-06-30 11:39:37,848 - DEBUG - Loaded backend Agg version v2.2.
2025-06-30 11:39:37,849 - DEBUG - Matplotlib backend set to: Agg
2025-06-30 11:39:37,855 - INFO - 当前路径开始OCR操作： D:\dev\medicalReport\ocr\source\20250620083741.jpg
2025-06-30 13:32:43,928 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-06-30 13:32:43,935 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-06-30 13:32:43,937 - DEBUG - interactive is False
2025-06-30 13:32:43,937 - DEBUG - platform is win32
2025-06-30 13:32:43,982 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-06-30 13:32:43,984 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-06-30 13:32:44,352 - INFO - NumExpr defaulting to 12 threads.
2025-06-30 13:32:46,053 - DEBUG - Report processor module loaded
2025-06-30 13:32:46,053 - DEBUG - Loaded backend Agg version v2.2.
2025-06-30 13:32:46,053 - DEBUG - Matplotlib backend set to: Agg
2025-06-30 13:32:46,059 - INFO - 当前路径开始OCR操作： D:\dev\medicalReport\ocr\source\20250415161331.jpg
2025-06-30 13:42:49,127 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-06-30 13:42:49,133 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-06-30 13:42:49,134 - DEBUG - interactive is False
2025-06-30 13:42:49,134 - DEBUG - platform is win32
2025-06-30 13:42:49,179 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-06-30 13:42:49,182 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-06-30 13:42:49,552 - INFO - NumExpr defaulting to 12 threads.
2025-06-30 13:42:51,258 - DEBUG - Report processor module loaded
2025-06-30 13:42:51,259 - DEBUG - Loaded backend Agg version v2.2.
2025-06-30 13:42:51,259 - DEBUG - Matplotlib backend set to: Agg
2025-06-30 13:42:51,265 - INFO - 当前路径开始OCR操作： D:\dev\medicalReport\ocr\source\20250415161331.jpg
2025-06-30 13:51:03,697 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-06-30 13:51:03,707 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-06-30 13:51:03,709 - DEBUG - interactive is False
2025-06-30 13:51:03,710 - DEBUG - platform is win32
2025-06-30 13:51:03,773 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-06-30 13:51:03,776 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-06-30 13:51:04,269 - INFO - NumExpr defaulting to 12 threads.
2025-06-30 13:51:06,176 - DEBUG - Report processor module loaded
2025-06-30 13:51:06,176 - DEBUG - Loaded backend Agg version v2.2.
2025-06-30 13:51:06,176 - DEBUG - Matplotlib backend set to: Agg
2025-06-30 13:51:06,182 - INFO - 当前路径开始OCR操作： D:\dev\medicalReport\ocr\source\20250415161331.jpg
2025-06-30 13:52:59,809 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-06-30 13:52:59,814 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-06-30 13:52:59,816 - DEBUG - interactive is False
2025-06-30 13:52:59,816 - DEBUG - platform is win32
2025-06-30 13:52:59,859 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-06-30 13:52:59,861 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-06-30 13:53:00,205 - INFO - NumExpr defaulting to 12 threads.
2025-06-30 13:53:01,866 - DEBUG - Report processor module loaded
2025-06-30 13:53:01,867 - DEBUG - Loaded backend Agg version v2.2.
2025-06-30 13:53:01,867 - DEBUG - Matplotlib backend set to: Agg
2025-06-30 13:53:01,873 - INFO - 当前路径开始OCR操作： D:\dev\medicalReport\ocr\source\20250521143200.jpg
2025-06-30 13:57:23,113 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-06-30 13:57:23,120 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-06-30 13:57:23,121 - DEBUG - interactive is False
2025-06-30 13:57:23,121 - DEBUG - platform is win32
2025-06-30 13:57:23,165 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-06-30 13:57:23,167 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-06-30 13:57:23,514 - INFO - NumExpr defaulting to 12 threads.
2025-06-30 13:57:25,139 - DEBUG - Report processor module loaded
2025-06-30 13:57:25,140 - DEBUG - Loaded backend Agg version v2.2.
2025-06-30 13:57:25,140 - DEBUG - Matplotlib backend set to: Agg
2025-06-30 13:57:25,146 - INFO - 当前路径开始OCR操作： D:\dev\medicalReport\ocr\source\20250618091353.jpg
2025-07-01 15:38:29,855 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-07-01 15:38:29,860 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-07-01 15:38:29,862 - DEBUG - interactive is False
2025-07-01 15:38:29,862 - DEBUG - platform is win32
2025-07-01 15:38:29,916 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-07-01 15:38:29,919 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-07-01 15:38:30,381 - INFO - NumExpr defaulting to 12 threads.
2025-07-01 15:38:32,421 - DEBUG - Report processor module loaded
2025-07-01 15:38:32,422 - DEBUG - Loaded backend Agg version v2.2.
2025-07-01 15:38:32,422 - DEBUG - Matplotlib backend set to: Agg
2025-07-01 15:39:44,301 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-07-01 15:39:44,307 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-07-01 15:39:44,309 - DEBUG - interactive is False
2025-07-01 15:39:44,309 - DEBUG - platform is win32
2025-07-01 15:39:44,356 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-07-01 15:39:44,358 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-07-01 15:39:44,716 - INFO - NumExpr defaulting to 12 threads.
2025-07-01 15:39:46,567 - DEBUG - OCR识别的完整文本: 检验日期：2024-12-01
检查方法：CT平扫+增强扫描
放射性诊断：肺部未见明显异常，心脏大小正常
2025-07-01 15:39:46,567 - DEBUG - 提取到检查日期: 2024-12-01
2025-07-01 15:39:46,568 - DEBUG - 提取到检查方法: CT平扫+增强扫描
2025-07-01 15:39:46,568 - DEBUG - 提取到放射性诊断: 肺部未见明显异常，心脏大小正常
2025-07-01 15:39:46,578 - INFO - 成功保存检查报告数据，ID: 1
2025-07-01 15:44:52,052 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-07-01 15:44:52,061 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-07-01 15:44:52,063 - DEBUG - interactive is False
2025-07-01 15:44:52,063 - DEBUG - platform is win32
2025-07-01 15:44:52,113 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-07-01 15:44:52,115 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-07-01 15:44:52,482 - INFO - NumExpr defaulting to 12 threads.
2025-07-01 15:44:54,271 - DEBUG - Report processor module loaded
2025-07-01 15:44:54,271 - DEBUG - Loaded backend Agg version v2.2.
2025-07-01 15:44:54,271 - DEBUG - Matplotlib backend set to: Agg
2025-07-01 15:44:58,466 - INFO - 开始处理检查报告： D:\dev\medicalReport\ocr\source2\20250424084510.jpg
2025-07-01 15:45:15,028 - WARNING - 未找到检查日期
2025-07-01 15:45:15,028 - WARNING - 未找到放射性诊断信息
2025-07-01 16:05:12,420 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-07-01 16:05:12,426 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-07-01 16:05:12,427 - DEBUG - interactive is False
2025-07-01 16:05:12,428 - DEBUG - platform is win32
2025-07-01 16:05:12,475 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-07-01 16:05:12,478 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-07-01 16:05:12,847 - INFO - NumExpr defaulting to 12 threads.
2025-07-01 16:05:14,713 - DEBUG - OCR识别的完整文本: 报告日期：2025/4/2216:24:00
检查方法：CT平扫+增强扫描
胸部CT检查
造影剂使用情况：使用碘对比剂
放射性诊断：
肺部未见明显异常
心脏大小正常
纵隔结构清晰
查看影像
2025-07-01 16:05:14,714 - DEBUG - 提取到检查日期: 2025-04-22 (原文: 报告日期：2025/4/2216:24:00)
2025-07-01 16:05:14,714 - DEBUG - 提取到检查方法: CT平扫+增强扫描
胸部CT检查
造影剂使用情况：使用碘对比剂
2025-07-01 16:05:14,714 - DEBUG - 提取到放射性诊断: 肺部未见明显异常
心脏大小正常
纵隔结构清晰
2025-07-01 16:05:14,728 - INFO - 成功保存检查报告数据，ID: 3
2025-07-01 16:09:12,745 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-07-01 16:09:12,751 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-07-01 16:09:12,753 - DEBUG - interactive is False
2025-07-01 16:09:12,753 - DEBUG - platform is win32
2025-07-01 16:09:12,804 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-07-01 16:09:12,806 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-07-01 16:09:13,194 - INFO - NumExpr defaulting to 12 threads.
2025-07-01 16:09:15,072 - DEBUG - OCR识别的完整文本: 报告日期：2025/4/2216:24:00
检查方法：CT平扫+增强扫描
胸部CT检查
造影剂使用情况：使用碘对比剂
放射性诊断：
肺部未见明显异常
心脏大小正常
纵隔结构清晰
查看影像
2025-07-01 16:09:15,072 - DEBUG - 提取到检查日期: 2025-04-22 (原文: 报告日期：2025/4/2216:24:00)
2025-07-01 16:09:15,072 - DEBUG - 提取到检查方法: CT平扫+增强扫描
胸部CT检查
造影剂使用情况：使用碘对比剂
2025-07-01 16:09:15,072 - DEBUG - 提取到放射性诊断: 肺部未见明显异常
心脏大小正常
纵隔结构清晰
2025-07-01 16:09:15,082 - INFO - 成功保存检查报告数据，ID: 6
2025-07-01 16:11:44,106 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-07-01 16:11:44,112 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-07-01 16:11:44,114 - DEBUG - interactive is False
2025-07-01 16:11:44,114 - DEBUG - platform is win32
2025-07-01 16:11:44,160 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-07-01 16:11:44,162 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-07-01 16:11:44,542 - INFO - NumExpr defaulting to 12 threads.
2025-07-01 16:11:46,339 - DEBUG - Report processor module loaded
2025-07-01 16:11:46,339 - DEBUG - Loaded backend Agg version v2.2.
2025-07-01 16:11:46,339 - DEBUG - Matplotlib backend set to: Agg
2025-07-01 16:11:48,410 - INFO - 开始处理检查报告： D:\dev\medicalReport\ocr\source2\20250424084510.jpg
2025-07-01 16:12:05,591 - WARNING - 未找到检查方法信息
2025-07-01 16:12:05,592 - WARNING - 未找到放射性诊断信息
2025-07-01 16:15:53,250 - DEBUG - matplotlib data path: D:\anaconda3\Lib\site-packages\matplotlib\mpl-data
2025-07-01 16:15:53,256 - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-07-01 16:15:53,258 - DEBUG - interactive is False
2025-07-01 16:15:53,258 - DEBUG - platform is win32
2025-07-01 16:15:53,306 - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-07-01 16:15:53,308 - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-07-01 16:15:53,670 - INFO - NumExpr defaulting to 12 threads.
2025-07-01 16:15:55,493 - DEBUG - Report processor module loaded
2025-07-01 16:15:55,493 - DEBUG - Loaded backend Agg version v2.2.
2025-07-01 16:15:55,494 - DEBUG - Matplotlib backend set to: Agg
2025-07-01 16:15:57,878 - INFO - 开始处理检查报告： D:\dev\medicalReport\ocr\source2\20250424084510.jpg
2025-07-01 16:16:15,458 - WARNING - 未找到检查方法信息
2025-07-01 16:16:15,459 - WARNING - 未找到放射性诊断信息
