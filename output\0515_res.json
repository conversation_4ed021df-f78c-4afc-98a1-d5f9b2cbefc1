{"input_path": "D:\\dev\\medicalReport\\ocr\\source\\0515.jpg", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": true, "use_doc_unwarping": true}, "angle": 90}, "dt_polys": [[[227, 11], [452, 16], [451, 58], [226, 53]], [[1424, 12], [1689, 8], [1690, 53], [1425, 57]], [[556, 65], [1248, 63], [1248, 124], [556, 126]], [[1312, 85], [1723, 88], [1723, 133], [1311, 130]], [[24, 128], [283, 134], [281, 182], [23, 176]], [[493, 149], [795, 143], [796, 187], [494, 193]], [[855, 144], [1261, 149], [1260, 194], [855, 189]], [[1300, 145], [1549, 147], [1548, 189], [1299, 187]], [[27, 186], [206, 186], [206, 232], [27, 232]], [[405, 196], [820, 199], [820, 246], [404, 243]], [[1296, 196], [1735, 194], [1735, 235], [1297, 237]], [[26, 232], [295, 234], [295, 289], [26, 287]], [[406, 248], [527, 248], [527, 292], [406, 292]], [[84, 297], [238, 299], [238, 345], [83, 343]], [[370, 302], [453, 302], [453, 347], [370, 347]], [[559, 303], [638, 303], [638, 345], [559, 345]], [[680, 303], [826, 303], [826, 345], [680, 345]], [[937, 303], [1083, 303], [1083, 346], [937, 346]], [[1205, 300], [1286, 300], [1286, 345], [1205, 345]], [[1394, 300], [1478, 300], [1478, 345], [1394, 345]], [[1516, 302], [1667, 302], [1667, 345], [1516, 345]], [[41, 360], [62, 360], [62, 388], [41, 388]], [[86, 357], [243, 357], [243, 392], [86, 392]], [[370, 360], [425, 360], [425, 395], [370, 395]], [[559, 360], [653, 360], [653, 393], [559, 393]], [[712, 362], [833, 362], [833, 393], [712, 393]], [[899, 360], [1088, 360], [1088, 395], [899, 395]], [[1205, 359], [1272, 359], [1272, 392], [1205, 392]], [[1349, 361], [1369, 361], [1369, 388], [1349, 388]], [[1396, 360], [1509, 360], [1509, 392], [1396, 392]], [[1544, 361], [1704, 361], [1704, 392], [1544, 392]], [[38, 404], [63, 404], [63, 435], [38, 435]], [[85, 402], [282, 404], [282, 439], [85, 437]], [[369, 407], [438, 407], [438, 440], [369, 440]], [[556, 407], [580, 407], [580, 438], [556, 438]], [[699, 408], [846, 408], [846, 436], [699, 436]], [[896, 406], [1057, 404], [1057, 439], [896, 441]], [[1207, 407], [1258, 407], [1258, 438], [1207, 438]], [[1351, 408], [1367, 408], [1367, 435], [1351, 435]], [[1392, 405], [1450, 405], [1450, 443], [1392, 443]], [[1561, 407], [1689, 407], [1689, 438], [1561, 438]], [[37, 448], [62, 448], [62, 478], [37, 478]], [[82, 446], [251, 448], [251, 483], [82, 481]], [[368, 453], [435, 453], [435, 486], [368, 486]], [[554, 452], [580, 452], [580, 484], [554, 484]], [[698, 452], [846, 452], [846, 483], [698, 483]], [[895, 449], [1088, 451], [1088, 486], [895, 484]], [[1206, 453], [1274, 453], [1274, 486], [1206, 486]], [[1349, 452], [1369, 452], [1369, 483], [1349, 483]], [[1391, 452], [1418, 452], [1418, 483], [1391, 483]], [[1546, 453], [1703, 453], [1703, 481], [1546, 481]], [[37, 495], [62, 495], [62, 524], [37, 524]], [[82, 492], [250, 494], [250, 529], [82, 527]], [[367, 499], [418, 499], [418, 530], [367, 530]], [[554, 499], [579, 499], [579, 530], [554, 530]], [[709, 498], [845, 498], [845, 529], [709, 529]], [[894, 498], [1145, 498], [1145, 532], [894, 532]], [[1205, 500], [1272, 500], [1272, 530], [1205, 530]], [[1391, 498], [1429, 498], [1429, 529], [1391, 529]], [[1545, 498], [1718, 498], [1718, 526], [1545, 526]], [[36, 540], [61, 540], [61, 570], [36, 570]], [[80, 538], [306, 538], [306, 572], [80, 572]], [[367, 543], [418, 543], [418, 574], [367, 574]], [[552, 544], [577, 544], [577, 575], [552, 575]], [[708, 545], [800, 545], [800, 574], [708, 574]], [[893, 545], [1143, 545], [1143, 575], [893, 575]], [[1202, 543], [1270, 543], [1270, 576], [1202, 576]], [[1344, 541], [1368, 541], [1368, 574], [1344, 574]], [[1388, 546], [1431, 546], [1431, 579], [1388, 579]], [[1544, 542], [1701, 542], [1701, 570], [1544, 570]], [[36, 586], [61, 586], [61, 617], [36, 617]], [[82, 586], [305, 586], [305, 617], [82, 617]], [[366, 588], [417, 588], [417, 619], [366, 619]], [[552, 588], [576, 588], [576, 620], [552, 620]], [[706, 589], [826, 589], [826, 621], [706, 621]], [[891, 589], [1170, 589], [1170, 620], [891, 620]], [[1199, 589], [1252, 589], [1252, 620], [1199, 620]], [[1383, 586], [1442, 583], [1444, 621], [1385, 624]], [[1555, 586], [1684, 586], [1684, 614], [1555, 614]], [[37, 633], [59, 633], [59, 661], [37, 661]], [[81, 630], [236, 630], [236, 664], [81, 664]], [[365, 632], [417, 632], [417, 662], [365, 662]], [[553, 632], [646, 632], [646, 664], [553, 664]], [[705, 634], [823, 634], [823, 665], [705, 665]], [[887, 634], [1137, 634], [1137, 665], [887, 665]], [[1200, 633], [1264, 633], [1264, 663], [1200, 663]], [[1384, 632], [1410, 632], [1410, 663], [1384, 663]], [[1539, 631], [1698, 629], [1698, 660], [1539, 662]], [[36, 678], [62, 678], [62, 707], [36, 707]], [[80, 675], [207, 675], [207, 707], [80, 707]], [[366, 676], [418, 676], [418, 707], [366, 707]], [[552, 677], [644, 677], [644, 709], [552, 709]], [[703, 678], [822, 678], [822, 709], [703, 709]], [[885, 677], [1074, 677], [1074, 708], [885, 708]], [[1197, 677], [1248, 677], [1248, 708], [1197, 708]], [[1383, 677], [1483, 675], [1484, 708], [1384, 710]], [[1551, 677], [1680, 677], [1680, 708], [1551, 708]], [[32, 716], [210, 716], [210, 755], [32, 755]], [[366, 721], [419, 721], [419, 751], [366, 751]], [[553, 722], [643, 722], [643, 751], [553, 751]], [[703, 724], [819, 724], [819, 749], [703, 749]], [[881, 720], [1133, 720], [1133, 751], [881, 751]], [[1194, 721], [1246, 721], [1246, 752], [1194, 752]], [[1381, 722], [1417, 722], [1417, 753], [1381, 753]], [[1547, 721], [1691, 723], [1690, 754], [1547, 752]], [[39, 763], [265, 763], [265, 794], [39, 794]], [[367, 765], [432, 765], [432, 794], [367, 794]], [[552, 766], [642, 766], [642, 795], [552, 795]], [[687, 766], [832, 766], [832, 794], [687, 794]], [[880, 764], [1072, 764], [1072, 798], [880, 798]], [[1192, 764], [1259, 764], [1259, 797], [1192, 797]], [[1378, 764], [1405, 764], [1405, 795], [1378, 795]], [[1530, 765], [1703, 767], [1703, 797], [1530, 795]], [[36, 806], [265, 805], [265, 837], [36, 838]], [[367, 808], [433, 808], [433, 838], [367, 838]], [[551, 808], [643, 808], [643, 840], [551, 840]], [[687, 810], [833, 810], [833, 838], [687, 838]], [[881, 809], [1131, 809], [1131, 840], [881, 840]], [[1191, 810], [1229, 810], [1229, 840], [1191, 840]], [[1375, 809], [1403, 809], [1403, 840], [1375, 840]], [[1557, 808], [1656, 810], [1655, 843], [1556, 841]], [[36, 1101], [281, 1101], [281, 1139], [36, 1139]], [[659, 1102], [1021, 1104], [1021, 1145], [659, 1143]], [[1215, 1101], [1580, 1099], [1580, 1146], [1216, 1148]], [[38, 1144], [494, 1144], [494, 1178], [38, 1178]], [[660, 1142], [1110, 1142], [1110, 1179], [660, 1179]], [[1216, 1141], [1687, 1145], [1687, 1186], [1215, 1182]], [[34, 1187], [941, 1187], [941, 1224], [34, 1224]], [[1352, 1180], [1513, 1180], [1513, 1215], [1352, 1215]]], "text_det_params": {"limit_side_len": 64, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.0, "rec_texts": ["血常规（五）", "2025年05月15日", "浙江省立同德医院检验报告单", "样本号：20250515CAL152", "姓名：李春香", "病案号：13510804", "就诊卡号：JJ619684433108", "样本类型：血液", "性别：女", "科室：肿瘤科一病房一", "临床诊断：胰腺恶性肿瘤个", "年龄：73岁", "备注：", "检验项目", "结果", "单位", "参考范围", "检验项目", "结果", "单位", "参考范围", "1", "白细胞计数", "5.0", "10E9/L", "3.5-9.5", "12红细胞计数", "3.70", "↓", "10E12/L", "3.80-5.10", "2", "中性粒细胞（%）", "63.4", "%", "40.0-75.0", "13血红蛋白", "100", "1", "g/L", "115-150", "3", "淋巴细胞（%）", "30.1", "%", "20.0-50.0", "14红细胞压积", "31.2", "←", "%", "35.0-45.0", "4", "单核细胞（%）", "5.1", "%", "3.0-10.0", "15平均红细胞体积", "84.4", "fl", "82.0-100.0", "5", "嗜酸性粒细胞（%）", "1.3", "%", "0.4-8", "16平均血红蛋白量", "26.9", "↑", "pg", "27.0-34.0", "6", "嗜碱性粒细胞（%）", "0.1", "%", "0.0-1.0", "17平均血红蛋白浓度", "319", "g/L", "316-354", "7", "中性粒细胞", "3.2", "10E9/L", "1.8-6.3", "18红细胞分布宽度", "13.0", "%", "11.5-14.5", "8", "淋巴细胞", "1.5", "10E9/L", "1.1-3.2", "19血小板计数", "160", "10E9/L", "125-350", "9 单核细胞", "0.3", "10E9/L", "0.1-0.6", "20平均血小板体积", "9.4", "fl", "7.4-12.5", "10嗜酸性粒细胞", "0.06", "10E9/L", "0.02-0.52", "21血小板压积", "0.15", "%", "0.05-0.282", "11嗜碱性粒细胞", "0.00", "10E9/L", "0.00-0.06", "22血小板分布宽度", "16", "%", "10-20", "送检医生：陈华", "检验者：", "审核者：叶海南", "采集时间：2025.05.1509:50", "接收时间：2025.05.1509:53", "报告时间：2025.05.1510:05", "本报告仅对所检测的标本负责！如有疑问请一周内与我科联系.", "打印次数1"], "rec_scores": [0.9940366744995117, 0.999826967716217, 0.9993991255760193, 0.9975874423980713, 0.9708071351051331, 0.9852030873298645, 0.955788254737854, 0.9887943267822266, 0.9719518423080444, 0.9888653755187988, 0.9883691668510437, 0.981077253818512, 0.9946057796478271, 0.9998005628585815, 0.9998326301574707, 0.999929666519165, 0.999652624130249, 0.9997981190681458, 0.9997767210006714, 0.999854326248169, 0.999596118927002, 0.9983925223350525, 0.9992653727531433, 0.9991562366485596, 0.9915134310722351, 0.9980770945549011, 0.9993301033973694, 0.999595582485199, 0.8590295910835266, 0.9914204478263855, 0.9976059794425964, 0.9998520612716675, 0.9325820207595825, 0.9993664026260376, 0.9990027546882629, 0.9973148703575134, 0.999316394329071, 0.9993683695793152, 0.504444420337677, 0.9663980603218079, 0.9994065165519714, 0.9998263716697693, 0.9134927988052368, 0.999383270740509, 0.9985968470573425, 0.9985913038253784, 0.9996029734611511, 0.9997493028640747, 0.1997968703508377, 0.9985382556915283, 0.9990344643592834, 0.9988368153572083, 0.9426565766334534, 0.9982821345329285, 0.9990164041519165, 0.9981542229652405, 0.9991124272346497, 0.9995304346084595, 0.8314658999443054, 0.9986951947212219, 0.9994655251502991, 0.9182723164558411, 0.9991733431816101, 0.9995704293251038, 0.9978982210159302, 0.9988278150558472, 0.9998128414154053, 0.9007178544998169, 0.7402627468109131, 0.9981237053871155, 0.999675989151001, 0.9326403737068176, 0.9988500475883484, 0.9991093277931213, 0.9978960156440735, 0.9983313679695129, 0.9996569752693176, 0.9656664729118347, 0.9994354844093323, 0.9992005228996277, 0.9992998838424683, 0.9992632865905762, 0.9925792217254639, 0.9989956021308899, 0.9993840456008911, 0.9995822906494141, 0.999462902545929, 0.998704195022583, 0.9531538486480713, 0.9973829984664917, 0.9988039135932922, 0.9929156303405762, 0.9990266561508179, 0.9992272257804871, 0.999629020690918, 0.9926998615264893, 0.9988629221916199, 0.9326713681221008, 0.9992707371711731, 0.9953038096427917, 0.9972618818283081, 0.9997096657752991, 0.9994279742240906, 0.8502671718597412, 0.9981566667556763, 0.9989566802978516, 0.9995448589324951, 0.9937283396720886, 0.998946487903595, 0.9993054270744324, 0.9994759559631348, 0.999210000038147, 0.9987594485282898, 0.9985418915748596, 0.9995080232620239, 0.9889781475067139, 0.9984681606292725, 0.999523401260376, 0.9997270107269287, 0.9990243911743164, 0.9988983273506165, 0.9862497448921204, 0.9849188923835754, 0.9492712616920471, 0.9776557087898254, 0.9738048315048218, 0.9816716313362122, 0.9887810349464417, 0.998416543006897], "rec_polys": [[[227, 11], [452, 16], [451, 58], [226, 53]], [[1424, 12], [1689, 8], [1690, 53], [1425, 57]], [[556, 65], [1248, 63], [1248, 124], [556, 126]], [[1312, 85], [1723, 88], [1723, 133], [1311, 130]], [[24, 128], [283, 134], [281, 182], [23, 176]], [[493, 149], [795, 143], [796, 187], [494, 193]], [[855, 144], [1261, 149], [1260, 194], [855, 189]], [[1300, 145], [1549, 147], [1548, 189], [1299, 187]], [[27, 186], [206, 186], [206, 232], [27, 232]], [[405, 196], [820, 199], [820, 246], [404, 243]], [[1296, 196], [1735, 194], [1735, 235], [1297, 237]], [[26, 232], [295, 234], [295, 289], [26, 287]], [[406, 248], [527, 248], [527, 292], [406, 292]], [[84, 297], [238, 299], [238, 345], [83, 343]], [[370, 302], [453, 302], [453, 347], [370, 347]], [[559, 303], [638, 303], [638, 345], [559, 345]], [[680, 303], [826, 303], [826, 345], [680, 345]], [[937, 303], [1083, 303], [1083, 346], [937, 346]], [[1205, 300], [1286, 300], [1286, 345], [1205, 345]], [[1394, 300], [1478, 300], [1478, 345], [1394, 345]], [[1516, 302], [1667, 302], [1667, 345], [1516, 345]], [[41, 360], [62, 360], [62, 388], [41, 388]], [[86, 357], [243, 357], [243, 392], [86, 392]], [[370, 360], [425, 360], [425, 395], [370, 395]], [[559, 360], [653, 360], [653, 393], [559, 393]], [[712, 362], [833, 362], [833, 393], [712, 393]], [[899, 360], [1088, 360], [1088, 395], [899, 395]], [[1205, 359], [1272, 359], [1272, 392], [1205, 392]], [[1349, 361], [1369, 361], [1369, 388], [1349, 388]], [[1396, 360], [1509, 360], [1509, 392], [1396, 392]], [[1544, 361], [1704, 361], [1704, 392], [1544, 392]], [[38, 404], [63, 404], [63, 435], [38, 435]], [[85, 402], [282, 404], [282, 439], [85, 437]], [[369, 407], [438, 407], [438, 440], [369, 440]], [[556, 407], [580, 407], [580, 438], [556, 438]], [[699, 408], [846, 408], [846, 436], [699, 436]], [[896, 406], [1057, 404], [1057, 439], [896, 441]], [[1207, 407], [1258, 407], [1258, 438], [1207, 438]], [[1351, 408], [1367, 408], [1367, 435], [1351, 435]], [[1392, 405], [1450, 405], [1450, 443], [1392, 443]], [[1561, 407], [1689, 407], [1689, 438], [1561, 438]], [[37, 448], [62, 448], [62, 478], [37, 478]], [[82, 446], [251, 448], [251, 483], [82, 481]], [[368, 453], [435, 453], [435, 486], [368, 486]], [[554, 452], [580, 452], [580, 484], [554, 484]], [[698, 452], [846, 452], [846, 483], [698, 483]], [[895, 449], [1088, 451], [1088, 486], [895, 484]], [[1206, 453], [1274, 453], [1274, 486], [1206, 486]], [[1349, 452], [1369, 452], [1369, 483], [1349, 483]], [[1391, 452], [1418, 452], [1418, 483], [1391, 483]], [[1546, 453], [1703, 453], [1703, 481], [1546, 481]], [[37, 495], [62, 495], [62, 524], [37, 524]], [[82, 492], [250, 494], [250, 529], [82, 527]], [[367, 499], [418, 499], [418, 530], [367, 530]], [[554, 499], [579, 499], [579, 530], [554, 530]], [[709, 498], [845, 498], [845, 529], [709, 529]], [[894, 498], [1145, 498], [1145, 532], [894, 532]], [[1205, 500], [1272, 500], [1272, 530], [1205, 530]], [[1391, 498], [1429, 498], [1429, 529], [1391, 529]], [[1545, 498], [1718, 498], [1718, 526], [1545, 526]], [[36, 540], [61, 540], [61, 570], [36, 570]], [[80, 538], [306, 538], [306, 572], [80, 572]], [[367, 543], [418, 543], [418, 574], [367, 574]], [[552, 544], [577, 544], [577, 575], [552, 575]], [[708, 545], [800, 545], [800, 574], [708, 574]], [[893, 545], [1143, 545], [1143, 575], [893, 575]], [[1202, 543], [1270, 543], [1270, 576], [1202, 576]], [[1344, 541], [1368, 541], [1368, 574], [1344, 574]], [[1388, 546], [1431, 546], [1431, 579], [1388, 579]], [[1544, 542], [1701, 542], [1701, 570], [1544, 570]], [[36, 586], [61, 586], [61, 617], [36, 617]], [[82, 586], [305, 586], [305, 617], [82, 617]], [[366, 588], [417, 588], [417, 619], [366, 619]], [[552, 588], [576, 588], [576, 620], [552, 620]], [[706, 589], [826, 589], [826, 621], [706, 621]], [[891, 589], [1170, 589], [1170, 620], [891, 620]], [[1199, 589], [1252, 589], [1252, 620], [1199, 620]], [[1383, 586], [1442, 583], [1444, 621], [1385, 624]], [[1555, 586], [1684, 586], [1684, 614], [1555, 614]], [[37, 633], [59, 633], [59, 661], [37, 661]], [[81, 630], [236, 630], [236, 664], [81, 664]], [[365, 632], [417, 632], [417, 662], [365, 662]], [[553, 632], [646, 632], [646, 664], [553, 664]], [[705, 634], [823, 634], [823, 665], [705, 665]], [[887, 634], [1137, 634], [1137, 665], [887, 665]], [[1200, 633], [1264, 633], [1264, 663], [1200, 663]], [[1384, 632], [1410, 632], [1410, 663], [1384, 663]], [[1539, 631], [1698, 629], [1698, 660], [1539, 662]], [[36, 678], [62, 678], [62, 707], [36, 707]], [[80, 675], [207, 675], [207, 707], [80, 707]], [[366, 676], [418, 676], [418, 707], [366, 707]], [[552, 677], [644, 677], [644, 709], [552, 709]], [[703, 678], [822, 678], [822, 709], [703, 709]], [[885, 677], [1074, 677], [1074, 708], [885, 708]], [[1197, 677], [1248, 677], [1248, 708], [1197, 708]], [[1383, 677], [1483, 675], [1484, 708], [1384, 710]], [[1551, 677], [1680, 677], [1680, 708], [1551, 708]], [[32, 716], [210, 716], [210, 755], [32, 755]], [[366, 721], [419, 721], [419, 751], [366, 751]], [[553, 722], [643, 722], [643, 751], [553, 751]], [[703, 724], [819, 724], [819, 749], [703, 749]], [[881, 720], [1133, 720], [1133, 751], [881, 751]], [[1194, 721], [1246, 721], [1246, 752], [1194, 752]], [[1381, 722], [1417, 722], [1417, 753], [1381, 753]], [[1547, 721], [1691, 723], [1690, 754], [1547, 752]], [[39, 763], [265, 763], [265, 794], [39, 794]], [[367, 765], [432, 765], [432, 794], [367, 794]], [[552, 766], [642, 766], [642, 795], [552, 795]], [[687, 766], [832, 766], [832, 794], [687, 794]], [[880, 764], [1072, 764], [1072, 798], [880, 798]], [[1192, 764], [1259, 764], [1259, 797], [1192, 797]], [[1378, 764], [1405, 764], [1405, 795], [1378, 795]], [[1530, 765], [1703, 767], [1703, 797], [1530, 795]], [[36, 806], [265, 805], [265, 837], [36, 838]], [[367, 808], [433, 808], [433, 838], [367, 838]], [[551, 808], [643, 808], [643, 840], [551, 840]], [[687, 810], [833, 810], [833, 838], [687, 838]], [[881, 809], [1131, 809], [1131, 840], [881, 840]], [[1191, 810], [1229, 810], [1229, 840], [1191, 840]], [[1375, 809], [1403, 809], [1403, 840], [1375, 840]], [[1557, 808], [1656, 810], [1655, 843], [1556, 841]], [[36, 1101], [281, 1101], [281, 1139], [36, 1139]], [[659, 1102], [1021, 1104], [1021, 1145], [659, 1143]], [[1215, 1101], [1580, 1099], [1580, 1146], [1216, 1148]], [[38, 1144], [494, 1144], [494, 1178], [38, 1178]], [[660, 1142], [1110, 1142], [1110, 1179], [660, 1179]], [[1216, 1141], [1687, 1145], [1687, 1186], [1215, 1182]], [[34, 1187], [941, 1187], [941, 1224], [34, 1224]], [[1352, 1180], [1513, 1180], [1513, 1215], [1352, 1215]]], "rec_boxes": [[226, 11, 452, 58], [1424, 8, 1690, 57], [556, 63, 1248, 126], [1311, 85, 1723, 133], [23, 128, 283, 182], [493, 143, 796, 193], [855, 144, 1261, 194], [1299, 145, 1549, 189], [27, 186, 206, 232], [404, 196, 820, 246], [1296, 194, 1735, 237], [26, 232, 295, 289], [406, 248, 527, 292], [83, 297, 238, 345], [370, 302, 453, 347], [559, 303, 638, 345], [680, 303, 826, 345], [937, 303, 1083, 346], [1205, 300, 1286, 345], [1394, 300, 1478, 345], [1516, 302, 1667, 345], [41, 360, 62, 388], [86, 357, 243, 392], [370, 360, 425, 395], [559, 360, 653, 393], [712, 362, 833, 393], [899, 360, 1088, 395], [1205, 359, 1272, 392], [1349, 361, 1369, 388], [1396, 360, 1509, 392], [1544, 361, 1704, 392], [38, 404, 63, 435], [85, 402, 282, 439], [369, 407, 438, 440], [556, 407, 580, 438], [699, 408, 846, 436], [896, 404, 1057, 441], [1207, 407, 1258, 438], [1351, 408, 1367, 435], [1392, 405, 1450, 443], [1561, 407, 1689, 438], [37, 448, 62, 478], [82, 446, 251, 483], [368, 453, 435, 486], [554, 452, 580, 484], [698, 452, 846, 483], [895, 449, 1088, 486], [1206, 453, 1274, 486], [1349, 452, 1369, 483], [1391, 452, 1418, 483], [1546, 453, 1703, 481], [37, 495, 62, 524], [82, 492, 250, 529], [367, 499, 418, 530], [554, 499, 579, 530], [709, 498, 845, 529], [894, 498, 1145, 532], [1205, 500, 1272, 530], [1391, 498, 1429, 529], [1545, 498, 1718, 526], [36, 540, 61, 570], [80, 538, 306, 572], [367, 543, 418, 574], [552, 544, 577, 575], [708, 545, 800, 574], [893, 545, 1143, 575], [1202, 543, 1270, 576], [1344, 541, 1368, 574], [1388, 546, 1431, 579], [1544, 542, 1701, 570], [36, 586, 61, 617], [82, 586, 305, 617], [366, 588, 417, 619], [552, 588, 576, 620], [706, 589, 826, 621], [891, 589, 1170, 620], [1199, 589, 1252, 620], [1383, 583, 1444, 624], [1555, 586, 1684, 614], [37, 633, 59, 661], [81, 630, 236, 664], [365, 632, 417, 662], [553, 632, 646, 664], [705, 634, 823, 665], [887, 634, 1137, 665], [1200, 633, 1264, 663], [1384, 632, 1410, 663], [1539, 629, 1698, 662], [36, 678, 62, 707], [80, 675, 207, 707], [366, 676, 418, 707], [552, 677, 644, 709], [703, 678, 822, 709], [885, 677, 1074, 708], [1197, 677, 1248, 708], [1383, 675, 1484, 710], [1551, 677, 1680, 708], [32, 716, 210, 755], [366, 721, 419, 751], [553, 722, 643, 751], [703, 724, 819, 749], [881, 720, 1133, 751], [1194, 721, 1246, 752], [1381, 722, 1417, 753], [1547, 721, 1691, 754], [39, 763, 265, 794], [367, 765, 432, 794], [552, 766, 642, 795], [687, 766, 832, 794], [880, 764, 1072, 798], [1192, 764, 1259, 797], [1378, 764, 1405, 795], [1530, 765, 1703, 797], [36, 805, 265, 838], [367, 808, 433, 838], [551, 808, 643, 840], [687, 810, 833, 838], [881, 809, 1131, 840], [1191, 810, 1229, 840], [1375, 809, 1403, 840], [1556, 808, 1656, 843], [36, 1101, 281, 1139], [659, 1102, 1021, 1145], [1215, 1099, 1580, 1148], [38, 1144, 494, 1178], [660, 1142, 1110, 1179], [1215, 1141, 1687, 1186], [34, 1187, 941, 1224], [1352, 1180, 1513, 1215]]}