from paddleocr import PaddleOCR
import pymysql
from datetime import datetime
import re

# ocr = PaddleOCR(
#     use_doc_orientation_classify=False, # 通过 use_doc_orientation_classify 参数指定不使用文档方向分类模型
#     use_doc_unwarping=False, # 通过 use_doc_unwarping 参数指定不使用文本图像矫正模型
#     use_textline_orientation=False, # 通过 use_textline_orientation 参数指定不使用文本行方向分类模型
# )
# ocr = PaddleOCR(lang="en") # 通过 lang 参数来使用英文模型
# ocr = PaddleOCR(ocr_version="PP-OCRv4") # 通过 ocr_version 参数来使用 PP-OCR 其他版本
# ocr = PaddleOCR(device="gpu") # 通过 device 参数使得在模型推理时使用 GPU
# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'database': 'medical',
    'charset': 'utf8mb4'
}

ocr = PaddleOCR(
    text_detection_model_name="PP-OCRv5_server_det",
    text_recognition_model_name="PP-OCRv5_server_rec",
    use_doc_orientation_classify=False,
    use_doc_unwarping=True,
    use_textline_orientation=False,
) # 更换 PP-OCRv5_server 模型
result = ocr.predict("./20250521143205.jpg")

# 获取并处理检测结果
for res in result:
    res.save_to_json("output")
    
    # 连接数据库
    conn = pymysql.connect(**DB_CONFIG)
    try:
        with conn.cursor() as cursor:
            # 提取报告日期
            rec_texts = res.to_dict()['rec_texts']
            date_str = next((s.split('：')[1][:10] for s in rec_texts if '报告日期：' in s), None)
            
            # 插入主表
            cursor.execute(
                "INSERT INTO medical_check (medical_date) VALUES (%s)",
                (datetime.strptime(date_str, '%Y-%m-%d').date(),)
            )
            medical_id = cursor.lastrowid

            # 插入明细表
            # 修改后的数据处理逻辑
            start_index = rec_texts.index('结果/单位') if '结果/单位' in rec_texts else -1
            if start_index != -1:
                i = start_index + 1
                while i < len(rec_texts):
                    # 过滤无效值
                    if not rec_texts[i] or '↓' in rec_texts[i] or '↑' in rec_texts[i]:
                        i += 1
                        continue
                    
                    # 动态获取有效数据组（3-4个元素为一组）
                    valid_group = []
                    for offset in range(4):
                        if i+offset >= len(rec_texts):
                            break
                        if rec_texts[i+offset] and not any(c in rec_texts[i+offset] for c in ['↓','↑']):
                            valid_group.append(rec_texts[i+offset])
                        else:
                            break
                    
                    # 插入有效数据（至少包含3个有效元素）
                    if len(valid_group) >= 3:
                        raw_value = valid_group[1]
                        index_value, index_unit = parse_value_unit(raw_value)
                        
                        cursor.execute(
                            "INSERT INTO medical_check_detail (medical_id, index_name, index_value, index_unit, reference_value) "
                            "VALUES (%s, %s, %s, %s, %s)",
                            (
                                medical_id,
                                valid_group[0],
                                index_value,
                                index_unit,
                                valid_group[2].replace('参考值：', '') if len(valid_group)>=3 else ''
                            )
                        )
                        
                        i += len(valid_group)
                    else:
                        i += 1
            
            conn.commit()
    except Exception as e:
        conn.rollback()
        print(f"Database error: {str(e)}")
    finally:
        conn.close()


def parse_value_unit(raw_value):
    # 新正则表达式：只匹配纯数字和小数点，遇到非数字立即停止
    match = re.match(r'^(\d+\.?\d*)(.*)', raw_value)
    if match:
        return match.group(1).strip(), match.group(2).strip()
    return raw_value, ''