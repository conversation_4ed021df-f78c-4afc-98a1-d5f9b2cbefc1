#!/usr/bin/env python3
"""
快速检查数据库
"""

import os
import sys
import pymysql

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from medical_ocr.config.config import DB_CONFIG

def quick_check():
    try:
        conn = pymysql.connect(
            host=DB_CONFIG['host'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database=DB_CONFIG['database'],
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        
        with conn.cursor() as cursor:
            # 检查medical_exam表
            cursor.execute("SELECT COUNT(*) as count FROM medical_exam")
            result = cursor.fetchone()
            print(f"medical_exam表记录数: {result['count']}")
            
            if result['count'] > 0:
                cursor.execute("SELECT * FROM medical_exam ORDER BY exam_id DESC LIMIT 3")
                rows = cursor.fetchall()
                for row in rows:
                    print(f"ID: {row['exam_id']}, 日期: {row['medical_date']}, 检查方法: {row['exam_info'][:50]}...")
        
        conn.close()
        print("检查完成")
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    quick_check()
