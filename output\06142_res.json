{"input_path": "D:\\dev\\medicalReport\\ocr\\source\\06142.jpg", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[203, 20], [446, 20], [446, 54], [203, 54]], [[477, 60], [600, 60], [600, 81], [477, 81]], [[46, 93], [159, 93], [159, 117], [46, 117]], [[320, 91], [400, 91], [400, 119], [320, 119]], [[502, 92], [600, 92], [600, 117], [502, 117]], [[48, 121], [192, 121], [192, 142], [48, 142]], [[321, 121], [554, 121], [554, 142], [321, 142]], [[46, 147], [175, 145], [176, 169], [46, 172]], [[320, 145], [412, 145], [412, 170], [320, 170]], [[46, 174], [383, 174], [383, 198], [46, 198]], [[54, 204], [101, 204], [101, 230], [54, 230]], [[262, 203], [309, 203], [309, 230], [262, 230]], [[339, 203], [405, 203], [405, 229], [339, 229]], [[53, 233], [148, 233], [148, 257], [53, 257]], [[258, 233], [302, 233], [302, 255], [258, 255]], [[321, 234], [510, 234], [510, 255], [321, 255]], [[55, 261], [183, 261], [183, 282], [55, 282]], [[258, 260], [301, 260], [301, 283], [258, 283]], [[326, 267], [338, 267], [338, 276], [326, 276]], [[334, 261], [509, 261], [509, 282], [334, 282]], [[54, 288], [165, 288], [165, 309], [54, 309]], [[257, 287], [302, 287], [302, 309], [257, 309]], [[337, 288], [510, 288], [510, 309], [337, 309]], [[54, 315], [183, 315], [183, 336], [54, 336]], [[257, 314], [302, 314], [302, 336], [257, 336]], [[337, 315], [510, 315], [510, 336], [337, 336]], [[55, 342], [200, 342], [200, 363], [55, 363]], [[257, 341], [302, 341], [302, 363], [257, 363]], [[337, 342], [510, 342], [510, 363], [337, 363]], [[55, 369], [200, 369], [200, 390], [55, 390]], [[257, 368], [301, 368], [301, 390], [257, 390]], [[337, 369], [509, 369], [509, 390], [337, 390]], [[55, 396], [201, 396], [201, 417], [55, 417]], [[257, 395], [313, 395], [313, 416], [257, 416]], [[326, 396], [336, 403], [329, 414], [319, 407]], [[334, 396], [476, 396], [476, 416], [334, 416]], [[54, 423], [184, 423], [184, 444], [54, 444]], [[257, 421], [312, 421], [312, 443], [257, 443]], [[336, 423], [476, 423], [476, 443], [336, 443]], [[54, 448], [185, 448], [185, 472], [54, 472]], [[258, 449], [312, 449], [312, 470], [258, 470]], [[323, 453], [337, 453], [337, 466], [323, 466]], [[333, 450], [465, 450], [465, 470], [333, 470]], [[55, 477], [218, 477], [218, 498], [55, 498]], [[258, 476], [302, 476], [302, 498], [258, 498]], [[336, 477], [455, 477], [455, 497], [336, 497]], [[55, 504], [218, 504], [218, 525], [55, 525]], [[258, 502], [302, 502], [302, 525], [258, 525]], [[337, 504], [454, 504], [454, 524], [337, 524]], [[53, 529], [148, 529], [148, 553], [53, 553]], [[258, 530], [301, 530], [301, 552], [258, 552]], [[336, 531], [518, 531], [518, 551], [336, 551]], [[54, 557], [129, 557], [129, 580], [54, 580]], [[259, 557], [312, 557], [312, 579], [259, 579]], [[335, 555], [512, 558], [512, 582], [334, 579]], [[53, 584], [149, 584], [149, 608], [53, 608]], [[259, 585], [313, 585], [313, 606], [259, 606]], [[334, 586], [476, 586], [476, 606], [334, 606]], [[54, 612], [183, 612], [183, 633], [54, 633]], [[258, 611], [311, 611], [311, 632], [258, 632]], [[336, 612], [485, 612], [485, 632], [336, 632]], [[54, 638], [253, 638], [253, 659], [54, 659]], [[257, 639], [312, 639], [312, 660], [257, 660]], [[336, 636], [484, 639], [483, 663], [335, 660]], [[55, 666], [254, 666], [254, 686], [55, 686]], [[257, 666], [323, 666], [323, 687], [257, 687]], [[336, 664], [512, 666], [512, 690], [335, 688]], [[54, 694], [237, 694], [237, 714], [54, 714]], [[258, 693], [312, 693], [312, 714], [258, 714]], [[338, 695], [474, 695], [474, 712], [338, 712]], [[54, 720], [254, 720], [254, 741], [54, 741]], [[259, 721], [311, 721], [311, 739], [259, 739]], [[337, 720], [476, 720], [476, 740], [337, 740]], [[54, 747], [147, 747], [147, 768], [54, 768]], [[258, 746], [298, 746], [298, 768], [258, 768]], [[337, 747], [502, 747], [502, 768], [337, 768]], [[55, 774], [183, 774], [183, 795], [55, 795]], [[259, 771], [312, 773], [311, 795], [258, 793]], [[337, 774], [456, 774], [456, 794], [337, 794]], [[54, 801], [182, 801], [182, 822], [54, 822]], [[258, 800], [302, 800], [302, 822], [258, 822]], [[336, 801], [463, 801], [463, 821], [336, 821]], [[54, 828], [166, 828], [166, 849], [54, 849]], [[259, 827], [312, 827], [312, 848], [259, 848]], [[338, 828], [476, 828], [476, 848], [338, 848]], [[53, 853], [148, 853], [148, 877], [53, 877]], [[258, 854], [301, 854], [301, 876], [258, 876]], [[337, 855], [455, 855], [455, 875], [337, 875]], [[53, 880], [161, 880], [161, 904], [53, 904]], [[259, 881], [315, 881], [315, 903], [259, 903]], [[317, 881], [459, 881], [459, 905], [317, 905]], [[53, 909], [141, 909], [141, 930], [53, 930]], [[259, 907], [448, 907], [448, 931], [259, 931]], [[47, 938], [309, 936], [309, 958], [47, 959]], [[48, 966], [309, 966], [309, 986], [48, 986]], [[416, 989], [601, 991], [600, 1015], [416, 1013]], [[46, 1017], [382, 1019], [382, 1043], [46, 1041]]], "text_det_params": {"limit_side_len": 64, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.6, "rec_texts": ["上海进康肿瘤医院", "住院样本号：42", "姓名：李春香", "性别：女", "年龄：73岁", "卡号：12520236", "检验科室：进康检验科床号：", "报告医生：黄珍", "样本种类：", "采样日期：2025-06-1409:37:54备注：", "项目", "结果", "参考值", "白细胞计数", "2.81", "↓3.50~9.50*10^9/L", "中性粒细胞计数", "1.01", "1.80~6.30*10^9/L", "淋巴细胞计数", "1.27", "1.10~3.20*10^9/L", "单核细胞绝对值", "0.42", "0.10~0.60*10^9/L", "嗜酸性粒细胞计数", "0.10", "0.02~0.52*10^9/L", "嗜碱性粒细胞计数", "0.01", "0.00~0.06*10^9/L", "中性粒细胞百分比", "35.90", "40.00~75.00%", "淋巴细胞百分比", "45.20", "20.00~50.00%", "单核细胞百分比", "15.00", "↑", "3.00~10.00%", "嗜酸性粒细胞百分比", "3.40", "0.40~8.00%", "嗜碱性粒细胞百分比", "0.50", "0.00~1.00%", "红细胞计数", "3.25", "3.80~5.10*10^12/L", "血红蛋白", "91.00", "115.00~150.00g/L", "红细胞压积", "27.30", "35.00~45.00%", "平均红细胞体积", "84.10", "82.00~100.00fL", "平均红细胞血红蛋白含量", "27.90", "27.00~34.00pg", "平均红细胞血红蛋白浓度", "332.00", "316.00~354.00g/L", "红细胞分布宽度标准差", "43.00", "36.40~46.30fL", "红细胞分布宽度变异系数", "14.00", "10.00~16.00%", "血小板计数", "144", "125~350*10^9/L", "血小板分布宽度", "15.70", "15.00~17.00", "平均血小板体积", "9.00", "6.50~13.00fL", "大血小板比率", "19.60", "10.00~50.00%", "血小板压积", "0.13", "0.11~0.27%", "淀粉样蛋白A", "20.70", "↑<10.00mg/L", "C反应蛋白", "11.95 ↑<5.00mg/L", "收样日期：2025-06-1409:48:52", "报告日期:2025-06-1410:33:18", "检验：王昱程核对：黄珍", "**代表已复做项目，以上结果仅供临床参考."], "rec_scores": [0.9995325803756714, 0.9940627217292786, 0.9966251254081726, 0.9871982336044312, 0.9973022937774658, 0.962392270565033, 0.9939512610435486, 0.995692789554596, 0.9977219700813293, 0.9742657542228699, 0.9999446272850037, 0.999934732913971, 0.9998583197593689, 0.9994556307792664, 0.999821126461029, 0.9839321374893188, 0.9996166825294495, 0.9997311234474182, 0.9848452806472778, 0.998878538608551, 0.9995829463005066, 0.9687449932098389, 0.9998263120651245, 0.9997864961624146, 0.9666988849639893, 0.998882532119751, 0.99972003698349, 0.9867009520530701, 0.9988689422607422, 0.9997328519821167, 0.9675143361091614, 0.9997795820236206, 0.9998044967651367, 0.989598274230957, 0.9993566274642944, 0.9995397329330444, 0.9927459359169006, 0.9657996296882629, 0.9596346020698547, 0.9301072955131531, 0.9902926087379456, 0.9996254444122314, 0.9997183084487915, 0.9944366216659546, 0.9994350075721741, 0.9995330572128296, 0.9910095930099487, 0.9996665716171265, 0.9995770454406738, 0.9695058465003967, 0.9997848272323608, 0.9997249841690063, 0.9784116744995117, 0.9999133944511414, 0.9994593858718872, 0.9916645884513855, 0.9997601509094238, 0.9993354678153992, 0.9414964318275452, 0.9990293979644775, 0.999485194683075, 0.995320737361908, 0.9991599917411804, 0.9995190501213074, 0.9853954315185547, 0.9997078776359558, 0.9995979070663452, 0.9631348252296448, 0.9994666576385498, 0.9996364712715149, 0.9941975474357605, 0.9995092153549194, 0.9997014999389648, 0.9798975586891174, 0.9992146492004395, 0.9993379712104797, 0.9958522319793701, 0.9996044039726257, 0.9997500777244568, 0.9647049307823181, 0.9993560910224915, 0.9996334314346313, 0.9968171119689941, 0.999653697013855, 0.9995335340499878, 0.9936726689338684, 0.9987146854400635, 0.9998334050178528, 0.9679480195045471, 0.996258556842804, 0.9632374048233032, 0.9757194519042969, 0.9657993316650391, 0.930011510848999, 0.9887251257896423], "rec_polys": [[[203, 20], [446, 20], [446, 54], [203, 54]], [[477, 60], [600, 60], [600, 81], [477, 81]], [[46, 93], [159, 93], [159, 117], [46, 117]], [[320, 91], [400, 91], [400, 119], [320, 119]], [[502, 92], [600, 92], [600, 117], [502, 117]], [[48, 121], [192, 121], [192, 142], [48, 142]], [[321, 121], [554, 121], [554, 142], [321, 142]], [[46, 147], [175, 145], [176, 169], [46, 172]], [[320, 145], [412, 145], [412, 170], [320, 170]], [[46, 174], [383, 174], [383, 198], [46, 198]], [[54, 204], [101, 204], [101, 230], [54, 230]], [[262, 203], [309, 203], [309, 230], [262, 230]], [[339, 203], [405, 203], [405, 229], [339, 229]], [[53, 233], [148, 233], [148, 257], [53, 257]], [[258, 233], [302, 233], [302, 255], [258, 255]], [[321, 234], [510, 234], [510, 255], [321, 255]], [[55, 261], [183, 261], [183, 282], [55, 282]], [[258, 260], [301, 260], [301, 283], [258, 283]], [[334, 261], [509, 261], [509, 282], [334, 282]], [[54, 288], [165, 288], [165, 309], [54, 309]], [[257, 287], [302, 287], [302, 309], [257, 309]], [[337, 288], [510, 288], [510, 309], [337, 309]], [[54, 315], [183, 315], [183, 336], [54, 336]], [[257, 314], [302, 314], [302, 336], [257, 336]], [[337, 315], [510, 315], [510, 336], [337, 336]], [[55, 342], [200, 342], [200, 363], [55, 363]], [[257, 341], [302, 341], [302, 363], [257, 363]], [[337, 342], [510, 342], [510, 363], [337, 363]], [[55, 369], [200, 369], [200, 390], [55, 390]], [[257, 368], [301, 368], [301, 390], [257, 390]], [[337, 369], [509, 369], [509, 390], [337, 390]], [[55, 396], [201, 396], [201, 417], [55, 417]], [[257, 395], [313, 395], [313, 416], [257, 416]], [[334, 396], [476, 396], [476, 416], [334, 416]], [[54, 423], [184, 423], [184, 444], [54, 444]], [[257, 421], [312, 421], [312, 443], [257, 443]], [[336, 423], [476, 423], [476, 443], [336, 443]], [[54, 448], [185, 448], [185, 472], [54, 472]], [[258, 449], [312, 449], [312, 470], [258, 470]], [[323, 453], [337, 453], [337, 466], [323, 466]], [[333, 450], [465, 450], [465, 470], [333, 470]], [[55, 477], [218, 477], [218, 498], [55, 498]], [[258, 476], [302, 476], [302, 498], [258, 498]], [[336, 477], [455, 477], [455, 497], [336, 497]], [[55, 504], [218, 504], [218, 525], [55, 525]], [[258, 502], [302, 502], [302, 525], [258, 525]], [[337, 504], [454, 504], [454, 524], [337, 524]], [[53, 529], [148, 529], [148, 553], [53, 553]], [[258, 530], [301, 530], [301, 552], [258, 552]], [[336, 531], [518, 531], [518, 551], [336, 551]], [[54, 557], [129, 557], [129, 580], [54, 580]], [[259, 557], [312, 557], [312, 579], [259, 579]], [[335, 555], [512, 558], [512, 582], [334, 579]], [[53, 584], [149, 584], [149, 608], [53, 608]], [[259, 585], [313, 585], [313, 606], [259, 606]], [[334, 586], [476, 586], [476, 606], [334, 606]], [[54, 612], [183, 612], [183, 633], [54, 633]], [[258, 611], [311, 611], [311, 632], [258, 632]], [[336, 612], [485, 612], [485, 632], [336, 632]], [[54, 638], [253, 638], [253, 659], [54, 659]], [[257, 639], [312, 639], [312, 660], [257, 660]], [[336, 636], [484, 639], [483, 663], [335, 660]], [[55, 666], [254, 666], [254, 686], [55, 686]], [[257, 666], [323, 666], [323, 687], [257, 687]], [[336, 664], [512, 666], [512, 690], [335, 688]], [[54, 694], [237, 694], [237, 714], [54, 714]], [[258, 693], [312, 693], [312, 714], [258, 714]], [[338, 695], [474, 695], [474, 712], [338, 712]], [[54, 720], [254, 720], [254, 741], [54, 741]], [[259, 721], [311, 721], [311, 739], [259, 739]], [[337, 720], [476, 720], [476, 740], [337, 740]], [[54, 747], [147, 747], [147, 768], [54, 768]], [[258, 746], [298, 746], [298, 768], [258, 768]], [[337, 747], [502, 747], [502, 768], [337, 768]], [[55, 774], [183, 774], [183, 795], [55, 795]], [[259, 771], [312, 773], [311, 795], [258, 793]], [[337, 774], [456, 774], [456, 794], [337, 794]], [[54, 801], [182, 801], [182, 822], [54, 822]], [[258, 800], [302, 800], [302, 822], [258, 822]], [[336, 801], [463, 801], [463, 821], [336, 821]], [[54, 828], [166, 828], [166, 849], [54, 849]], [[259, 827], [312, 827], [312, 848], [259, 848]], [[338, 828], [476, 828], [476, 848], [338, 848]], [[53, 853], [148, 853], [148, 877], [53, 877]], [[258, 854], [301, 854], [301, 876], [258, 876]], [[337, 855], [455, 855], [455, 875], [337, 875]], [[53, 880], [161, 880], [161, 904], [53, 904]], [[259, 881], [315, 881], [315, 903], [259, 903]], [[317, 881], [459, 881], [459, 905], [317, 905]], [[53, 909], [141, 909], [141, 930], [53, 930]], [[259, 907], [448, 907], [448, 931], [259, 931]], [[47, 938], [309, 936], [309, 958], [47, 959]], [[48, 966], [309, 966], [309, 986], [48, 986]], [[416, 989], [601, 991], [600, 1015], [416, 1013]], [[46, 1017], [382, 1019], [382, 1043], [46, 1041]]], "rec_boxes": [[203, 20, 446, 54], [477, 60, 600, 81], [46, 93, 159, 117], [320, 91, 400, 119], [502, 92, 600, 117], [48, 121, 192, 142], [321, 121, 554, 142], [46, 145, 176, 172], [320, 145, 412, 170], [46, 174, 383, 198], [54, 204, 101, 230], [262, 203, 309, 230], [339, 203, 405, 229], [53, 233, 148, 257], [258, 233, 302, 255], [321, 234, 510, 255], [55, 261, 183, 282], [258, 260, 301, 283], [334, 261, 509, 282], [54, 288, 165, 309], [257, 287, 302, 309], [337, 288, 510, 309], [54, 315, 183, 336], [257, 314, 302, 336], [337, 315, 510, 336], [55, 342, 200, 363], [257, 341, 302, 363], [337, 342, 510, 363], [55, 369, 200, 390], [257, 368, 301, 390], [337, 369, 509, 390], [55, 396, 201, 417], [257, 395, 313, 416], [334, 396, 476, 416], [54, 423, 184, 444], [257, 421, 312, 443], [336, 423, 476, 443], [54, 448, 185, 472], [258, 449, 312, 470], [323, 453, 337, 466], [333, 450, 465, 470], [55, 477, 218, 498], [258, 476, 302, 498], [336, 477, 455, 497], [55, 504, 218, 525], [258, 502, 302, 525], [337, 504, 454, 524], [53, 529, 148, 553], [258, 530, 301, 552], [336, 531, 518, 551], [54, 557, 129, 580], [259, 557, 312, 579], [334, 555, 512, 582], [53, 584, 149, 608], [259, 585, 313, 606], [334, 586, 476, 606], [54, 612, 183, 633], [258, 611, 311, 632], [336, 612, 485, 632], [54, 638, 253, 659], [257, 639, 312, 660], [335, 636, 484, 663], [55, 666, 254, 686], [257, 666, 323, 687], [335, 664, 512, 690], [54, 694, 237, 714], [258, 693, 312, 714], [338, 695, 474, 712], [54, 720, 254, 741], [259, 721, 311, 739], [337, 720, 476, 740], [54, 747, 147, 768], [258, 746, 298, 768], [337, 747, 502, 768], [55, 774, 183, 795], [258, 771, 312, 795], [337, 774, 456, 794], [54, 801, 182, 822], [258, 800, 302, 822], [336, 801, 463, 821], [54, 828, 166, 849], [259, 827, 312, 848], [338, 828, 476, 848], [53, 853, 148, 877], [258, 854, 301, 876], [337, 855, 455, 875], [53, 880, 161, 904], [259, 881, 315, 903], [317, 881, 459, 905], [53, 909, 141, 930], [259, 907, 448, 931], [47, 936, 309, 959], [48, 966, 309, 986], [416, 989, 601, 1015], [46, 1017, 382, 1043]]}