{"input_path": "D:\\dev\\medicalReport\\ocr\\source\\06152.jpg", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[203, 20], [446, 20], [446, 54], [203, 54]], [[486, 57], [602, 59], [601, 84], [485, 82]], [[46, 93], [160, 93], [160, 117], [46, 117]], [[319, 91], [400, 91], [400, 119], [319, 119]], [[502, 92], [600, 92], [600, 117], [502, 117]], [[48, 121], [192, 121], [192, 142], [48, 142]], [[321, 121], [554, 121], [554, 142], [321, 142]], [[47, 148], [195, 148], [195, 169], [47, 169]], [[320, 145], [412, 145], [412, 170], [320, 170]], [[46, 173], [383, 173], [383, 197], [46, 197]], [[54, 204], [101, 204], [101, 230], [54, 230]], [[262, 203], [309, 203], [309, 230], [262, 230]], [[340, 203], [405, 203], [405, 229], [340, 229]], [[53, 232], [148, 232], [148, 257], [53, 257]], [[258, 233], [303, 233], [303, 256], [258, 256]], [[337, 234], [510, 234], [510, 255], [337, 255]], [[55, 261], [183, 261], [183, 282], [55, 282]], [[258, 260], [302, 260], [302, 282], [258, 282]], [[337, 261], [510, 261], [510, 282], [337, 282]], [[54, 288], [165, 288], [165, 309], [54, 309]], [[258, 287], [303, 287], [303, 309], [258, 309]], [[337, 288], [510, 288], [510, 309], [337, 309]], [[54, 315], [183, 315], [183, 336], [54, 336]], [[257, 314], [302, 314], [302, 336], [257, 336]], [[336, 314], [509, 314], [509, 335], [336, 335]], [[54, 342], [200, 342], [200, 363], [54, 363]], [[257, 340], [302, 340], [302, 363], [257, 363]], [[336, 342], [510, 342], [510, 363], [336, 363]], [[54, 369], [200, 369], [200, 390], [54, 390]], [[257, 368], [302, 368], [302, 390], [257, 390]], [[337, 369], [509, 369], [509, 390], [337, 390]], [[55, 396], [201, 396], [201, 417], [55, 417]], [[257, 395], [312, 395], [312, 416], [257, 416]], [[336, 396], [476, 396], [476, 416], [336, 416]], [[53, 421], [184, 421], [184, 445], [53, 445]], [[257, 421], [312, 421], [312, 443], [257, 443]], [[336, 423], [476, 423], [476, 443], [336, 443]], [[54, 448], [185, 448], [185, 472], [54, 472]], [[258, 449], [312, 449], [312, 470], [258, 470]], [[320, 449], [465, 449], [465, 470], [320, 470]], [[55, 477], [218, 477], [218, 498], [55, 498]], [[258, 476], [302, 476], [302, 498], [258, 498]], [[336, 477], [455, 477], [455, 497], [336, 497]], [[55, 504], [218, 504], [218, 525], [55, 525]], [[258, 502], [302, 502], [302, 525], [258, 525]], [[337, 504], [454, 504], [454, 524], [337, 524]], [[53, 529], [148, 529], [148, 553], [53, 553]], [[258, 530], [302, 530], [302, 552], [258, 552]], [[336, 531], [518, 531], [518, 551], [336, 551]], [[54, 557], [129, 557], [129, 580], [54, 580]], [[259, 557], [312, 557], [312, 579], [259, 579]], [[335, 555], [512, 558], [512, 582], [334, 579]], [[53, 584], [148, 584], [148, 608], [53, 608]], [[259, 585], [313, 585], [313, 606], [259, 606]], [[324, 590], [333, 590], [333, 601], [324, 601]], [[333, 586], [476, 586], [476, 606], [333, 606]], [[54, 612], [183, 612], [183, 633], [54, 633]], [[258, 611], [311, 611], [311, 632], [258, 632]], [[337, 613], [486, 613], [486, 633], [337, 633]], [[54, 639], [254, 639], [254, 659], [54, 659]], [[257, 639], [312, 639], [312, 660], [257, 660]], [[336, 636], [484, 639], [483, 663], [335, 660]], [[55, 666], [254, 666], [254, 686], [55, 686]], [[257, 666], [323, 666], [323, 687], [257, 687]], [[336, 664], [512, 666], [512, 690], [335, 688]], [[54, 694], [237, 694], [237, 714], [54, 714]], [[258, 693], [313, 693], [313, 714], [258, 714]], [[337, 693], [475, 693], [475, 713], [337, 713]], [[54, 720], [254, 720], [254, 741], [54, 741]], [[258, 720], [312, 720], [312, 741], [258, 741]], [[337, 720], [476, 720], [476, 740], [337, 740]], [[54, 747], [147, 747], [147, 768], [54, 768]], [[259, 747], [296, 747], [296, 767], [259, 767]], [[337, 747], [502, 747], [502, 768], [337, 768]], [[55, 774], [183, 774], [183, 795], [55, 795]], [[259, 774], [313, 774], [313, 795], [259, 795]], [[337, 774], [456, 774], [456, 794], [337, 794]], [[55, 801], [182, 801], [182, 822], [55, 822]], [[258, 800], [302, 800], [302, 822], [258, 822]], [[337, 801], [464, 801], [464, 821], [337, 821]], [[54, 828], [166, 828], [166, 849], [54, 849]], [[259, 827], [313, 827], [313, 849], [259, 849]], [[337, 828], [476, 828], [476, 848], [337, 848]], [[54, 855], [148, 855], [148, 876], [54, 876]], [[258, 854], [303, 854], [303, 876], [258, 876]], [[336, 855], [455, 855], [455, 875], [336, 875]], [[48, 884], [308, 884], [308, 904], [48, 904]], [[47, 910], [308, 909], [308, 930], [47, 932]], [[400, 938], [599, 938], [599, 959], [400, 959]], [[47, 965], [382, 965], [382, 988], [47, 988]]], "text_det_params": {"limit_side_len": 64, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.6, "rec_texts": ["上海进康肿瘤医院", "住院样本号：2", "姓名：李春香", "性别：女", "年龄：73岁", "卡号：12520236", "检验科室：进康检验科床号：", "报告医生：龙卫清", "样本种类：", "采样日期：2025-06-1505:48:27备注：", "项目", "结果", "参考值", "白细胞计数", "5.52", "3.50~9.50*10^9/L", "中性粒细胞计数", "3.02", "1.80~6.30*10^9/L", "淋巴细胞计数", "1.74", "1.10~3.20*10^9/L", "单核细胞绝对值", "0.60", "0.10~0.60*10^9/L", "嗜酸性粒细胞计数", "0.13", "0.02~0.52*10^9/L", "嗜碱性粒细胞计数", "0.03", "0.00~0.06*10^9/L", "中性粒细胞百分比", "54.50", "40.00~75.00%", "淋巴细胞百分比", "31.60", "20.00~50.00%", "单核细胞百分比", "10.90", "↑3.00~10.00%", "嗜酸性粒细胞百分比", "2.40", "0.40~8.00%", "嗜碱性粒细胞百分比", "0.60", "0.00~1.00%", "红细胞计数", "3.08", "3.80~5.10*10^12/L", "血红蛋白", "86.00", "115.00~150.00g/L", "红细胞压积", "26.00", "35.00~45.00%", "平均红细胞体积", "84.50", "82.00~100.00fL", "平均红细胞血红蛋白含量", "28.10", "27.00~34.00pg", "平均红细胞血红蛋白浓度", "333.00", "316.00~354.00g/L", "红细胞分布宽度标准差", "44.10", "36.40~46.30fL", "红细胞分布宽度变异系数", "14.20", "10.00~16.00%", "血小板计数", "145", "125~350*10^9/L", "血小板分布宽度", "16.10", "15.00~17.00", "平均血小板体积", "9.70", "6.50~13.00fL", "大血小板比率", "23.60", "10.00~50.00%", "血小板压积", "0.14", "0.11~0.27%", "收样日期：2025-06-1507:30:58", "报告日期：2025-06-1508:03:25", "检验：王昱程核对：龙卫清", "**代表已复做项目，以上结果仅供临床参考."], "rec_scores": [0.9993495345115662, 0.9906266927719116, 0.9968684315681458, 0.9869880676269531, 0.997452974319458, 0.9963458776473999, 0.9937865138053894, 0.9956467747688293, 0.9980461001396179, 0.9863539338111877, 0.9999446272850037, 0.9999341368675232, 0.9997367858886719, 0.9993306994438171, 0.9997221231460571, 0.9679813385009766, 0.9996150732040405, 0.9998253583908081, 0.9678152799606323, 0.9989073872566223, 0.9995693564414978, 0.9684650897979736, 0.9998223185539246, 0.9997005462646484, 0.9485198259353638, 0.9989573955535889, 0.9997942447662354, 0.9831459522247314, 0.9991779923439026, 0.999712347984314, 0.9647876024246216, 0.999773383140564, 0.9994412660598755, 0.9945817589759827, 0.999198317527771, 0.999286949634552, 0.9921882748603821, 0.9998722076416016, 0.9996752738952637, 0.9822470545768738, 0.9995815753936768, 0.9996636509895325, 0.9943779110908508, 0.9994488954544067, 0.999610960483551, 0.99090176820755, 0.9996660947799683, 0.9997843503952026, 0.9627319574356079, 0.9997848272323608, 0.999811053276062, 0.9775771498680115, 0.9999016523361206, 0.9997919797897339, 0.992012083530426, 0.9997519254684448, 0.9991391897201538, 0.980404794216156, 0.9989036917686462, 0.9996522665023804, 0.994995653629303, 0.9987308382987976, 0.9995654225349426, 0.9849607348442078, 0.9996899366378784, 0.9993101954460144, 0.9908266663551331, 0.999561071395874, 0.9991631507873535, 0.994114339351654, 0.9995023608207703, 0.9995700716972351, 0.9804196953773499, 0.9992392659187317, 0.9998094439506531, 0.9962883591651917, 0.9992588758468628, 0.9996423721313477, 0.9880258440971375, 0.9993591904640198, 0.9995601773262024, 0.9938825964927673, 0.9997138977050781, 0.9996570944786072, 0.9884227514266968, 0.9544472694396973, 0.962179958820343, 0.9713232517242432, 0.9789645075798035], "rec_polys": [[[203, 20], [446, 20], [446, 54], [203, 54]], [[486, 57], [602, 59], [601, 84], [485, 82]], [[46, 93], [160, 93], [160, 117], [46, 117]], [[319, 91], [400, 91], [400, 119], [319, 119]], [[502, 92], [600, 92], [600, 117], [502, 117]], [[48, 121], [192, 121], [192, 142], [48, 142]], [[321, 121], [554, 121], [554, 142], [321, 142]], [[47, 148], [195, 148], [195, 169], [47, 169]], [[320, 145], [412, 145], [412, 170], [320, 170]], [[46, 173], [383, 173], [383, 197], [46, 197]], [[54, 204], [101, 204], [101, 230], [54, 230]], [[262, 203], [309, 203], [309, 230], [262, 230]], [[340, 203], [405, 203], [405, 229], [340, 229]], [[53, 232], [148, 232], [148, 257], [53, 257]], [[258, 233], [303, 233], [303, 256], [258, 256]], [[337, 234], [510, 234], [510, 255], [337, 255]], [[55, 261], [183, 261], [183, 282], [55, 282]], [[258, 260], [302, 260], [302, 282], [258, 282]], [[337, 261], [510, 261], [510, 282], [337, 282]], [[54, 288], [165, 288], [165, 309], [54, 309]], [[258, 287], [303, 287], [303, 309], [258, 309]], [[337, 288], [510, 288], [510, 309], [337, 309]], [[54, 315], [183, 315], [183, 336], [54, 336]], [[257, 314], [302, 314], [302, 336], [257, 336]], [[336, 314], [509, 314], [509, 335], [336, 335]], [[54, 342], [200, 342], [200, 363], [54, 363]], [[257, 340], [302, 340], [302, 363], [257, 363]], [[336, 342], [510, 342], [510, 363], [336, 363]], [[54, 369], [200, 369], [200, 390], [54, 390]], [[257, 368], [302, 368], [302, 390], [257, 390]], [[337, 369], [509, 369], [509, 390], [337, 390]], [[55, 396], [201, 396], [201, 417], [55, 417]], [[257, 395], [312, 395], [312, 416], [257, 416]], [[336, 396], [476, 396], [476, 416], [336, 416]], [[53, 421], [184, 421], [184, 445], [53, 445]], [[257, 421], [312, 421], [312, 443], [257, 443]], [[336, 423], [476, 423], [476, 443], [336, 443]], [[54, 448], [185, 448], [185, 472], [54, 472]], [[258, 449], [312, 449], [312, 470], [258, 470]], [[320, 449], [465, 449], [465, 470], [320, 470]], [[55, 477], [218, 477], [218, 498], [55, 498]], [[258, 476], [302, 476], [302, 498], [258, 498]], [[336, 477], [455, 477], [455, 497], [336, 497]], [[55, 504], [218, 504], [218, 525], [55, 525]], [[258, 502], [302, 502], [302, 525], [258, 525]], [[337, 504], [454, 504], [454, 524], [337, 524]], [[53, 529], [148, 529], [148, 553], [53, 553]], [[258, 530], [302, 530], [302, 552], [258, 552]], [[336, 531], [518, 531], [518, 551], [336, 551]], [[54, 557], [129, 557], [129, 580], [54, 580]], [[259, 557], [312, 557], [312, 579], [259, 579]], [[335, 555], [512, 558], [512, 582], [334, 579]], [[53, 584], [148, 584], [148, 608], [53, 608]], [[259, 585], [313, 585], [313, 606], [259, 606]], [[333, 586], [476, 586], [476, 606], [333, 606]], [[54, 612], [183, 612], [183, 633], [54, 633]], [[258, 611], [311, 611], [311, 632], [258, 632]], [[337, 613], [486, 613], [486, 633], [337, 633]], [[54, 639], [254, 639], [254, 659], [54, 659]], [[257, 639], [312, 639], [312, 660], [257, 660]], [[336, 636], [484, 639], [483, 663], [335, 660]], [[55, 666], [254, 666], [254, 686], [55, 686]], [[257, 666], [323, 666], [323, 687], [257, 687]], [[336, 664], [512, 666], [512, 690], [335, 688]], [[54, 694], [237, 694], [237, 714], [54, 714]], [[258, 693], [313, 693], [313, 714], [258, 714]], [[337, 693], [475, 693], [475, 713], [337, 713]], [[54, 720], [254, 720], [254, 741], [54, 741]], [[258, 720], [312, 720], [312, 741], [258, 741]], [[337, 720], [476, 720], [476, 740], [337, 740]], [[54, 747], [147, 747], [147, 768], [54, 768]], [[259, 747], [296, 747], [296, 767], [259, 767]], [[337, 747], [502, 747], [502, 768], [337, 768]], [[55, 774], [183, 774], [183, 795], [55, 795]], [[259, 774], [313, 774], [313, 795], [259, 795]], [[337, 774], [456, 774], [456, 794], [337, 794]], [[55, 801], [182, 801], [182, 822], [55, 822]], [[258, 800], [302, 800], [302, 822], [258, 822]], [[337, 801], [464, 801], [464, 821], [337, 821]], [[54, 828], [166, 828], [166, 849], [54, 849]], [[259, 827], [313, 827], [313, 849], [259, 849]], [[337, 828], [476, 828], [476, 848], [337, 848]], [[54, 855], [148, 855], [148, 876], [54, 876]], [[258, 854], [303, 854], [303, 876], [258, 876]], [[336, 855], [455, 855], [455, 875], [336, 875]], [[48, 884], [308, 884], [308, 904], [48, 904]], [[47, 910], [308, 909], [308, 930], [47, 932]], [[400, 938], [599, 938], [599, 959], [400, 959]], [[47, 965], [382, 965], [382, 988], [47, 988]]], "rec_boxes": [[203, 20, 446, 54], [485, 57, 602, 84], [46, 93, 160, 117], [319, 91, 400, 119], [502, 92, 600, 117], [48, 121, 192, 142], [321, 121, 554, 142], [47, 148, 195, 169], [320, 145, 412, 170], [46, 173, 383, 197], [54, 204, 101, 230], [262, 203, 309, 230], [340, 203, 405, 229], [53, 232, 148, 257], [258, 233, 303, 256], [337, 234, 510, 255], [55, 261, 183, 282], [258, 260, 302, 282], [337, 261, 510, 282], [54, 288, 165, 309], [258, 287, 303, 309], [337, 288, 510, 309], [54, 315, 183, 336], [257, 314, 302, 336], [336, 314, 509, 335], [54, 342, 200, 363], [257, 340, 302, 363], [336, 342, 510, 363], [54, 369, 200, 390], [257, 368, 302, 390], [337, 369, 509, 390], [55, 396, 201, 417], [257, 395, 312, 416], [336, 396, 476, 416], [53, 421, 184, 445], [257, 421, 312, 443], [336, 423, 476, 443], [54, 448, 185, 472], [258, 449, 312, 470], [320, 449, 465, 470], [55, 477, 218, 498], [258, 476, 302, 498], [336, 477, 455, 497], [55, 504, 218, 525], [258, 502, 302, 525], [337, 504, 454, 524], [53, 529, 148, 553], [258, 530, 302, 552], [336, 531, 518, 551], [54, 557, 129, 580], [259, 557, 312, 579], [334, 555, 512, 582], [53, 584, 148, 608], [259, 585, 313, 606], [333, 586, 476, 606], [54, 612, 183, 633], [258, 611, 311, 632], [337, 613, 486, 633], [54, 639, 254, 659], [257, 639, 312, 660], [335, 636, 484, 663], [55, 666, 254, 686], [257, 666, 323, 687], [335, 664, 512, 690], [54, 694, 237, 714], [258, 693, 313, 714], [337, 693, 475, 713], [54, 720, 254, 741], [258, 720, 312, 741], [337, 720, 476, 740], [54, 747, 147, 768], [259, 747, 296, 767], [337, 747, 502, 768], [55, 774, 183, 795], [259, 774, 313, 795], [337, 774, 456, 794], [55, 801, 182, 822], [258, 800, 302, 822], [337, 801, 464, 821], [54, 828, 166, 849], [259, 827, 313, 849], [337, 828, 476, 848], [54, 855, 148, 876], [258, 854, 303, 876], [336, 855, 455, 875], [48, 884, 308, 904], [47, 909, 308, 932], [400, 938, 599, 959], [47, 965, 382, 988]]}