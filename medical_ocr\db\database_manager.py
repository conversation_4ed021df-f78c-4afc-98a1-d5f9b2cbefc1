import pymysql
from ..utils.logger import logger

class DatabaseManager:
    def __init__(self, config):
        print(f"DatabaseManager initializing with config: {config}")
        self.config = config
        self._validate_config()
        self.index_cache = {}
        self._load_medical_index()

    def _load_medical_index(self):
        conn = self.get_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute("SELECT index_name, reference_min, reference_max FROM medical_index")
                count = 0
                for row in cursor:
                    self.index_cache[row['index_name']] = {
                        'min': float(row['reference_min']) if row['reference_min'] else None,
                        'max': float(row['reference_max']) if row['reference_max'] else None
                    }
                    count += 1
                print(f"Loaded {count} medical index references")
                if count == 0:
                    print("WARNING: No medical index references loaded!")
        except Exception as e:
            print(f"Error loading medical index: {str(e)}")
        finally:
            conn.close()

    def _validate_config(self):
        required_keys = ['host', 'user', 'password', 'database']
        if not all(k in self.config for k in required_keys):
            raise ValueError("Missing required database configuration keys")

    def get_connection(self):
        try:
            return pymysql.connect(
                host=self.config['host'],
                user=self.config['user'],
                password=self.config['password'],
                database=self.config['database'],
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
        except pymysql.Error as e:
            logger.error(f"Database connection failed: {str(e)}")
            raise

