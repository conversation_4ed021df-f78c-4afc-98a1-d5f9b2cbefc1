{"input_path": "D:\\dev\\medicalReport\\ocr\\source\\0605.jpg", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": true, "use_doc_unwarping": true}, "angle": 0}, "dt_polys": [[[581, 63], [926, 93], [921, 145], [576, 115]], [[74, 332], [472, 456], [443, 546], [45, 422]], [[71, 527], [870, 646], [852, 760], [53, 641]], [[72, 648], [775, 755], [761, 842], [58, 734]], [[77, 959], [342, 1009], [328, 1081], [63, 1031]], [[73, 1149], [298, 1191], [285, 1257], [60, 1215]], [[1118, 1231], [1360, 1212], [1365, 1276], [1123, 1296]], [[79, 1313], [348, 1362], [336, 1427], [67, 1377]], [[74, 1415], [565, 1481], [557, 1538], [66, 1472]], [[1085, 1426], [1363, 1413], [1366, 1478], [1088, 1490]], [[76, 1568], [338, 1608], [329, 1666], [67, 1627]], [[75, 1668], [493, 1718], [488, 1765], [69, 1716]], [[1197, 1663], [1362, 1653], [1366, 1716], [1201, 1726]], [[76, 1815], [338, 1847], [330, 1904], [69, 1872]], [[76, 1915], [422, 1949], [417, 1999], [71, 1965]], [[1226, 1907], [1362, 1897], [1367, 1959], [1231, 1969]], [[78, 2070], [382, 2096], [377, 2148], [73, 2121]], [[1196, 2155], [1362, 2141], [1367, 2202], [1201, 2216]], [[74, 2166], [417, 2192], [414, 2242], [70, 2215]], [[75, 2320], [339, 2335], [335, 2392], [72, 2378]], [[1100, 2396], [1363, 2385], [1366, 2444], [1102, 2455]], [[76, 2422], [535, 2444], [533, 2488], [74, 2466]], [[76, 2569], [337, 2586], [333, 2643], [72, 2626]], [[1083, 2637], [1365, 2626], [1368, 2689], [1085, 2700]], [[75, 2672], [546, 2692], [544, 2742], [72, 2722]], [[77, 2827], [387, 2843], [384, 2900], [74, 2884]], [[1087, 2887], [1361, 2877], [1364, 2936], [1089, 2946]], [[74, 2930], [548, 2942], [547, 2992], [73, 2980]], [[73, 3081], [336, 3090], [334, 3149], [71, 3140]], [[1034, 3138], [1361, 3123], [1364, 3180], [1037, 3195]], [[966, 3148], [1007, 3148], [1007, 3194], [966, 3194]], [[75, 3187], [557, 3194], [556, 3239], [75, 3231]], [[73, 3341], [283, 3341], [283, 3398], [73, 3398]], [[1151, 3369], [1374, 3369], [1374, 3448], [1151, 3448]], [[75, 3446], [452, 3443], [452, 3487], [75, 3491]], [[73, 3595], [334, 3595], [334, 3647], [73, 3647]], [[1125, 3632], [1176, 3632], [1176, 3684], [1125, 3684]], [[1196, 3627], [1367, 3623], [1369, 3689], [1198, 3694]], [[73, 3695], [464, 3687], [465, 3730], [74, 3738]], [[75, 3842], [382, 3836], [383, 3886], [76, 3892]], [[1218, 3874], [1371, 3869], [1373, 3935], [1220, 3940]], [[75, 3938], [453, 3930], [454, 3974], [76, 3982]], [[71, 4085], [387, 4079], [388, 4131], [72, 4137]], [[1196, 4119], [1373, 4126], [1370, 4195], [1194, 4187]], [[75, 4181], [489, 4181], [489, 4225], [75, 4225]], [[77, 4328], [386, 4328], [386, 4378], [77, 4378]], [[1164, 4359], [1374, 4359], [1374, 4431], [1164, 4431]], [[75, 4420], [467, 4424], [467, 4468], [75, 4464]], [[70, 4564], [334, 4568], [333, 4625], [69, 4621]], [[1028, 4617], [1067, 4617], [1067, 4661], [1028, 4661]], [[1099, 4610], [1367, 4610], [1367, 4667], [1099, 4667]], [[77, 4669], [572, 4669], [572, 4711], [77, 4711]], [[74, 4816], [368, 4826], [366, 4877], [73, 4867]], [[1229, 4859], [1370, 4852], [1373, 4915], [1232, 4922]], [[74, 4911], [433, 4919], [432, 4964], [73, 4956]], [[71, 5057], [334, 5065], [332, 5122], [69, 5114]], [[1123, 5116], [1178, 5116], [1178, 5168], [1123, 5168]], [[1189, 5111], [1368, 5100], [1372, 5167], [1194, 5178]], [[74, 5158], [477, 5170], [475, 5212], [73, 5200]], [[72, 5307], [368, 5317], [366, 5367], [71, 5357]], [[1187, 5365], [1245, 5365], [1245, 5425], [1187, 5425]], [[1255, 5356], [1372, 5351], [1375, 5421], [1259, 5426]], [[74, 5401], [392, 5411], [391, 5457], [73, 5447]], [[74, 5550], [383, 5560], [381, 5612], [73, 5602]], [[1054, 5611], [1369, 5611], [1369, 5674], [1054, 5674]], [[72, 5644], [606, 5658], [605, 5708], [71, 5694]], [[73, 5795], [381, 5807], [379, 5862], [71, 5850]], [[1205, 5866], [1371, 5861], [1373, 5928], [1207, 5932]], [[74, 5896], [435, 5906], [434, 5951], [73, 5941]], [[74, 6045], [383, 6056], [381, 6107], [73, 6097]], [[1055, 6106], [1371, 6114], [1369, 6179], [1053, 6171]], [[75, 6147], [602, 6157], [601, 6195], [75, 6186]], [[76, 6296], [379, 6302], [378, 6354], [75, 6347]], [[1199, 6367], [1369, 6362], [1371, 6425], [1201, 6429]], [[74, 6395], [484, 6403], [483, 6447], [73, 6440]], [[70, 6546], [469, 6552], [468, 6604], [69, 6598]], [[1301, 6609], [1376, 6609], [1376, 6675], [1301, 6675]], [[73, 6646], [435, 6650], [435, 6694], [73, 6690]], [[73, 6801], [471, 6801], [471, 6852], [73, 6852]], [[1213, 6856], [1374, 6856], [1374, 6922], [1213, 6922]], [[72, 6891], [465, 6897], [464, 6946], [71, 6940]], [[74, 7040], [387, 7044], [386, 7103], [73, 7099]], [[1257, 7094], [1376, 7089], [1379, 7165], [1260, 7169]], [[69, 7139], [268, 7139], [268, 7198], [69, 7198]]], "text_det_params": {"limit_side_len": 64, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.0, "rec_texts": ["cs.linkingcloud.cn", "*春香**岁", "申请科室：胰腺肿瘤临床研究门诊", "报告日期：2025-6-50:00:00", "项目明细", "项目名称", "结果/单位", "白细胞计数", "参考值：3.5-9.5*10^9/L", "5.7*10^9/L", "淋巴细胞%", "参考值：20.0-50.0%", "29.2%", "单核细胞%", "参考值：3.0-10%", "5.3%", "中性粒细胞%", "63.6%", "参考值：40-75%", "淋巴细胞数", "1.7*10^9/L", "参考值：1.1-3.2*10^9/L", "单核细胞数", "0.3*10^9/L", "参考值：0.1-0.6*10^9/L", "中性粒细胞数", "3.6*10^9/L", "参考值：1.8-6.3*10^9/L", "红细胞计数", "3.34*10^12/L", "↓", "参考值：3.8-5.1*10^12/L", "血红蛋白", "↓91g/l", "参考值：115-150g/l", "红细胞压积", "↑", "28.8%", "参考值：女35-45%", "平均RBC体积", "86.2fl", "参考值：82.0-100fl", "RBC平均HGB", "27.2pg", "参考值：27.0-34.0pg", "平均HGB浓度", "316.0g/l", "参考值：316-354g/l", "血小板计数", "↓", "92*10^9/L", "参考值：125-350*10^9/", "平均PLT容积", "10.3fl", "参考值：7.4-10.4fl", "血小板压积", "↓", "0.09%", "参考值：0.15-0.30%", "PLT分布宽度", "", "11%", "参考值：13-21%", "嗜酸性细胞数", "0.08*10^9/L", "参考值：0.02-0.52*10^9/L", "嗜酸性细胞%", "1.40%", "参考值：0.4-8.0%", "嗜碱性细胞数", "0.03*10^9/L", "参考值：0.00-0.06*10^9/", "嗜碱性细胞%", "0.50%", "参考值：0.00-1.00%", "RBC分布宽度-SD", "41", "参考值：35-44FL", "RBC分布宽度-CV", "13.2%", "参考值：11.6-14.4%", "中性淋巴比值", "2.12", "参考值：."], "rec_scores": [0.9565392732620239, 0.9821309447288513, 0.9970647096633911, 0.9757713079452515, 0.9996131062507629, 0.9998582601547241, 0.9952905774116516, 0.9990628361701965, 0.973552405834198, 0.9760821461677551, 0.9981049299240112, 0.9965600371360779, 0.9990178942680359, 0.9996582269668579, 0.9937005639076233, 0.9983880519866943, 0.9991204142570496, 0.9994452595710754, 0.9957375526428223, 0.9994149208068848, 0.9609792828559875, 0.9655221104621887, 0.9998799562454224, 0.9663599133491516, 0.9597985148429871, 0.9996228814125061, 0.979097843170166, 0.9710801243782043, 0.9996300935745239, 0.9677523970603943, 0.9896626472473145, 0.9776577949523926, 0.9997217059135437, 0.887248694896698, 0.9900680184364319, 0.9997491836547852, 0.6234427094459534, 0.9993522763252258, 0.9889810085296631, 0.9994571805000305, 0.9704738259315491, 0.9841132164001465, 0.9995782971382141, 0.9994305968284607, 0.9940217137336731, 0.9988124966621399, 0.9716633558273315, 0.9521133303642273, 0.9990558624267578, 0.9925164580345154, 0.9613118767738342, 0.9906957149505615, 0.9990779161453247, 0.9931498169898987, 0.9727083444595337, 0.9995768666267395, 0.28956475853919983, 0.9994540214538574, 0.9889479279518127, 0.9986244440078735, 0.0, 0.9992883801460266, 0.9918286204338074, 0.9996352195739746, 0.9769344329833984, 0.9857865571975708, 0.9983434677124023, 0.9987842440605164, 0.9899043440818787, 0.9996230602264404, 0.9760841727256775, 0.9781606793403625, 0.9982520937919617, 0.9987319111824036, 0.9974572062492371, 0.9979889988899231, 0.9996457099914551, 0.9913475513458252, 0.9915422201156616, 0.9997116327285767, 0.9604798555374146, 0.9987213611602783, 0.9998329877853394, 0.9861348271369934], "rec_polys": [[[581, 63], [926, 93], [921, 145], [576, 115]], [[74, 332], [472, 456], [443, 546], [45, 422]], [[71, 527], [870, 646], [852, 760], [53, 641]], [[72, 648], [775, 755], [761, 842], [58, 734]], [[77, 959], [342, 1009], [328, 1081], [63, 1031]], [[73, 1149], [298, 1191], [285, 1257], [60, 1215]], [[1118, 1231], [1360, 1212], [1365, 1276], [1123, 1296]], [[79, 1313], [348, 1362], [336, 1427], [67, 1377]], [[74, 1415], [565, 1481], [557, 1538], [66, 1472]], [[1085, 1426], [1363, 1413], [1366, 1478], [1088, 1490]], [[76, 1568], [338, 1608], [329, 1666], [67, 1627]], [[75, 1668], [493, 1718], [488, 1765], [69, 1716]], [[1197, 1663], [1362, 1653], [1366, 1716], [1201, 1726]], [[76, 1815], [338, 1847], [330, 1904], [69, 1872]], [[76, 1915], [422, 1949], [417, 1999], [71, 1965]], [[1226, 1907], [1362, 1897], [1367, 1959], [1231, 1969]], [[78, 2070], [382, 2096], [377, 2148], [73, 2121]], [[1196, 2155], [1362, 2141], [1367, 2202], [1201, 2216]], [[74, 2166], [417, 2192], [414, 2242], [70, 2215]], [[75, 2320], [339, 2335], [335, 2392], [72, 2378]], [[1100, 2396], [1363, 2385], [1366, 2444], [1102, 2455]], [[76, 2422], [535, 2444], [533, 2488], [74, 2466]], [[76, 2569], [337, 2586], [333, 2643], [72, 2626]], [[1083, 2637], [1365, 2626], [1368, 2689], [1085, 2700]], [[75, 2672], [546, 2692], [544, 2742], [72, 2722]], [[77, 2827], [387, 2843], [384, 2900], [74, 2884]], [[1087, 2887], [1361, 2877], [1364, 2936], [1089, 2946]], [[74, 2930], [548, 2942], [547, 2992], [73, 2980]], [[73, 3081], [336, 3090], [334, 3149], [71, 3140]], [[1034, 3138], [1361, 3123], [1364, 3180], [1037, 3195]], [[966, 3148], [1007, 3148], [1007, 3194], [966, 3194]], [[75, 3187], [557, 3194], [556, 3239], [75, 3231]], [[73, 3341], [283, 3341], [283, 3398], [73, 3398]], [[1151, 3369], [1374, 3369], [1374, 3448], [1151, 3448]], [[75, 3446], [452, 3443], [452, 3487], [75, 3491]], [[73, 3595], [334, 3595], [334, 3647], [73, 3647]], [[1125, 3632], [1176, 3632], [1176, 3684], [1125, 3684]], [[1196, 3627], [1367, 3623], [1369, 3689], [1198, 3694]], [[73, 3695], [464, 3687], [465, 3730], [74, 3738]], [[75, 3842], [382, 3836], [383, 3886], [76, 3892]], [[1218, 3874], [1371, 3869], [1373, 3935], [1220, 3940]], [[75, 3938], [453, 3930], [454, 3974], [76, 3982]], [[71, 4085], [387, 4079], [388, 4131], [72, 4137]], [[1196, 4119], [1373, 4126], [1370, 4195], [1194, 4187]], [[75, 4181], [489, 4181], [489, 4225], [75, 4225]], [[77, 4328], [386, 4328], [386, 4378], [77, 4378]], [[1164, 4359], [1374, 4359], [1374, 4431], [1164, 4431]], [[75, 4420], [467, 4424], [467, 4468], [75, 4464]], [[70, 4564], [334, 4568], [333, 4625], [69, 4621]], [[1028, 4617], [1067, 4617], [1067, 4661], [1028, 4661]], [[1099, 4610], [1367, 4610], [1367, 4667], [1099, 4667]], [[77, 4669], [572, 4669], [572, 4711], [77, 4711]], [[74, 4816], [368, 4826], [366, 4877], [73, 4867]], [[1229, 4859], [1370, 4852], [1373, 4915], [1232, 4922]], [[74, 4911], [433, 4919], [432, 4964], [73, 4956]], [[71, 5057], [334, 5065], [332, 5122], [69, 5114]], [[1123, 5116], [1178, 5116], [1178, 5168], [1123, 5168]], [[1189, 5111], [1368, 5100], [1372, 5167], [1194, 5178]], [[74, 5158], [477, 5170], [475, 5212], [73, 5200]], [[72, 5307], [368, 5317], [366, 5367], [71, 5357]], [[1187, 5365], [1245, 5365], [1245, 5425], [1187, 5425]], [[1255, 5356], [1372, 5351], [1375, 5421], [1259, 5426]], [[74, 5401], [392, 5411], [391, 5457], [73, 5447]], [[74, 5550], [383, 5560], [381, 5612], [73, 5602]], [[1054, 5611], [1369, 5611], [1369, 5674], [1054, 5674]], [[72, 5644], [606, 5658], [605, 5708], [71, 5694]], [[73, 5795], [381, 5807], [379, 5862], [71, 5850]], [[1205, 5866], [1371, 5861], [1373, 5928], [1207, 5932]], [[74, 5896], [435, 5906], [434, 5951], [73, 5941]], [[74, 6045], [383, 6056], [381, 6107], [73, 6097]], [[1055, 6106], [1371, 6114], [1369, 6179], [1053, 6171]], [[75, 6147], [602, 6157], [601, 6195], [75, 6186]], [[76, 6296], [379, 6302], [378, 6354], [75, 6347]], [[1199, 6367], [1369, 6362], [1371, 6425], [1201, 6429]], [[74, 6395], [484, 6403], [483, 6447], [73, 6440]], [[70, 6546], [469, 6552], [468, 6604], [69, 6598]], [[1301, 6609], [1376, 6609], [1376, 6675], [1301, 6675]], [[73, 6646], [435, 6650], [435, 6694], [73, 6690]], [[73, 6801], [471, 6801], [471, 6852], [73, 6852]], [[1213, 6856], [1374, 6856], [1374, 6922], [1213, 6922]], [[72, 6891], [465, 6897], [464, 6946], [71, 6940]], [[74, 7040], [387, 7044], [386, 7103], [73, 7099]], [[1257, 7094], [1376, 7089], [1379, 7165], [1260, 7169]], [[69, 7139], [268, 7139], [268, 7198], [69, 7198]]], "rec_boxes": [[576, 63, 926, 145], [45, 332, 472, 546], [53, 527, 870, 760], [58, 648, 775, 842], [63, 959, 342, 1081], [60, 1149, 298, 1257], [1118, 1212, 1365, 1296], [67, 1313, 348, 1427], [66, 1415, 565, 1538], [1085, 1413, 1366, 1490], [67, 1568, 338, 1666], [69, 1668, 493, 1765], [1197, 1653, 1366, 1726], [69, 1815, 338, 1904], [71, 1915, 422, 1999], [1226, 1897, 1367, 1969], [73, 2070, 382, 2148], [1196, 2141, 1367, 2216], [70, 2166, 417, 2242], [72, 2320, 339, 2392], [1100, 2385, 1366, 2455], [74, 2422, 535, 2488], [72, 2569, 337, 2643], [1083, 2626, 1368, 2700], [72, 2672, 546, 2742], [74, 2827, 387, 2900], [1087, 2877, 1364, 2946], [73, 2930, 548, 2992], [71, 3081, 336, 3149], [1034, 3123, 1364, 3195], [966, 3148, 1007, 3194], [75, 3187, 557, 3239], [73, 3341, 283, 3398], [1151, 3369, 1374, 3448], [75, 3443, 452, 3491], [73, 3595, 334, 3647], [1125, 3632, 1176, 3684], [1196, 3623, 1369, 3694], [73, 3687, 465, 3738], [75, 3836, 383, 3892], [1218, 3869, 1373, 3940], [75, 3930, 454, 3982], [71, 4079, 388, 4137], [1194, 4119, 1373, 4195], [75, 4181, 489, 4225], [77, 4328, 386, 4378], [1164, 4359, 1374, 4431], [75, 4420, 467, 4468], [69, 4564, 334, 4625], [1028, 4617, 1067, 4661], [1099, 4610, 1367, 4667], [77, 4669, 572, 4711], [73, 4816, 368, 4877], [1229, 4852, 1373, 4922], [73, 4911, 433, 4964], [69, 5057, 334, 5122], [1123, 5116, 1178, 5168], [1189, 5100, 1372, 5178], [73, 5158, 477, 5212], [71, 5307, 368, 5367], [1187, 5365, 1245, 5425], [1255, 5351, 1375, 5426], [73, 5401, 392, 5457], [73, 5550, 383, 5612], [1054, 5611, 1369, 5674], [71, 5644, 606, 5708], [71, 5795, 381, 5862], [1205, 5861, 1373, 5932], [73, 5896, 435, 5951], [73, 6045, 383, 6107], [1053, 6106, 1371, 6179], [75, 6147, 602, 6195], [75, 6296, 379, 6354], [1199, 6362, 1371, 6429], [73, 6395, 484, 6447], [69, 6546, 469, 6604], [1301, 6609, 1376, 6675], [73, 6646, 435, 6694], [73, 6801, 471, 6852], [1213, 6856, 1374, 6922], [71, 6891, 465, 6946], [73, 7040, 387, 7103], [1257, 7089, 1379, 7169], [69, 7139, 268, 7198]]}