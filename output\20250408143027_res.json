{"input_path": "D:\\dev\\medicalReport\\ocr\\source\\20250408143027.jpg", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": true}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": true, "use_doc_unwarping": true}, "angle": 0}, "dt_polys": [[[25, 47], [596, 138], [579, 247], [8, 156]], [[21, 241], [140, 260], [128, 332], [10, 313]], [[326, 308], [485, 317], [481, 380], [323, 370]], [[21, 342], [141, 363], [129, 433], [9, 412]], [[328, 417], [813, 429], [812, 479], [327, 467]], [[21, 453], [230, 488], [221, 548], [11, 513]], [[326, 517], [786, 529], [785, 579], [325, 567]], [[17, 640], [134, 659], [125, 721], [8, 703]], [[1117, 693], [1234, 688], [1237, 753], [1120, 758]], [[26, 790], [312, 836], [303, 893], [17, 847]], [[1104, 834], [1236, 826], [1239, 891], [1108, 899]], [[22, 891], [424, 942], [418, 986], [16, 935]], [[963, 934], [1235, 921], [1237, 971], [965, 983]], [[21, 1053], [418, 1099], [411, 1158], [14, 1112]], [[1101, 1084], [1239, 1076], [1243, 1144], [1104, 1152]], [[18, 1153], [425, 1193], [420, 1243], [13, 1203]], [[1056, 1179], [1239, 1172], [1241, 1230], [1058, 1236]], [[17, 1321], [362, 1352], [357, 1415], [11, 1384]], [[1106, 1342], [1239, 1334], [1243, 1403], [1110, 1411]], [[17, 1419], [423, 1449], [419, 1499], [13, 1469]], [[1058, 1441], [1235, 1435], [1237, 1486], [1059, 1493]], [[21, 1584], [358, 1610], [353, 1668], [17, 1641]], [[1137, 1605], [1240, 1596], [1245, 1665], [1143, 1673]], [[18, 1682], [384, 1702], [382, 1746], [16, 1726]], [[1059, 1704], [1236, 1695], [1239, 1749], [1062, 1757]], [[18, 1846], [410, 1867], [407, 1922], [15, 1902]], [[1135, 1861], [1241, 1856], [1245, 1929], [1139, 1935]], [[18, 1941], [368, 1961], [366, 2005], [16, 1985]], [[1062, 1960], [1236, 1960], [1236, 2014], [1062, 2014]], [[18, 2105], [410, 2123], [407, 2181], [16, 2162]], [[1140, 2122], [1244, 2122], [1244, 2193], [1140, 2193]], [[18, 2205], [361, 2219], [359, 2263], [16, 2249]], [[1062, 2222], [1236, 2222], [1236, 2274], [1062, 2274]], [[18, 2368], [459, 2384], [457, 2441], [16, 2425]], [[1105, 2387], [1236, 2387], [1236, 2442], [1105, 2442]], [[14, 2462], [412, 2480], [409, 2530], [12, 2512]], [[965, 2483], [1234, 2477], [1235, 2527], [966, 2533]], [[9, 2624], [410, 2644], [407, 2707], [6, 2687]], [[1118, 2642], [1240, 2642], [1240, 2704], [1118, 2704]], [[9, 2724], [399, 2742], [397, 2792], [7, 2774]], [[964, 2734], [1233, 2734], [1233, 2784], [964, 2784]], [[18, 2894], [410, 2910], [408, 2967], [16, 2951]], [[1055, 2902], [1236, 2902], [1236, 2961], [1055, 2961]], [[10, 2992], [410, 3006], [408, 3050], [9, 3036]], [[963, 3000], [1234, 2992], [1235, 3042], [965, 3050]], [[15, 3160], [462, 3168], [461, 3224], [14, 3216]], [[1101, 3159], [1238, 3154], [1241, 3223], [1104, 3228]], [[13, 3260], [413, 3260], [413, 3305], [13, 3305]], [[965, 3263], [1232, 3254], [1233, 3301], [966, 3309]], [[16, 3427], [464, 3427], [464, 3482], [16, 3482]], [[1105, 3418], [1236, 3413], [1239, 3478], [1107, 3483]], [[11, 3523], [424, 3523], [424, 3573], [11, 3573]], [[963, 3524], [1233, 3509], [1235, 3561], [966, 3575]], [[1117, 3679], [1238, 3673], [1241, 3738], [1120, 3743]], [[1, 3691], [297, 3685], [299, 3742], [3, 3748]], [[949, 3778], [1234, 3772], [1235, 3822], [950, 3828]], [[5, 3789], [405, 3777], [406, 3823], [6, 3835]], [[1144, 3935], [1242, 3935], [1242, 4001], [1144, 4001]], [[10, 3950], [240, 3941], [242, 4001], [12, 4009]], [[9, 4050], [368, 4040], [369, 4086], [10, 4096]], [[1036, 4041], [1234, 4030], [1237, 4083], [1038, 4094]], [[0, 4212], [294, 4204], [295, 4259], [0, 4268]], [[1073, 4205], [1235, 4194], [1239, 4255], [1077, 4266]], [[1, 4306], [466, 4294], [468, 4344], [3, 4356]], [[1016, 4301], [1237, 4290], [1239, 4342], [1018, 4353]], [[7, 4469], [407, 4461], [408, 4520], [8, 4528]], [[1115, 4460], [1241, 4453], [1245, 4516], [1119, 4523]], [[0, 4563], [434, 4555], [435, 4605], [1, 4613]], [[1077, 4560], [1243, 4547], [1247, 4600], [1081, 4614]], [[11, 4723], [460, 4723], [460, 4779], [11, 4779]], [[1114, 4723], [1245, 4713], [1251, 4781], [1120, 4791]], [[4, 4825], [409, 4825], [409, 4869], [4, 4869]], [[1053, 4821], [1242, 4821], [1242, 4875], [1053, 4875]], [[4, 4989], [457, 4995], [456, 5045], [3, 5039]], [[1081, 4990], [1245, 4982], [1248, 5052], [1084, 5061]], [[2, 5080], [384, 5088], [383, 5132], [2, 5124]], [[1038, 5085], [1244, 5085], [1244, 5145], [1038, 5145]], [[0, 5240], [408, 5252], [406, 5309], [0, 5297]], [[1124, 5257], [1250, 5247], [1255, 5317], [1129, 5327]], [[0, 5338], [382, 5346], [381, 5392], [0, 5384]], [[1065, 5354], [1243, 5345], [1246, 5404], [1067, 5413]], [[12, 5499], [297, 5507], [295, 5566], [10, 5558]], [[1119, 5521], [1244, 5513], [1249, 5583], [1123, 5591]], [[0, 5597], [384, 5603], [383, 5653], [0, 5647]], [[967, 5620], [1239, 5613], [1240, 5665], [968, 5671]], [[12, 5761], [408, 5771], [406, 5829], [10, 5818]], [[1118, 5779], [1245, 5771], [1249, 5842], [1123, 5850]], [[0, 5859], [395, 5863], [394, 5908], [0, 5904]], [[1077, 5882], [1243, 5869], [1247, 5923], [1081, 5936]], [[3, 6022], [408, 6032], [406, 6087], [1, 6077]], [[1130, 6043], [1241, 6037], [1245, 6104], [1134, 6110]], [[0, 6118], [375, 6126], [374, 6170], [0, 6162]], [[1075, 6145], [1241, 6134], [1245, 6187], [1079, 6198]], [[11, 6278], [299, 6291], [297, 6348], [8, 6335]], [[1108, 6311], [1239, 6304], [1243, 6367], [1112, 6374]], [[0, 6375], [397, 6383], [396, 6432], [0, 6424]], [[1060, 6405], [1237, 6400], [1239, 6452], [1061, 6457]], [[0, 6537], [246, 6541], [245, 6601], [0, 6596]], [[1127, 6561], [1240, 6561], [1240, 6633], [1127, 6633]], [[0, 6633], [262, 6633], [262, 6683], [0, 6683]], [[1067, 6661], [1240, 6661], [1240, 6715], [1067, 6715]], [[10, 6792], [531, 6802], [530, 6865], [9, 6855]], [[1111, 6818], [1240, 6818], [1240, 6888], [1111, 6888]], [[0, 6888], [265, 6888], [265, 6938], [0, 6938]], [[1064, 6909], [1240, 6909], [1240, 6968], [1064, 6968]], [[0, 7047], [136, 7047], [136, 7116], [0, 7116]], [[1089, 7067], [1241, 7072], [1238, 7148], [1087, 7143]], [[0, 7150], [322, 7137], [324, 7196], [0, 7209]], [[978, 7153], [1236, 7174], [1231, 7233], [973, 7212]]], "text_det_params": {"limit_side_len": 64, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.0, "rec_texts": ["血沉,病房血常规+血型", "姓名", "李春香", "单号", "20250407G62244717", "报告时间", "2025-04-0717:13:33", "项目", "结果", "白细胞计数", "6.58", "参考范围：3.50-9.50", "单位：10^9/L", "中性粒细胞比率", "60.8", "参考范围：40.0-75.0", "单位：%", "淋巴细胞比率", "28.3", "参考范围：20.0-50.0", "单位：%", "单核细胞比率", "9.7", "参考范围：3.0-10.0", "单位：%", "嗜酸性细胞比率", "0.9", "参考范围：0.4-8.0", "单位：%", "嗜碱性细胞比率", "0.3", "参考范围：0.0-1.0", "单位：%", "中性粒细胞绝对值", "4.00", "参考范围：1.80-6.30", "单位：10^9/L", "淋巴细胞绝对值", "1.86", "参考范围：1.10-3.20", "单位：10^9/L", "单核细胞绝对值", " 0.64", "参考范围：0.10-0.60", "单位：10^9/L", "嗜酸性细胞绝对值", "0.06", "参考范围：0.02-0.52", "单位：10^9/L", "嗜碱性细胞绝对值", "0.02", "参考范围：0.00-0.06", "单位：10^9/L", "4.21", "红细胞计数", "单位：10^12/L", "参考范围：3.80-5.10", "116", "血红蛋白", "参考范围：115-150", "单位：g/L", "红细胞压积", "0.369", "参考范围：0.350-0.450", "单位：ratio", "平均红细胞体积", "87.6", "参考范围：82.0-100.0", "单位：fl", "平均血红蛋白含量", "27.6", "参考范围：27.0-34.0", "单位：pg", "平均血红蛋白浓度", "↓314", "参考范围：316-354", "单位：g/L", "红细胞分布宽度", "12.7", "参考范围：0.0-15.0", "单位：%", "血小板计数", "205", "参考范围：125-350", "单位：10^9/L", "血小板分布宽度", "12.3", "参考范围：10.0-17.0", "单位：fl", "平均血小板体积", "11.2", "参考范围：7.8-12.5", "单位：fl", "血小板压积", "0.23", "参考范围：0.11-0.28", "单位：%", "ABO血型", "B型", "参考范围：--", "单位：--", "RhD血型（RhD抗原）", "阳性", "参考范围：--", "单位：--", "血沉", "40", "参考范围：0-20", "单位：mm/H"], "rec_scores": [0.9713605642318726, 0.9998537302017212, 0.8787646293640137, 0.999942421913147, 0.998331606388092, 0.9994613528251648, 0.9893283843994141, 0.9998544454574585, 0.9998311996459961, 0.9988633990287781, 0.9994539022445679, 0.9825387001037598, 0.9438921809196472, 0.9994221329689026, 0.9997944831848145, 0.9969210028648376, 0.9872549772262573, 0.9958491325378418, 0.9997938871383667, 0.9974238276481628, 0.9943777322769165, 0.9988039135932922, 0.9997682571411133, 0.9980428218841553, 0.9909111261367798, 0.9985259771347046, 0.9998550415039062, 0.995066225528717, 0.9885904788970947, 0.9956244826316833, 0.9996383786201477, 0.9932491779327393, 0.9918999671936035, 0.9980524778366089, 0.9991006255149841, 0.9978395700454712, 0.9409151077270508, 0.9958624243736267, 0.9996520280838013, 0.9983060956001282, 0.9643194675445557, 0.9988988637924194, 0.8850160837173462, 0.9978934526443481, 0.9453505277633667, 0.9991344213485718, 0.9997258186340332, 0.9915939569473267, 0.9185391664505005, 0.9988195896148682, 0.9995612502098083, 0.997674286365509, 0.9647219777107239, 0.9995622634887695, 0.998502254486084, 0.9326618909835815, 0.9662671685218811, 0.9999265670776367, 0.99381023645401, 0.9907005429267883, 0.9847822785377502, 0.9988174438476562, 0.9993794560432434, 0.9976885318756104, 0.9943873286247253, 0.9984559416770935, 0.9991996884346008, 0.9980784058570862, 0.924942135810852, 0.9959735870361328, 0.999731719493866, 0.9916092753410339, 0.9917054176330566, 0.9974247813224792, 0.9670161008834839, 0.9795998930931091, 0.9831930994987488, 0.9977613091468811, 0.9998171925544739, 0.9971933364868164, 0.9911201000213623, 0.9625058174133301, 0.9999249577522278, 0.9974767565727234, 0.9326742887496948, 0.975188136100769, 0.9997972249984741, 0.9876865148544312, 0.9361494779586792, 0.9987407326698303, 0.9998370409011841, 0.9911251664161682, 0.964158833026886, 0.9966182708740234, 0.9996037483215332, 0.9981113076210022, 0.9917032122612, 0.9236223101615906, 0.9997704029083252, 0.9708203077316284, 0.9507012367248535, 0.9530187249183655, 0.9996641278266907, 0.9782023429870605, 0.9822394251823425, 0.9985329508781433, 0.9993152618408203, 0.9989162087440491, 0.9849096536636353], "rec_polys": [[[25, 47], [596, 138], [579, 247], [8, 156]], [[21, 241], [140, 260], [128, 332], [10, 313]], [[326, 308], [485, 317], [481, 380], [323, 370]], [[21, 342], [141, 363], [129, 433], [9, 412]], [[328, 417], [813, 429], [812, 479], [327, 467]], [[21, 453], [230, 488], [221, 548], [11, 513]], [[326, 517], [786, 529], [785, 579], [325, 567]], [[17, 640], [134, 659], [125, 721], [8, 703]], [[1117, 693], [1234, 688], [1237, 753], [1120, 758]], [[26, 790], [312, 836], [303, 893], [17, 847]], [[1104, 834], [1236, 826], [1239, 891], [1108, 899]], [[22, 891], [424, 942], [418, 986], [16, 935]], [[963, 934], [1235, 921], [1237, 971], [965, 983]], [[21, 1053], [418, 1099], [411, 1158], [14, 1112]], [[1101, 1084], [1239, 1076], [1243, 1144], [1104, 1152]], [[18, 1153], [425, 1193], [420, 1243], [13, 1203]], [[1056, 1179], [1239, 1172], [1241, 1230], [1058, 1236]], [[17, 1321], [362, 1352], [357, 1415], [11, 1384]], [[1106, 1342], [1239, 1334], [1243, 1403], [1110, 1411]], [[17, 1419], [423, 1449], [419, 1499], [13, 1469]], [[1058, 1441], [1235, 1435], [1237, 1486], [1059, 1493]], [[21, 1584], [358, 1610], [353, 1668], [17, 1641]], [[1137, 1605], [1240, 1596], [1245, 1665], [1143, 1673]], [[18, 1682], [384, 1702], [382, 1746], [16, 1726]], [[1059, 1704], [1236, 1695], [1239, 1749], [1062, 1757]], [[18, 1846], [410, 1867], [407, 1922], [15, 1902]], [[1135, 1861], [1241, 1856], [1245, 1929], [1139, 1935]], [[18, 1941], [368, 1961], [366, 2005], [16, 1985]], [[1062, 1960], [1236, 1960], [1236, 2014], [1062, 2014]], [[18, 2105], [410, 2123], [407, 2181], [16, 2162]], [[1140, 2122], [1244, 2122], [1244, 2193], [1140, 2193]], [[18, 2205], [361, 2219], [359, 2263], [16, 2249]], [[1062, 2222], [1236, 2222], [1236, 2274], [1062, 2274]], [[18, 2368], [459, 2384], [457, 2441], [16, 2425]], [[1105, 2387], [1236, 2387], [1236, 2442], [1105, 2442]], [[14, 2462], [412, 2480], [409, 2530], [12, 2512]], [[965, 2483], [1234, 2477], [1235, 2527], [966, 2533]], [[9, 2624], [410, 2644], [407, 2707], [6, 2687]], [[1118, 2642], [1240, 2642], [1240, 2704], [1118, 2704]], [[9, 2724], [399, 2742], [397, 2792], [7, 2774]], [[964, 2734], [1233, 2734], [1233, 2784], [964, 2784]], [[18, 2894], [410, 2910], [408, 2967], [16, 2951]], [[1055, 2902], [1236, 2902], [1236, 2961], [1055, 2961]], [[10, 2992], [410, 3006], [408, 3050], [9, 3036]], [[963, 3000], [1234, 2992], [1235, 3042], [965, 3050]], [[15, 3160], [462, 3168], [461, 3224], [14, 3216]], [[1101, 3159], [1238, 3154], [1241, 3223], [1104, 3228]], [[13, 3260], [413, 3260], [413, 3305], [13, 3305]], [[965, 3263], [1232, 3254], [1233, 3301], [966, 3309]], [[16, 3427], [464, 3427], [464, 3482], [16, 3482]], [[1105, 3418], [1236, 3413], [1239, 3478], [1107, 3483]], [[11, 3523], [424, 3523], [424, 3573], [11, 3573]], [[963, 3524], [1233, 3509], [1235, 3561], [966, 3575]], [[1117, 3679], [1238, 3673], [1241, 3738], [1120, 3743]], [[1, 3691], [297, 3685], [299, 3742], [3, 3748]], [[949, 3778], [1234, 3772], [1235, 3822], [950, 3828]], [[5, 3789], [405, 3777], [406, 3823], [6, 3835]], [[1144, 3935], [1242, 3935], [1242, 4001], [1144, 4001]], [[10, 3950], [240, 3941], [242, 4001], [12, 4009]], [[9, 4050], [368, 4040], [369, 4086], [10, 4096]], [[1036, 4041], [1234, 4030], [1237, 4083], [1038, 4094]], [[0, 4212], [294, 4204], [295, 4259], [0, 4268]], [[1073, 4205], [1235, 4194], [1239, 4255], [1077, 4266]], [[1, 4306], [466, 4294], [468, 4344], [3, 4356]], [[1016, 4301], [1237, 4290], [1239, 4342], [1018, 4353]], [[7, 4469], [407, 4461], [408, 4520], [8, 4528]], [[1115, 4460], [1241, 4453], [1245, 4516], [1119, 4523]], [[0, 4563], [434, 4555], [435, 4605], [1, 4613]], [[1077, 4560], [1243, 4547], [1247, 4600], [1081, 4614]], [[11, 4723], [460, 4723], [460, 4779], [11, 4779]], [[1114, 4723], [1245, 4713], [1251, 4781], [1120, 4791]], [[4, 4825], [409, 4825], [409, 4869], [4, 4869]], [[1053, 4821], [1242, 4821], [1242, 4875], [1053, 4875]], [[4, 4989], [457, 4995], [456, 5045], [3, 5039]], [[1081, 4990], [1245, 4982], [1248, 5052], [1084, 5061]], [[2, 5080], [384, 5088], [383, 5132], [2, 5124]], [[1038, 5085], [1244, 5085], [1244, 5145], [1038, 5145]], [[0, 5240], [408, 5252], [406, 5309], [0, 5297]], [[1124, 5257], [1250, 5247], [1255, 5317], [1129, 5327]], [[0, 5338], [382, 5346], [381, 5392], [0, 5384]], [[1065, 5354], [1243, 5345], [1246, 5404], [1067, 5413]], [[12, 5499], [297, 5507], [295, 5566], [10, 5558]], [[1119, 5521], [1244, 5513], [1249, 5583], [1123, 5591]], [[0, 5597], [384, 5603], [383, 5653], [0, 5647]], [[967, 5620], [1239, 5613], [1240, 5665], [968, 5671]], [[12, 5761], [408, 5771], [406, 5829], [10, 5818]], [[1118, 5779], [1245, 5771], [1249, 5842], [1123, 5850]], [[0, 5859], [395, 5863], [394, 5908], [0, 5904]], [[1077, 5882], [1243, 5869], [1247, 5923], [1081, 5936]], [[3, 6022], [408, 6032], [406, 6087], [1, 6077]], [[1130, 6043], [1241, 6037], [1245, 6104], [1134, 6110]], [[0, 6118], [375, 6126], [374, 6170], [0, 6162]], [[1075, 6145], [1241, 6134], [1245, 6187], [1079, 6198]], [[11, 6278], [299, 6291], [297, 6348], [8, 6335]], [[1108, 6311], [1239, 6304], [1243, 6367], [1112, 6374]], [[0, 6375], [397, 6383], [396, 6432], [0, 6424]], [[1060, 6405], [1237, 6400], [1239, 6452], [1061, 6457]], [[0, 6537], [246, 6541], [245, 6601], [0, 6596]], [[1127, 6561], [1240, 6561], [1240, 6633], [1127, 6633]], [[0, 6633], [262, 6633], [262, 6683], [0, 6683]], [[1067, 6661], [1240, 6661], [1240, 6715], [1067, 6715]], [[10, 6792], [531, 6802], [530, 6865], [9, 6855]], [[1111, 6818], [1240, 6818], [1240, 6888], [1111, 6888]], [[0, 6888], [265, 6888], [265, 6938], [0, 6938]], [[1064, 6909], [1240, 6909], [1240, 6968], [1064, 6968]], [[0, 7047], [136, 7047], [136, 7116], [0, 7116]], [[1089, 7067], [1241, 7072], [1238, 7148], [1087, 7143]], [[0, 7150], [322, 7137], [324, 7196], [0, 7209]], [[978, 7153], [1236, 7174], [1231, 7233], [973, 7212]]], "rec_boxes": [[8, 47, 596, 247], [10, 241, 140, 332], [323, 308, 485, 380], [9, 342, 141, 433], [327, 417, 813, 479], [11, 453, 230, 548], [325, 517, 786, 579], [8, 640, 134, 721], [1117, 688, 1237, 758], [17, 790, 312, 893], [1104, 826, 1239, 899], [16, 891, 424, 986], [963, 921, 1237, 983], [14, 1053, 418, 1158], [1101, 1076, 1243, 1152], [13, 1153, 425, 1243], [1056, 1172, 1241, 1236], [11, 1321, 362, 1415], [1106, 1334, 1243, 1411], [13, 1419, 423, 1499], [1058, 1435, 1237, 1493], [17, 1584, 358, 1668], [1137, 1596, 1245, 1673], [16, 1682, 384, 1746], [1059, 1695, 1239, 1757], [15, 1846, 410, 1922], [1135, 1856, 1245, 1935], [16, 1941, 368, 2005], [1062, 1960, 1236, 2014], [16, 2105, 410, 2181], [1140, 2122, 1244, 2193], [16, 2205, 361, 2263], [1062, 2222, 1236, 2274], [16, 2368, 459, 2441], [1105, 2387, 1236, 2442], [12, 2462, 412, 2530], [965, 2477, 1235, 2533], [6, 2624, 410, 2707], [1118, 2642, 1240, 2704], [7, 2724, 399, 2792], [964, 2734, 1233, 2784], [16, 2894, 410, 2967], [1055, 2902, 1236, 2961], [9, 2992, 410, 3050], [963, 2992, 1235, 3050], [14, 3160, 462, 3224], [1101, 3154, 1241, 3228], [13, 3260, 413, 3305], [965, 3254, 1233, 3309], [16, 3427, 464, 3482], [1105, 3413, 1239, 3483], [11, 3523, 424, 3573], [963, 3509, 1235, 3575], [1117, 3673, 1241, 3743], [1, 3685, 299, 3748], [949, 3772, 1235, 3828], [5, 3777, 406, 3835], [1144, 3935, 1242, 4001], [10, 3941, 242, 4009], [9, 4040, 369, 4096], [1036, 4030, 1237, 4094], [0, 4204, 295, 4268], [1073, 4194, 1239, 4266], [1, 4294, 468, 4356], [1016, 4290, 1239, 4353], [7, 4461, 408, 4528], [1115, 4453, 1245, 4523], [0, 4555, 435, 4613], [1077, 4547, 1247, 4614], [11, 4723, 460, 4779], [1114, 4713, 1251, 4791], [4, 4825, 409, 4869], [1053, 4821, 1242, 4875], [3, 4989, 457, 5045], [1081, 4982, 1248, 5061], [2, 5080, 384, 5132], [1038, 5085, 1244, 5145], [0, 5240, 408, 5309], [1124, 5247, 1255, 5327], [0, 5338, 382, 5392], [1065, 5345, 1246, 5413], [10, 5499, 297, 5566], [1119, 5513, 1249, 5591], [0, 5597, 384, 5653], [967, 5613, 1240, 5671], [10, 5761, 408, 5829], [1118, 5771, 1249, 5850], [0, 5859, 395, 5908], [1077, 5869, 1247, 5936], [1, 6022, 408, 6087], [1130, 6037, 1245, 6110], [0, 6118, 375, 6170], [1075, 6134, 1245, 6198], [8, 6278, 299, 6348], [1108, 6304, 1243, 6374], [0, 6375, 397, 6432], [1060, 6400, 1239, 6457], [0, 6537, 246, 6601], [1127, 6561, 1240, 6633], [0, 6633, 262, 6683], [1067, 6661, 1240, 6715], [9, 6792, 531, 6865], [1111, 6818, 1240, 6888], [0, 6888, 265, 6938], [1064, 6909, 1240, 6968], [0, 7047, 136, 7116], [1087, 7067, 1241, 7148], [0, 7137, 324, 7209], [973, 7153, 1236, 7233]]}